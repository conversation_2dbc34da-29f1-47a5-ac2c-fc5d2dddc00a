#ifndef SL_PMU_COMMON_TYPES_HPP_
#define SL_PMU_COMMON_TYPES_HPP_

#include <functional>
#include <memory>
#include <chrono>
#include <cstdint>
#include <string>
#include <mutex>
#include <condition_variable>
#include <algorithm>

namespace sl_pmu {
namespace common {

// Forward declarations
struct AsyncResult;
struct TimeoutConfig;

// Common result types
enum class ResultCode {
    SUCCESS = 0,
    TIMEOUT,
    COMMUNICATION_ERROR,
    INVALID_PARAMETER,
    DEVICE_NOT_READY,
    PROTOCOL_ERROR,
    UNKNOWN_ERROR
};

// Async operation result
struct AsyncResult {
    ResultCode code;
    std::string message;
    uint32_t data;
    
    AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = "", uint32_t d = 0)
        : code(c), message(msg), data(d) {}
    
    bool isSuccess() const { return code == ResultCode::SUCCESS; }
};

// Callback types
using AsyncCallback = std::function<void(const AsyncResult& result)>;
using TimeoutCallback = std::function<void()>;

// Timeout configuration
struct TimeoutConfig {
    std::chrono::milliseconds timeout{1000};
    TimeoutCallback callback;
    
    TimeoutConfig(std::chrono::milliseconds t = std::chrono::milliseconds(1000))
        : timeout(t) {}
};

// Motor control data structures
struct MotorSpeeds {
    double left_rpm;
    double right_rpm;
    
    MotorSpeeds(double l = 0.0, double r = 0.0) : left_rpm(l), right_rpm(r) {}
};

struct MotorPositions {
    int32_t left_encoder;
    int32_t right_encoder;
    
    MotorPositions(int32_t l = 0, int32_t r = 0) : left_encoder(l), right_encoder(r) {}
};

struct MotorCurrents {
    int16_t left_current;   // in 0.1A units
    int16_t right_current;  // in 0.1A units
    
    MotorCurrents(int16_t l = 0, int16_t r = 0) : left_current(l), right_current(r) {}
};

struct MotorTemperatures {
    int16_t left_temp;      // in 0.1°C units
    int16_t right_temp;     // in 0.1°C units
    int16_t driver_temp;    // in 0.1°C units
    
    MotorTemperatures(int16_t l = 0, int16_t r = 0, int16_t d = 0) 
        : left_temp(l), right_temp(r), driver_temp(d) {}
};

struct GpioStatus {
    uint32_t status_bits;
    
    GpioStatus(uint32_t bits = 0) : status_bits(bits) {}
    
    bool isEmergencyStop() const { return (status_bits & 0x01) != 0; }
    bool isBrakeReleased() const { return (status_bits & 0x04) != 0; }
    bool isPowerOffPressed() const { return (status_bits & 0x08) != 0; }
};

// Power control data structures
struct PowerStatus {
    bool power_off_requested;
    std::chrono::steady_clock::time_point request_time;
    
    PowerStatus(bool req = false) : power_off_requested(req) {
        if (req) {
            request_time = std::chrono::steady_clock::now();
        }
    }
};

// Robot physical parameters
struct RobotParameters {
    double wheel_diameter_left;
    double wheel_diameter_right;
    double wheel_separation;
    double gear_ratio;
    double encoder_resolution;
    
    RobotParameters() 
        : wheel_diameter_left(0.140)
        , wheel_diameter_right(0.140)
        , wheel_separation(0.390)
        , gear_ratio(1.0)
        , encoder_resolution(16384.0) {}
};

// Communication frame structure
struct CommFrame {
    uint32_t id;
    uint8_t data[8];
    uint8_t length;
    bool is_extended;
    bool is_rtr;
    
    CommFrame() : id(0), length(0), is_extended(false), is_rtr(false) {
        std::fill(data, data + 8, 0);
    }
};

// Response waiting structure for sync/async operations
struct ResponseWaiter {
    std::mutex mutex;
    std::condition_variable cv;
    bool response_received;
    AsyncResult result;
    
    ResponseWaiter() : response_received(false) {}
    
    // Wait for response with timeout
    bool waitForResponse(std::chrono::milliseconds timeout) {
        std::unique_lock<std::mutex> lock(mutex);
        return cv.wait_for(lock, timeout, [this] { return response_received; });
    }
    
    // Signal response received
    void signalResponse(const AsyncResult& res) {
        std::lock_guard<std::mutex> lock(mutex);
        result = res;
        response_received = true;
        cv.notify_one();
    }
};

} // namespace common
} // namespace sl_pmu

#endif // SL_PMU_COMMON_TYPES_HPP_
