#ifndef SL_PMU_DEVICE_DEVICE_INTERFACE_HPP_
#define SL_PMU_DEVICE_DEVICE_INTERFACE_HPP_

#include "sl_pmu/common/types.hpp"
#include "sl_pmu/protocol/communication_protocol.hpp"
#include <memory>
#include <functional>

namespace sl_pmu {
namespace device {

/**
 * @brief Abstract base class for motor controllers
 */
class IMotorController {
public:
    virtual ~IMotorController() = default;

    /**
     * @brief Initialize the motor controller
     * @param protocol Communication protocol to use
     * @param params Robot physical parameters
     * @return true if initialization successful, false otherwise
     */
    virtual bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                           const common::RobotParameters& params) = 0;

    /**
     * @brief Shutdown the motor controller
     */
    virtual void shutdown() = 0;

    /**
     * @brief Check if the motor controller is ready
     * @return true if ready, false otherwise
     */
    virtual bool isReady() const = 0;

    /**
     * @brief Enable motors
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void enableMotors(common::AsyncCallback callback,
                             const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Disable motors
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void disableMotors(common::AsyncCallback callback,
                              const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Set motor speeds
     * @param speeds Motor speeds in RPM
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void setMotorSpeeds(const common::MotorSpeeds& speeds,
                               common::AsyncCallback callback,
                               const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read motor positions
     * @param callback Callback to call when operation completes (data contains positions)
     * @param timeout_config Timeout configuration
     */
    virtual void readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                                   const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read motor speeds
     * @param callback Callback to call when operation completes (data contains speeds)
     * @param timeout_config Timeout configuration
     */
    virtual void readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                                const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read motor currents
     * @param callback Callback to call when operation completes (data contains currents)
     * @param timeout_config Timeout configuration
     */
    virtual void readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                                  const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read motor temperatures
     * @param callback Callback to call when operation completes (data contains temperatures)
     * @param timeout_config Timeout configuration
     */
    virtual void readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                                      const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read GPIO status
     * @param callback Callback to call when operation completes (data contains GPIO status)
     * @param timeout_config Timeout configuration
     */
    virtual void readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                               const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Read last alarm code
     * @param callback Callback to call when operation completes (data contains alarm code)
     * @param timeout_config Timeout configuration
     */
    virtual void readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                              const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Clear alarm
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void clearAlarm(common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Get device type identifier
     * @return String identifying the device type
     */
    virtual std::string getDeviceType() const = 0;

    /**
     * @brief Check if motors are enabled
     * @return true if motors are enabled, false otherwise
     */
    virtual bool areMotorsEnabled() const = 0;
};

/**
 * @brief Abstract base class for power controllers
 */
class IPowerController {
public:
    virtual ~IPowerController() = default;

    /**
     * @brief Initialize the power controller
     * @param protocol Communication protocol to use
     * @return true if initialization successful, false otherwise
     */
    virtual bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) = 0;

    /**
     * @brief Shutdown the power controller
     */
    virtual void shutdown() = 0;

    /**
     * @brief Check if the power controller is ready
     * @return true if ready, false otherwise
     */
    virtual bool isReady() const = 0;

    /**
     * @brief Control power off button light
     * @param enable true to turn on light, false to turn off
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void controlPowerButtonLight(bool enable,
                                        common::AsyncCallback callback,
                                        const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Control main power
     * @param enable true to turn on power, false to turn off
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void controlMainPower(bool enable,
                                 common::AsyncCallback callback,
                                 const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Get device type identifier
     * @return String identifying the device type
     */
    virtual std::string getDeviceType() const = 0;
};

} // namespace device
} // namespace sl_pmu

#endif // SL_PMU_DEVICE_DEVICE_INTERFACE_HPP_
