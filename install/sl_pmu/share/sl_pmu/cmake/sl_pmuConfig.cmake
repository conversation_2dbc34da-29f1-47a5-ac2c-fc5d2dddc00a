# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_sl_pmu_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED sl_pmu_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(sl_pmu_FOUND FALSE)
  elseif(NOT sl_pmu_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(sl_pmu_FOUND FALSE)
  endif()
  return()
endif()
set(_sl_pmu_CONFIG_INCLUDED TRUE)

# output package information
if(NOT sl_pmu_FIND_QUIETLY)
  message(STATUS "Found sl_pmu: 1.0.0 (${sl_pmu_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'sl_pmu' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT sl_pmu_DEPRECATED_QUIET)
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(sl_pmu_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${sl_pmu_DIR}/${_extra}")
endforeach()
