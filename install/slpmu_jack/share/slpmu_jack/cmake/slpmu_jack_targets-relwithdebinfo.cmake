#----------------------------------------------------------------
# Generated CMake target import file for configuration "RelWithDebInfo".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "slpmu_jack" for configuration "RelWithDebInfo"
set_property(TARGET slpmu_jack APPEND PROPERTY IMPORTED_CONFIGURATIONS RELWITHDEBINFO)
set_target_properties(slpmu_jack PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELWITHDEBINFO "CXX"
  IMPORTED_LOCATION_RELWITHDEBINFO "${_IMPORT_PREFIX}/lib/libslpmu_jack.a"
  )

list(APPEND _cmake_import_check_targets slpmu_jack )
list(APPEND _cmake_import_check_files_for_slpmu_jack "${_IMPORT_PREFIX}/lib/libslpmu_jack.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
