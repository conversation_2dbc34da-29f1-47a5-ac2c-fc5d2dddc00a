#pragma once

#include <cstdint>
#include <optional>

namespace slpmu {

class IJackController {
public:
    template<typename T>
    struct TimestampedValue {
        bool success;
        T value;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

public:
    virtual ~IJackController() = default;

    virtual bool open() = 0;
    virtual bool close() = 0;
    virtual bool is_open() const = 0;

    // Commands
    virtual bool cmd_brake() = 0;
    virtual bool cmd_clear_alarm() = 0;
    virtual bool cmd_detect_base() = 0;
    virtual bool cmd_auto_pos_change() = 0;

    virtual bool set_mode_detect_base() = 0;
    virtual bool set_mode_position() = 0;

    virtual bool write_target_speed(std::uint32_t can_speed_value) = 0;
    virtual bool write_target_position(std::int32_t pos) = 0;

    // Reads
    virtual TimestampedValue<std::int32_t> read_actual_position() = 0;
    virtual TimestampedValue<std::uint16_t> read_status() = 0;
    virtual TimestampedValue<std::uint16_t> read_alarm() = 0;
};

}

