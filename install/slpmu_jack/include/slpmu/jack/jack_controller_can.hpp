#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <optional>
#include <slpmu/jack/i_jack_controller.hpp>

namespace slcan { class CANopen; }

namespace slpmu {

class JackControllerCan : public IJackController {
public:
    struct Config {
        std::string can_interface {"can0"};
        std::uint16_t node_id {0x07};
        std::int64_t sdo_timeout_ns {200'000'000};
    };

    JackControllerCan();
    explicit JackControllerCan(const Config& cfg);
    ~JackControllerCan();

    bool open() override;
    bool close() override;
    bool is_open() const override { return open_; }

    // Commands
    bool cmd_brake() override;
    bool cmd_detect_base() override;
    bool cmd_auto_pos_change() override;
    bool cmd_clear_alarm() override;
    bool set_mode_detect_base() override;
    bool set_mode_position() override;

    bool write_target_speed(std::uint32_t can_speed_value) override;
    bool write_target_position(std::int32_t pos) override;

    // Reads
    TimestampedValue<std::int32_t> read_actual_position() override;
    TimestampedValue<std::uint16_t> read_status() override;
    TimestampedValue<std::uint16_t> read_alarm() override;

    // Helpers
    static std::uint32_t rpm_to_can_speed(std::uint32_t rpm);

private:
    template<typename T>
    std::optional<T> sdo_read(std::uint16_t index, std::uint8_t sub);

private:
    Config cfg_;
    std::unique_ptr<slcan::CANopen> canopen_;
    bool open_ {false};
};

}

