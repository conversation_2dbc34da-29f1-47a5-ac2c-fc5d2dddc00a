/**
 * socket_can.hpp
 * Low level SocketCAN interface
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include <linux/can.h>

#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace slcan {

class SocketCan {
public:
    enum class CanDeviceFlags: std::uint64_t {
        Timestamp = 1 << 0,
        HardwareTimestamp = 1 << 1,
        None = 0,
        All = Timestamp | HardwareTimestamp,
    };

    struct Config {
        std::string iface;
        CanDeviceFlags flags;
        std::vector<struct can_filter> filters;

        static inline Config defaultConfig() {
            return {
                "can0",
                kDefaultFlags,
                {},
            };
        }
    };

    static constexpr CanDeviceFlags kDefaultFlags = CanDeviceFlags::All;

    struct ReceiveFrame {
        struct can_frame frame;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

public:
    explicit SocketCan(const Config& config = Config::defaultConfig());
    ~SocketCan();

    SocketCan(const SocketCan&) = delete;
    SocketCan& operator=(const SocketCan&) = delete;

    SocketCan(SocketCan&&) = delete;
    SocketCan& operator=(SocketCan&&) = delete;

public:
    bool open();
    bool close();
    bool send_frame(const struct can_frame& frame);
    bool recv_frame(ReceiveFrame& frame, std::int64_t timeout_ns = -1);

private:
    Config config_;
    int socket_fd_ {-1};
};

}
