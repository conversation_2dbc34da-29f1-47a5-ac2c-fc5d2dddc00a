[0.015s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
[0.114s] -- The C compiler identification is GNU 13.3.0
[0.186s] -- The CXX compiler identification is GNU 13.3.0
[0.211s] -- Detecting C compiler ABI info
[0.317s] -- Detecting C compiler ABI info - done
[0.329s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.330s] -- Detecting C compile features
[0.330s] -- Detecting C compile features - done
[0.343s] -- Detecting CXX compiler ABI info
[0.476s] -- Detecting CXX compiler ABI info - done
[0.489s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.489s] -- Detecting CXX compile features
[0.489s] -- Detecting CXX compile features - done
[0.498s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.689s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.870s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.976s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[1.005s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[1.040s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.077s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.219s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[1.227s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[1.354s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
[1.385s] -- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") 
[1.414s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.545s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[1.573s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[1.602s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[1.653s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[1.701s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[1.712s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[1.934s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[1.965s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[1.967s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.968s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.971s] -- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)
[2.005s] -- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
[2.007s] -- Checking for module 'libmodbus'
[2.024s] --   Found libmodbus, version 3.1.10
[2.094s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[2.100s] -- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)
[2.254s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.259s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.260s] -- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include
[2.260s] -- Configured cppcheck exclude dirs and/or files: 
[2.265s] -- Added test 'cpplint' to check C / C++ code against the Google style
[2.265s] -- Configured cpplint exclude dirs and/or files: 
[2.267s] -- Added test 'flake8' to check Python code syntax and style conventions
[2.267s] -- Configured 'flake8' exclude dirs and/or files: 
[2.269s] -- Added test 'lint_cmake' to check CMake code style
[2.271s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[2.275s] -- Added test 'uncrustify' to check C / C++ code style
[2.275s] -- Configured uncrustify additional arguments: 
[2.277s] -- Added test 'xmllint' to check XML markup files
[2.280s] -- Configuring done (2.2s)
[2.289s] [31mCMake Error at CMakeLists.txt:53 (add_library):
[2.289s]   Cannot find source file:
[2.289s] 
[2.289s]     src/driver/serial_driver.cpp
[2.289s] 
[2.289s]   Tried extensions .c .C .c++ .cc .cpp .cxx .cu .mpp .m .M .mm .ixx .cppm
[2.289s]   .ccm .cxxm .c++m .h .hh .h++ .hm .hpp .hxx .in .txx .f .F .for .f77 .f90
[2.289s]   .f95 .f03 .hip .ispc
[2.290s] 
[2.290s] [0m
[2.297s] [31mCMake Error at CMakeLists.txt:53 (add_library):
[2.297s]   No SOURCES given to target: sl_pmu_driver
[2.297s] 
[2.297s] [0m
[2.297s] [0mCMake Generate step failed.  Build files cannot be regenerated correctly.[0m
[2.310s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '1': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
