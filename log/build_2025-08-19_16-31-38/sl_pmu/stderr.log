[31mCMake Error at CMakeLists.txt:53 (add_library):
  Cannot find source file:

    src/driver/serial_driver.cpp

  Tried extensions .c .C .c++ .cc .cpp .cxx .cu .mpp .m .M .mm .ixx .cppm
  .ccm .cxxm .c++m .h .hh .h++ .hm .hpp .hxx .in .txx .f .F .for .f77 .f90
  .f95 .f03 .hip .ispc

[0m
[31mCMake Error at CMakeLists.txt:53 (add_library):
  No SOURCES given to target: sl_pmu_driver

[0m
[0mCMake Generate step failed.  Build files cannot be regenerated correctly.[0m
