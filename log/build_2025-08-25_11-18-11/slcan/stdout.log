-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/socket_can.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/canopen.cpp.o[0m
[100%] [32m[1mLinking CXX static library libslcan.a[0m
[100%] Built target slcan
-- Install configuration: ""
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-noconfig.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake
