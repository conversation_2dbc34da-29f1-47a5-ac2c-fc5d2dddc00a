[0.014s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan
[0.089s] -- The C compiler identification is GNU 13.3.0
[0.160s] -- The CXX compiler identification is GNU 13.3.0
[0.179s] -- Detecting C compiler ABI info
[0.295s] -- Detecting C compiler ABI info - done
[0.306s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.307s] -- Detecting C compile features
[0.307s] -- Detecting C compile features - done
[0.318s] -- Detecting CXX compiler ABI info
[0.447s] -- Detecting CXX compiler ABI info - done
[0.459s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.460s] -- Detecting CXX compile features
[0.460s] -- Detecting CXX compile features - done
[0.463s] -- Configuring done (0.4s)
[0.467s] -- Generating done (0.0s)
[0.468s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.472s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan
[0.474s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.535s] [ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/socket_can.cpp.o[0m
[0.535s] [ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/canopen.cpp.o[0m
[0.917s] [100%] [32m[1mLinking CXX static library libslcan.a[0m
[0.963s] [100%] Built target slcan
[0.978s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.990s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[1.001s] -- Install configuration: ""
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp
[1.002s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp
[1.003s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake
[1.003s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-noconfig.cmake
[1.003s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake
[1.005s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
