[  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
[ 11%] [32m[1mLinking CXX static library libsl_pmu_common.a[0m
[ 11%] Built target sl_pmu_common
[ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o[0m
[ 27%] [32m[1mLinking CXX static library libsl_pmu_driver.a[0m
[ 27%] Built target sl_pmu_driver
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.o[0m
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:[m[K In constructor ‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::[01;32m[KImpl[m[K()[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:361:23:[m[K [01;35m[Kwarning: [m[K‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
  361 |     std::atomic<bool> [01;35m[Kinitialized_[m[K;
      |                       [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:359:14:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
  359 |     uint32_t [01;35m[Kcan_id_tx_[m[K;
      |              [01;35m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   52 |     [01;35m[KImpl[m[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}
      |     [01;35m[K^~~~[m[K
[ 50%] [32m[1mLinking CXX static library libsl_pmu_protocol.a[0m
[ 50%] Built target sl_pmu_protocol
[ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.o[0m
[ 66%] [32m[1mLinking CXX static library libsl_pmu_device.a[0m
[ 66%] Built target sl_pmu_device
[ 72%] [32mBuilding CXX object CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.o[0m
[ 77%] [32m[1mLinking CXX static library libsl_pmu_application.a[0m
[ 77%] Built target sl_pmu_application
[ 83%] [32mBuilding CXX object CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.o[0m
[ 88%] [32m[1mLinking CXX static library libsl_pmu_ros_node.a[0m
[ 88%] Built target sl_pmu_ros_node
[ 94%] [32mBuilding CXX object CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.o[0m
[100%] [32m[1mLinking CXX executable pmu_node[0m
/usr/bin/ld: libsl_pmu_driver.a(communication_driver_factory.cpp.o): in function `std::__detail::_MakeUniq<sl_pmu::driver::SerialDriver>::__single_object std::make_unique<sl_pmu::driver::SerialDriver>()':
communication_driver_factory.cpp:(.text._ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_[_ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_]+0x2e): undefined reference to `sl_pmu::driver::SerialDriver::SerialDriver()'
collect2: error: ld returned 1 exit status
gmake[2]: *** [CMakeFiles/pmu_node.dir/build.make:239: pmu_node] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:320: CMakeFiles/pmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
