[  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
[ 11%] [32m[1mLinking CXX static library libsl_pmu_common.a[0m
[ 11%] Built target sl_pmu_common
[ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o[0m
[ 27%] [32m[1mLinking CXX static library libsl_pmu_driver.a[0m
[ 27%] Built target sl_pmu_driver
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.o[0m
[ 50%] [32m[1mLinking CXX static library libsl_pmu_protocol.a[0m
[ 50%] Built target sl_pmu_protocol
[ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.o[0m
[ 66%] [32m[1mLinking CXX static library libsl_pmu_device.a[0m
[ 66%] Built target sl_pmu_device
[ 72%] [32mBuilding CXX object CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.o[0m
[ 77%] [32m[1mLinking CXX static library libsl_pmu_application.a[0m
[ 77%] Built target sl_pmu_application
[ 83%] [32mBuilding CXX object CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.o[0m
[ 88%] [32m[1mLinking CXX static library libsl_pmu_ros_node.a[0m
[ 88%] Built target sl_pmu_ros_node
[ 94%] [32mBuilding CXX object CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.o[0m
[100%] [32m[1mLinking CXX executable pmu_node[0m
