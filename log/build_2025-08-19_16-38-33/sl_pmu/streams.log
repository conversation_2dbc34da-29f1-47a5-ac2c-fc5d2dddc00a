[0.016s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.086s] [  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
[0.631s] [ 11%] [32m[1mLinking CXX static library libsl_pmu_common.a[0m
[0.675s] [ 11%] Built target sl_pmu_common
[0.702s] [ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o[0m
[0.702s] [ 22%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o[0m
[1.561s] [ 27%] [32m[1mLinking CXX static library libsl_pmu_driver.a[0m
[1.607s] [ 27%] Built target sl_pmu_driver
[1.634s] [ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o[0m
[1.634s] [ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.o[0m
[1.634s] [ 44%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.o[0m
[2.212s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:[m[K In constructor ‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::[01;32m[KImpl[m[K()[m[K’:
[2.212s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:361:23:[m[K [01;35m[Kwarning: [m[K‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[2.212s]   361 |     std::atomic<bool> [01;35m[Kinitialized_[m[K;
[2.212s]       |                       [01;35m[K^~~~~~~~~~~~[m[K
[2.213s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:359:14:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[2.213s]   359 |     uint32_t [01;35m[Kcan_id_tx_[m[K;
[2.213s]       |              [01;35m[K^~~~~~~~~~[m[K
[2.213s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[2.213s]    52 |     [01;35m[KImpl[m[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}
[2.213s]       |     [01;35m[K^~~~[m[K
[2.726s] [ 50%] [32m[1mLinking CXX static library libsl_pmu_protocol.a[0m
[2.778s] [ 50%] Built target sl_pmu_protocol
[2.805s] [ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.o[0m
[2.806s] [ 61%] [32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.o[0m
[4.298s] [ 66%] [32m[1mLinking CXX static library libsl_pmu_device.a[0m
[4.353s] [ 66%] Built target sl_pmu_device
[4.380s] [ 72%] [32mBuilding CXX object CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.o[0m
[5.441s] [ 77%] [32m[1mLinking CXX static library libsl_pmu_application.a[0m
[5.488s] [ 77%] Built target sl_pmu_application
[5.513s] [ 83%] [32mBuilding CXX object CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.o[0m
[25.954s] [ 88%] [32m[1mLinking CXX static library libsl_pmu_ros_node.a[0m
[26.410s] [ 88%] Built target sl_pmu_ros_node
[26.436s] [ 94%] [32mBuilding CXX object CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.o[0m
[30.184s] [100%] [32m[1mLinking CXX executable pmu_node[0m
[31.206s] /usr/bin/ld: libsl_pmu_driver.a(communication_driver_factory.cpp.o): in function `std::__detail::_MakeUniq<sl_pmu::driver::SerialDriver>::__single_object std::make_unique<sl_pmu::driver::SerialDriver>()':
[31.206s] communication_driver_factory.cpp:(.text._ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_[_ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_]+0x2e): undefined reference to `sl_pmu::driver::SerialDriver::SerialDriver()'
[31.290s] collect2: error: ld returned 1 exit status
[31.292s] gmake[2]: *** [CMakeFiles/pmu_node.dir/build.make:239: pmu_node] Error 1
[31.292s] gmake[1]: *** [CMakeFiles/Makefile2:320: CMakeFiles/pmu_node.dir/all] Error 2
[31.293s] gmake: *** [Makefile:146: all] Error 2
[31.296s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
