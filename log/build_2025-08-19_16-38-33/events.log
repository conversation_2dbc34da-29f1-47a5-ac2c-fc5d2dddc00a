[0.000000] (-) TimerEvent: {}
[0.000315] (-) JobUnselected: {'identifier': 'sl_vcu_all'}
[0.000862] (sl_pmu) JobQueued: {'identifier': 'sl_pmu', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'})}
[0.000916] (sl_pmu) JobStarted: {'identifier': 'sl_pmu'}
[0.012769] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'cmake'}
[0.013789] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'build'}
[0.014736] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.087181] (sl_pmu) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o\x1b[0m\n'}
[0.099686] (-) TimerEvent: {}
[0.199967] (-) TimerEvent: {}
[0.300268] (-) TimerEvent: {}
[0.400550] (-) TimerEvent: {}
[0.500836] (-) TimerEvent: {}
[0.601137] (-) TimerEvent: {}
[0.632242] (sl_pmu) StdoutLine: {'line': b'[ 11%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_common.a\x1b[0m\n'}
[0.675961] (sl_pmu) StdoutLine: {'line': b'[ 11%] Built target sl_pmu_common\n'}
[0.701259] (-) TimerEvent: {}
[0.702547] (sl_pmu) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/socket_can_driver.cpp.o\x1b[0m\n'}
[0.702744] (sl_pmu) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/communication_driver_factory.cpp.o\x1b[0m\n'}
[0.801383] (-) TimerEvent: {}
[0.901655] (-) TimerEvent: {}
[1.001937] (-) TimerEvent: {}
[1.102234] (-) TimerEvent: {}
[1.202519] (-) TimerEvent: {}
[1.302805] (-) TimerEvent: {}
[1.403263] (-) TimerEvent: {}
[1.503559] (-) TimerEvent: {}
[1.562238] (sl_pmu) StdoutLine: {'line': b'[ 27%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_driver.a\x1b[0m\n'}
[1.603681] (-) TimerEvent: {}
[1.608215] (sl_pmu) StdoutLine: {'line': b'[ 27%] Built target sl_pmu_driver\n'}
[1.635052] (sl_pmu) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o\x1b[0m\n'}
[1.635247] (sl_pmu) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/modbus_rtu_protocol.cpp.o\x1b[0m\n'}
[1.635357] (sl_pmu) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/communication_protocol_factory.cpp.o\x1b[0m\n'}
[1.703810] (-) TimerEvent: {}
[1.804086] (-) TimerEvent: {}
[1.904363] (-) TimerEvent: {}
[2.004801] (-) TimerEvent: {}
[2.105384] (-) TimerEvent: {}
[2.207008] (-) TimerEvent: {}
[2.212639] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::\x1b[01;32m\x1b[KImpl\x1b[m\x1b[K()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.213279] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:361:23:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_\x1b[m\x1b[K\xe2\x80\x99 will be initialized after [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.213415] (sl_pmu) StderrLine: {'line': b'  361 |     std::atomic<bool> \x1b[01;35m\x1b[Kinitialized_\x1b[m\x1b[K;\n'}
[2.213505] (sl_pmu) StderrLine: {'line': b'      |                       \x1b[01;35m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.213589] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:359:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  \xe2\x80\x98\x1b[01m\x1b[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.213677] (sl_pmu) StderrLine: {'line': b'  359 |     uint32_t \x1b[01;35m\x1b[Kcan_id_tx_\x1b[m\x1b[K;\n'}
[2.213761] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[2.213842] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  when initialized here [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[2.213926] (sl_pmu) StderrLine: {'line': b'   52 |     \x1b[01;35m\x1b[KImpl\x1b[m\x1b[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}\n'}
[2.214017] (sl_pmu) StderrLine: {'line': b'      |     \x1b[01;35m\x1b[K^~~~\x1b[m\x1b[K\n'}
[2.307394] (-) TimerEvent: {}
[2.407658] (-) TimerEvent: {}
[2.508232] (-) TimerEvent: {}
[2.608536] (-) TimerEvent: {}
[2.708833] (-) TimerEvent: {}
[2.726929] (sl_pmu) StdoutLine: {'line': b'[ 50%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_protocol.a\x1b[0m\n'}
[2.778971] (sl_pmu) StdoutLine: {'line': b'[ 50%] Built target sl_pmu_protocol\n'}
[2.806240] (sl_pmu) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/power_controller.cpp.o\x1b[0m\n'}
[2.806516] (sl_pmu) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_device.dir/src/device/motor_controller.cpp.o\x1b[0m\n'}
[2.808929] (-) TimerEvent: {}
[2.909166] (-) TimerEvent: {}
[3.009418] (-) TimerEvent: {}
[3.109711] (-) TimerEvent: {}
[3.210002] (-) TimerEvent: {}
[3.310292] (-) TimerEvent: {}
[3.410843] (-) TimerEvent: {}
[3.511375] (-) TimerEvent: {}
[3.611904] (-) TimerEvent: {}
[3.712186] (-) TimerEvent: {}
[3.812645] (-) TimerEvent: {}
[3.912931] (-) TimerEvent: {}
[4.013227] (-) TimerEvent: {}
[4.113539] (-) TimerEvent: {}
[4.213836] (-) TimerEvent: {}
[4.299207] (sl_pmu) StdoutLine: {'line': b'[ 66%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_device.a\x1b[0m\n'}
[4.313962] (-) TimerEvent: {}
[4.354155] (sl_pmu) StdoutLine: {'line': b'[ 66%] Built target sl_pmu_device\n'}
[4.380753] (sl_pmu) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_application.dir/src/application/pmu_application.cpp.o\x1b[0m\n'}
[4.414082] (-) TimerEvent: {}
[4.514389] (-) TimerEvent: {}
[4.614681] (-) TimerEvent: {}
[4.714975] (-) TimerEvent: {}
[4.815276] (-) TimerEvent: {}
[4.915591] (-) TimerEvent: {}
[5.015892] (-) TimerEvent: {}
[5.116192] (-) TimerEvent: {}
[5.216503] (-) TimerEvent: {}
[5.316799] (-) TimerEvent: {}
[5.417089] (-) TimerEvent: {}
[5.442024] (sl_pmu) StdoutLine: {'line': b'[ 77%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_application.a\x1b[0m\n'}
[5.488811] (sl_pmu) StdoutLine: {'line': b'[ 77%] Built target sl_pmu_application\n'}
[5.514245] (sl_pmu) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_ros_node.dir/src/ros_node/pmu_ros_node.cpp.o\x1b[0m\n'}
[5.517200] (-) TimerEvent: {}
[5.617418] (-) TimerEvent: {}
[5.717703] (-) TimerEvent: {}
[5.818024] (-) TimerEvent: {}
[5.918328] (-) TimerEvent: {}
[6.018681] (-) TimerEvent: {}
[6.118976] (-) TimerEvent: {}
[6.219263] (-) TimerEvent: {}
[6.319607] (-) TimerEvent: {}
[6.419902] (-) TimerEvent: {}
[6.520204] (-) TimerEvent: {}
[6.620523] (-) TimerEvent: {}
[6.720824] (-) TimerEvent: {}
[6.821128] (-) TimerEvent: {}
[6.921431] (-) TimerEvent: {}
[7.021730] (-) TimerEvent: {}
[7.122034] (-) TimerEvent: {}
[7.222331] (-) TimerEvent: {}
[7.322628] (-) TimerEvent: {}
[7.422918] (-) TimerEvent: {}
[7.523201] (-) TimerEvent: {}
[7.623487] (-) TimerEvent: {}
[7.723785] (-) TimerEvent: {}
[7.824076] (-) TimerEvent: {}
[7.924422] (-) TimerEvent: {}
[8.024727] (-) TimerEvent: {}
[8.125024] (-) TimerEvent: {}
[8.225308] (-) TimerEvent: {}
[8.325574] (-) TimerEvent: {}
[8.425876] (-) TimerEvent: {}
[8.526174] (-) TimerEvent: {}
[8.626419] (-) TimerEvent: {}
[8.726712] (-) TimerEvent: {}
[8.827001] (-) TimerEvent: {}
[8.927301] (-) TimerEvent: {}
[9.027637] (-) TimerEvent: {}
[9.127939] (-) TimerEvent: {}
[9.228240] (-) TimerEvent: {}
[9.328586] (-) TimerEvent: {}
[9.428891] (-) TimerEvent: {}
[9.529195] (-) TimerEvent: {}
[9.629478] (-) TimerEvent: {}
[9.729785] (-) TimerEvent: {}
[9.830089] (-) TimerEvent: {}
[9.930428] (-) TimerEvent: {}
[10.030730] (-) TimerEvent: {}
[10.131035] (-) TimerEvent: {}
[10.231330] (-) TimerEvent: {}
[10.331676] (-) TimerEvent: {}
[10.431973] (-) TimerEvent: {}
[10.532260] (-) TimerEvent: {}
[10.632545] (-) TimerEvent: {}
[10.732836] (-) TimerEvent: {}
[10.833138] (-) TimerEvent: {}
[10.933442] (-) TimerEvent: {}
[11.033745] (-) TimerEvent: {}
[11.134048] (-) TimerEvent: {}
[11.234377] (-) TimerEvent: {}
[11.334702] (-) TimerEvent: {}
[11.435004] (-) TimerEvent: {}
[11.535306] (-) TimerEvent: {}
[11.635662] (-) TimerEvent: {}
[11.735968] (-) TimerEvent: {}
[11.836238] (-) TimerEvent: {}
[11.936592] (-) TimerEvent: {}
[12.036898] (-) TimerEvent: {}
[12.137201] (-) TimerEvent: {}
[12.237516] (-) TimerEvent: {}
[12.337822] (-) TimerEvent: {}
[12.438125] (-) TimerEvent: {}
[12.538419] (-) TimerEvent: {}
[12.638727] (-) TimerEvent: {}
[12.739037] (-) TimerEvent: {}
[12.839335] (-) TimerEvent: {}
[12.939687] (-) TimerEvent: {}
[13.039996] (-) TimerEvent: {}
[13.140300] (-) TimerEvent: {}
[13.240647] (-) TimerEvent: {}
[13.340951] (-) TimerEvent: {}
[13.441251] (-) TimerEvent: {}
[13.541537] (-) TimerEvent: {}
[13.641852] (-) TimerEvent: {}
[13.742152] (-) TimerEvent: {}
[13.842434] (-) TimerEvent: {}
[13.942744] (-) TimerEvent: {}
[14.043069] (-) TimerEvent: {}
[14.143405] (-) TimerEvent: {}
[14.243693] (-) TimerEvent: {}
[14.343988] (-) TimerEvent: {}
[14.444294] (-) TimerEvent: {}
[14.544616] (-) TimerEvent: {}
[14.644916] (-) TimerEvent: {}
[14.745216] (-) TimerEvent: {}
[14.845530] (-) TimerEvent: {}
[14.945835] (-) TimerEvent: {}
[15.046138] (-) TimerEvent: {}
[15.146436] (-) TimerEvent: {}
[15.246728] (-) TimerEvent: {}
[15.347023] (-) TimerEvent: {}
[15.447322] (-) TimerEvent: {}
[15.547615] (-) TimerEvent: {}
[15.647900] (-) TimerEvent: {}
[15.748202] (-) TimerEvent: {}
[15.848517] (-) TimerEvent: {}
[15.948834] (-) TimerEvent: {}
[16.049146] (-) TimerEvent: {}
[16.149444] (-) TimerEvent: {}
[16.249742] (-) TimerEvent: {}
[16.350043] (-) TimerEvent: {}
[16.450335] (-) TimerEvent: {}
[16.550671] (-) TimerEvent: {}
[16.650963] (-) TimerEvent: {}
[16.751288] (-) TimerEvent: {}
[16.851589] (-) TimerEvent: {}
[16.951885] (-) TimerEvent: {}
[17.052182] (-) TimerEvent: {}
[17.152477] (-) TimerEvent: {}
[17.252762] (-) TimerEvent: {}
[17.353059] (-) TimerEvent: {}
[17.453387] (-) TimerEvent: {}
[17.553698] (-) TimerEvent: {}
[17.653989] (-) TimerEvent: {}
[17.754277] (-) TimerEvent: {}
[17.854580] (-) TimerEvent: {}
[17.954874] (-) TimerEvent: {}
[18.055166] (-) TimerEvent: {}
[18.155461] (-) TimerEvent: {}
[18.255754] (-) TimerEvent: {}
[18.356050] (-) TimerEvent: {}
[18.456370] (-) TimerEvent: {}
[18.556681] (-) TimerEvent: {}
[18.656972] (-) TimerEvent: {}
[18.757261] (-) TimerEvent: {}
[18.857594] (-) TimerEvent: {}
[18.957887] (-) TimerEvent: {}
[19.058181] (-) TimerEvent: {}
[19.158419] (-) TimerEvent: {}
[19.258711] (-) TimerEvent: {}
[19.358966] (-) TimerEvent: {}
[19.459256] (-) TimerEvent: {}
[19.559595] (-) TimerEvent: {}
[19.659891] (-) TimerEvent: {}
[19.760182] (-) TimerEvent: {}
[19.860497] (-) TimerEvent: {}
[19.960789] (-) TimerEvent: {}
[20.061082] (-) TimerEvent: {}
[20.161407] (-) TimerEvent: {}
[20.261688] (-) TimerEvent: {}
[20.361979] (-) TimerEvent: {}
[20.462283] (-) TimerEvent: {}
[20.562599] (-) TimerEvent: {}
[20.662886] (-) TimerEvent: {}
[20.763178] (-) TimerEvent: {}
[20.863480] (-) TimerEvent: {}
[20.963771] (-) TimerEvent: {}
[21.064065] (-) TimerEvent: {}
[21.164389] (-) TimerEvent: {}
[21.264688] (-) TimerEvent: {}
[21.364977] (-) TimerEvent: {}
[21.465267] (-) TimerEvent: {}
[21.565595] (-) TimerEvent: {}
[21.665877] (-) TimerEvent: {}
[21.766169] (-) TimerEvent: {}
[21.866418] (-) TimerEvent: {}
[21.966701] (-) TimerEvent: {}
[22.066994] (-) TimerEvent: {}
[22.167283] (-) TimerEvent: {}
[22.267582] (-) TimerEvent: {}
[22.367874] (-) TimerEvent: {}
[22.468165] (-) TimerEvent: {}
[22.568418] (-) TimerEvent: {}
[22.668707] (-) TimerEvent: {}
[22.769000] (-) TimerEvent: {}
[22.869297] (-) TimerEvent: {}
[22.969641] (-) TimerEvent: {}
[23.069936] (-) TimerEvent: {}
[23.170228] (-) TimerEvent: {}
[23.270535] (-) TimerEvent: {}
[23.370816] (-) TimerEvent: {}
[23.471110] (-) TimerEvent: {}
[23.571441] (-) TimerEvent: {}
[23.671732] (-) TimerEvent: {}
[23.772028] (-) TimerEvent: {}
[23.872322] (-) TimerEvent: {}
[23.972634] (-) TimerEvent: {}
[24.073161] (-) TimerEvent: {}
[24.174047] (-) TimerEvent: {}
[24.274326] (-) TimerEvent: {}
[24.374610] (-) TimerEvent: {}
[24.474989] (-) TimerEvent: {}
[24.575286] (-) TimerEvent: {}
[24.675638] (-) TimerEvent: {}
[24.775935] (-) TimerEvent: {}
[24.876232] (-) TimerEvent: {}
[24.976567] (-) TimerEvent: {}
[25.076868] (-) TimerEvent: {}
[25.177163] (-) TimerEvent: {}
[25.277462] (-) TimerEvent: {}
[25.377769] (-) TimerEvent: {}
[25.478071] (-) TimerEvent: {}
[25.578368] (-) TimerEvent: {}
[25.678661] (-) TimerEvent: {}
[25.778967] (-) TimerEvent: {}
[25.879278] (-) TimerEvent: {}
[25.955051] (sl_pmu) StdoutLine: {'line': b'[ 88%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_ros_node.a\x1b[0m\n'}
[25.979453] (-) TimerEvent: {}
[26.079782] (-) TimerEvent: {}
[26.180114] (-) TimerEvent: {}
[26.280430] (-) TimerEvent: {}
[26.380666] (-) TimerEvent: {}
[26.410675] (sl_pmu) StdoutLine: {'line': b'[ 88%] Built target sl_pmu_ros_node\n'}
[26.437071] (sl_pmu) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/pmu_node.dir/src/ros_node/pmu_main.cpp.o\x1b[0m\n'}
[26.480791] (-) TimerEvent: {}
[26.581084] (-) TimerEvent: {}
[26.681411] (-) TimerEvent: {}
[26.781701] (-) TimerEvent: {}
[26.881964] (-) TimerEvent: {}
[26.982422] (-) TimerEvent: {}
[27.082675] (-) TimerEvent: {}
[27.182950] (-) TimerEvent: {}
[27.283212] (-) TimerEvent: {}
[27.384422] (-) TimerEvent: {}
[27.484712] (-) TimerEvent: {}
[27.585002] (-) TimerEvent: {}
[27.685294] (-) TimerEvent: {}
[27.785574] (-) TimerEvent: {}
[27.885840] (-) TimerEvent: {}
[27.986119] (-) TimerEvent: {}
[28.086570] (-) TimerEvent: {}
[28.186861] (-) TimerEvent: {}
[28.287145] (-) TimerEvent: {}
[28.387428] (-) TimerEvent: {}
[28.487687] (-) TimerEvent: {}
[28.590430] (-) TimerEvent: {}
[28.690701] (-) TimerEvent: {}
[28.791140] (-) TimerEvent: {}
[28.891432] (-) TimerEvent: {}
[28.991683] (-) TimerEvent: {}
[29.091975] (-) TimerEvent: {}
[29.192262] (-) TimerEvent: {}
[29.292789] (-) TimerEvent: {}
[29.393080] (-) TimerEvent: {}
[29.493407] (-) TimerEvent: {}
[29.593944] (-) TimerEvent: {}
[29.694210] (-) TimerEvent: {}
[29.794643] (-) TimerEvent: {}
[29.894927] (-) TimerEvent: {}
[29.995231] (-) TimerEvent: {}
[30.095511] (-) TimerEvent: {}
[30.184489] (sl_pmu) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable pmu_node\x1b[0m\n'}
[30.195658] (-) TimerEvent: {}
[30.295958] (-) TimerEvent: {}
[30.396250] (-) TimerEvent: {}
[30.496558] (-) TimerEvent: {}
[30.596861] (-) TimerEvent: {}
[30.697371] (-) TimerEvent: {}
[30.797710] (-) TimerEvent: {}
[30.898041] (-) TimerEvent: {}
[30.998377] (-) TimerEvent: {}
[31.098658] (-) TimerEvent: {}
[31.198972] (-) TimerEvent: {}
[31.207182] (sl_pmu) StderrLine: {'line': b"/usr/bin/ld: libsl_pmu_driver.a(communication_driver_factory.cpp.o): in function `std::__detail::_MakeUniq<sl_pmu::driver::SerialDriver>::__single_object std::make_unique<sl_pmu::driver::SerialDriver>()':\n"}
[31.207371] (sl_pmu) StderrLine: {'line': b"communication_driver_factory.cpp:(.text._ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_[_ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_]+0x2e): undefined reference to `sl_pmu::driver::SerialDriver::SerialDriver()'\n"}
[31.291088] (sl_pmu) StderrLine: {'line': b'collect2: error: ld returned 1 exit status\n'}
[31.293016] (sl_pmu) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/pmu_node.dir/build.make:239: pmu_node] Error 1\n'}
[31.293287] (sl_pmu) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:320: CMakeFiles/pmu_node.dir/all] Error 2\n'}
[31.293542] (sl_pmu) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[31.296266] (sl_pmu) CommandEnded: {'returncode': 2}
[31.299235] (-) TimerEvent: {}
[31.306204] (sl_pmu) JobEnded: {'identifier': 'sl_pmu', 'rc': 2}
[31.316777] (-) EventReactorShutdown: {}
