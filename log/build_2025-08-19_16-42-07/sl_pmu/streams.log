[0.016s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.049s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.297s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.332s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.342s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.356s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.373s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.447s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.449s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.529s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.607s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.625s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.646s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.683s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.713s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.717s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.852s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.857s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.859s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.861s] -- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)
[0.925s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.926s] -- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)
[1.034s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.037s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.039s] -- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include
[1.039s] -- Configured cppcheck exclude dirs and/or files: 
[1.042s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.042s] -- Configured cpplint exclude dirs and/or files: 
[1.043s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.043s] -- Configured 'flake8' exclude dirs and/or files: 
[1.044s] -- Added test 'lint_cmake' to check CMake code style
[1.045s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.048s] -- Added test 'uncrustify' to check C / C++ code style
[1.048s] -- Configured uncrustify additional arguments: 
[1.049s] -- Added test 'xmllint' to check XML markup files
[1.050s] -- Configuring done (1.0s)
[1.110s] -- Generating done (0.0s)
[1.122s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[1.172s] [ 10%] Built target sl_pmu_common
[1.203s] [ 15%] [32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/serial_driver.cpp.o[0m
[2.023s] [ 21%] [32m[1mLinking CXX static library libsl_pmu_driver.a[0m
[2.071s] [ 31%] Built target sl_pmu_driver
[2.103s] [ 52%] Built target sl_pmu_protocol
[2.135s] [ 68%] Built target sl_pmu_device
[2.164s] [ 78%] Built target sl_pmu_application
[2.203s] [ 89%] Built target sl_pmu_ros_node
[2.242s] [ 94%] [32m[1mLinking CXX executable pmu_node[0m
[3.297s] [100%] Built target pmu_node
[3.312s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[3.326s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[3.337s] -- Install configuration: ""
[3.337s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_common.a
[3.337s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_driver.a
[3.338s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_protocol.a
[3.339s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_device.a
[3.340s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_application.a
[3.341s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_ros_node.a
[3.362s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node
[3.377s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node" to ""
[3.377s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include/
[3.377s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu
[3.377s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node
[3.378s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node/pmu_ros_node.hpp
[3.378s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol
[3.378s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol/communication_protocol.hpp
[3.379s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver
[3.379s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver/communication_driver.hpp
[3.379s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application
[3.379s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application/pmu_application.hpp
[3.380s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device
[3.380s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/device_interface.hpp
[3.380s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/motor_controller.hpp
[3.380s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common
[3.380s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common/types.hpp
[3.381s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config/
[3.381s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config//pmu_config.yaml
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch/
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch//pmu.launch.py
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/doc/
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/package_run_dependencies/sl_pmu
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/parent_prefix_path/sl_pmu
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.sh
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.dsv
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.sh
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.dsv
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.bash
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.sh
[3.382s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.zsh
[3.384s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.dsv
[3.384s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv
[3.385s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/packages/sl_pmu
[3.385s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig.cmake
[3.385s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig-version.cmake
[3.386s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.xml
[3.386s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
