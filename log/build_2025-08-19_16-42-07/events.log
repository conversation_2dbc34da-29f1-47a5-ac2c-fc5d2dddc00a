[0.000000] (-) TimerEvent: {}
[0.000428] (-) JobUnselected: {'identifier': 'sl_vcu_all'}
[0.000504] (sl_pmu) JobQueued: {'identifier': 'sl_pmu', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'})}
[0.000538] (sl_pmu) JobStarted: {'identifier': 'sl_pmu'}
[0.012461] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'cmake'}
[0.013377] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'build'}
[0.014292] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.049394] (sl_pmu) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.098852] (-) TimerEvent: {}
[0.199097] (-) TimerEvent: {}
[0.297586] (sl_pmu) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[0.299177] (-) TimerEvent: {}
[0.332216] (sl_pmu) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[0.342781] (sl_pmu) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[0.356355] (sl_pmu) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.373848] (sl_pmu) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.399296] (-) TimerEvent: {}
[0.447477] (sl_pmu) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[0.449403] (sl_pmu) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[0.499424] (-) TimerEvent: {}
[0.529417] (sl_pmu) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.599548] (-) TimerEvent: {}
[0.607480] (sl_pmu) StdoutLine: {'line': b'-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)\n'}
[0.625200] (sl_pmu) StdoutLine: {'line': b'-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)\n'}
[0.646296] (sl_pmu) StdoutLine: {'line': b'-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)\n'}
[0.683541] (sl_pmu) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[0.699652] (-) TimerEvent: {}
[0.713338] (sl_pmu) StdoutLine: {'line': b'-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)\n'}
[0.717702] (sl_pmu) StdoutLine: {'line': b'-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)\n'}
[0.799778] (-) TimerEvent: {}
[0.852826] (sl_pmu) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)\n'}
[0.857304] (sl_pmu) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)\n'}
[0.859004] (sl_pmu) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.861429] (sl_pmu) StdoutLine: {'line': b'-- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)\n'}
[0.899919] (-) TimerEvent: {}
[0.925769] (sl_pmu) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[0.926754] (sl_pmu) StdoutLine: {'line': b'-- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)\n'}
[1.000019] (-) TimerEvent: {}
[1.034219] (sl_pmu) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.037793] (sl_pmu) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.038972] (sl_pmu) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include\n'}
[1.039158] (sl_pmu) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.042235] (sl_pmu) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.042383] (sl_pmu) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.043169] (sl_pmu) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.043316] (sl_pmu) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[1.044381] (sl_pmu) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.045247] (sl_pmu) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.048299] (sl_pmu) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.048454] (sl_pmu) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.049079] (sl_pmu) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.050148] (sl_pmu) StdoutLine: {'line': b'-- Configuring done (1.0s)\n'}
[1.100141] (-) TimerEvent: {}
[1.109997] (sl_pmu) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[1.121894] (sl_pmu) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu\n'}
[1.172364] (sl_pmu) StdoutLine: {'line': b'[ 10%] Built target sl_pmu_common\n'}
[1.200222] (-) TimerEvent: {}
[1.203849] (sl_pmu) StdoutLine: {'line': b'[ 15%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_driver.dir/src/driver/serial_driver.cpp.o\x1b[0m\n'}
[1.300340] (-) TimerEvent: {}
[1.400605] (-) TimerEvent: {}
[1.500864] (-) TimerEvent: {}
[1.601152] (-) TimerEvent: {}
[1.701437] (-) TimerEvent: {}
[1.801743] (-) TimerEvent: {}
[1.902017] (-) TimerEvent: {}
[2.002281] (-) TimerEvent: {}
[2.023820] (sl_pmu) StdoutLine: {'line': b'[ 21%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_driver.a\x1b[0m\n'}
[2.071474] (sl_pmu) StdoutLine: {'line': b'[ 31%] Built target sl_pmu_driver\n'}
[2.102410] (-) TimerEvent: {}
[2.103627] (sl_pmu) StdoutLine: {'line': b'[ 52%] Built target sl_pmu_protocol\n'}
[2.134911] (sl_pmu) StdoutLine: {'line': b'[ 68%] Built target sl_pmu_device\n'}
[2.164424] (sl_pmu) StdoutLine: {'line': b'[ 78%] Built target sl_pmu_application\n'}
[2.202535] (-) TimerEvent: {}
[2.203510] (sl_pmu) StdoutLine: {'line': b'[ 89%] Built target sl_pmu_ros_node\n'}
[2.242699] (sl_pmu) StdoutLine: {'line': b'[ 94%] \x1b[32m\x1b[1mLinking CXX executable pmu_node\x1b[0m\n'}
[2.302658] (-) TimerEvent: {}
[2.402960] (-) TimerEvent: {}
[2.503294] (-) TimerEvent: {}
[2.603623] (-) TimerEvent: {}
[2.703959] (-) TimerEvent: {}
[2.804345] (-) TimerEvent: {}
[2.904676] (-) TimerEvent: {}
[3.004992] (-) TimerEvent: {}
[3.105331] (-) TimerEvent: {}
[3.205611] (-) TimerEvent: {}
[3.297146] (sl_pmu) StdoutLine: {'line': b'[100%] Built target pmu_node\n'}
[3.305707] (-) TimerEvent: {}
[3.312788] (sl_pmu) CommandEnded: {'returncode': 0}
[3.313539] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'install'}
[3.325594] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[3.336803] (sl_pmu) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[3.337329] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_common.a\n'}
[3.337450] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_driver.a\n'}
[3.337975] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_protocol.a\n'}
[3.338929] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_device.a\n'}
[3.340309] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_application.a\n'}
[3.341006] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_ros_node.a\n'}
[3.362728] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node\n'}
[3.376925] (sl_pmu) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node" to ""\n'}
[3.377123] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include/\n'}
[3.377293] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu\n'}
[3.377684] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node\n'}
[3.377955] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node/pmu_ros_node.hpp\n'}
[3.378287] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol\n'}
[3.378494] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol/communication_protocol.hpp\n'}
[3.378839] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver\n'}
[3.379145] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver/communication_driver.hpp\n'}
[3.379244] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application\n'}
[3.379592] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application/pmu_application.hpp\n'}
[3.379884] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device\n'}
[3.380191] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/device_interface.hpp\n'}
[3.380286] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/motor_controller.hpp\n'}
[3.380445] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common\n'}
[3.380728] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common/types.hpp\n'}
[3.381009] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config/\n'}
[3.381141] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config//pmu_config.yaml\n'}
[3.381598] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch/\n'}
[3.382157] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch//pmu.launch.py\n'}
[3.382243] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/doc/\n'}
[3.382324] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/package_run_dependencies/sl_pmu\n'}
[3.382405] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/parent_prefix_path/sl_pmu\n'}
[3.382485] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.sh\n'}
[3.382564] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.dsv\n'}
[3.382643] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.sh\n'}
[3.382721] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.dsv\n'}
[3.382801] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.bash\n'}
[3.382879] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.sh\n'}
[3.382956] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.zsh\n'}
[3.384104] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.dsv\n'}
[3.384407] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv\n'}
[3.385071] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/packages/sl_pmu\n'}
[3.385554] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig.cmake\n'}
[3.385820] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig-version.cmake\n'}
[3.385941] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.xml\n'}
[3.386242] (sl_pmu) CommandEnded: {'returncode': 0}
[3.405420] (sl_pmu) JobEnded: {'identifier': 'sl_pmu', 'rc': 0}
[3.406409] (-) TimerEvent: {}
[3.406490] (-) EventReactorShutdown: {}
