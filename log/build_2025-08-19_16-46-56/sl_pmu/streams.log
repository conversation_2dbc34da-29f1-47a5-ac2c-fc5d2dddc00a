[0.011s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.081s] [ 10%] Built target sl_pmu_common
[0.112s] [ 31%] Built target sl_pmu_driver
[0.141s] [ 52%] Built target sl_pmu_protocol
[0.169s] [ 68%] Built target sl_pmu_device
[0.197s] [ 78%] Built target sl_pmu_application
[0.231s] [ 89%] Built target sl_pmu_ros_node
[0.265s] [100%] Built target pmu_node
[0.280s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.281s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[0.292s] -- Install configuration: ""
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_common.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_driver.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_protocol.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_device.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_application.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_ros_node.a
[0.292s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include/
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node/pmu_ros_node.hpp
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol/communication_protocol.hpp
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver
[0.293s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver/communication_driver.hpp
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application/pmu_application.hpp
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/device_interface.hpp
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/motor_controller.hpp
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common/types.hpp
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config/
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config//pmu_config.yaml
[0.294s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch/
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch//pmu.launch.py
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/doc/
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/package_run_dependencies/sl_pmu
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/parent_prefix_path/sl_pmu
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.sh
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.dsv
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.sh
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.dsv
[0.295s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.bash
[0.296s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.sh
[0.296s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.zsh
[0.298s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.dsv
[0.298s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv
[0.299s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/packages/sl_pmu
[0.299s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig.cmake
[0.299s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig-version.cmake
[0.299s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.xml
[0.300s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
