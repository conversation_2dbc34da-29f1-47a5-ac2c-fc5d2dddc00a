[0.134s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'slpmu_jack', '--cmake-args', '-DCMAKE_BUILD_TYPE=RelWithDebInfo']
[0.135s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['slpmu_jack'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=RelWithDebInfo'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7ae026ca9eb0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ae026ca9ac0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ae026ca9ac0>>, mixin_verb=('build',))
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.182s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.225s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.226s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.228s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.248s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sl_vcu_all' in 'src/sl_vcu_all'
[0.248s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'slcan' in 'src/pmu/slcan'
[0.248s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'slpmu_process' in 'src/pmu/slpmu_process'
[0.248s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'slpmu_motor' in 'src/pmu/slpmu_motor'
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.252s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.254s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=RelWithDebInfo']'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.334s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=RelWithDebInfo'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.334s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.335s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.336s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.336s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack'
[0.338s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.338s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.338s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.352s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[2.028s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[2.041s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[2.057s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[2.057s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[2.057s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[2.058s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[2.058s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[2.061s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[2.061s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[2.062s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[2.062s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[2.063s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[2.065s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake module files
[2.066s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake config files
[2.067s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib'
[2.067s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[2.067s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[2.067s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[2.068s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[2.068s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[2.068s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[2.069s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/python3.12/site-packages'
[2.069s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[2.069s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[2.069s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[2.070s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[2.070s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[2.071s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[2.072s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[2.073s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[2.074s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[2.075s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[2.075s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[2.076s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.076s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.077s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.077s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.083s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.083s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.083s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.097s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.097s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[2.098s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[2.099s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[2.100s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[2.101s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[2.102s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[2.103s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[2.103s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[2.104s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[2.105s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
