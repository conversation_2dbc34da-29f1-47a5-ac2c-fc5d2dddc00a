[0.014s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
[0.032s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.280s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.314s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.325s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.340s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.356s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.432s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.434s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.516s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.596s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.612s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.630s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.665s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.694s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.698s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.839s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.844s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.846s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.848s] -- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)
[0.911s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.912s] -- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)
[1.017s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.020s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.021s] -- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include
[1.021s] -- Configured cppcheck exclude dirs and/or files: 
[1.024s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.025s] -- Configured cpplint exclude dirs and/or files: 
[1.025s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.025s] -- Configured 'flake8' exclude dirs and/or files: 
[1.026s] -- Added test 'lint_cmake' to check CMake code style
[1.027s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.030s] -- Added test 'uncrustify' to check C / C++ code style
[1.030s] -- Configured uncrustify additional arguments: 
[1.031s] -- Added test 'xmllint' to check XML markup files
[1.032s] -- Configuring done (1.0s)
[1.089s] -- Generating done (0.0s)
[1.099s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[1.110s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu
[1.112s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[1.176s] [  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
[1.580s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/common/config_manager.cpp:1[m[K:
[1.580s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:30:17:[m[K [01;31m[Kerror: [m[Kfield ‘[01m[Kmessage[m[K’ has incomplete type ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kstd::__cxx11::basic_string<char>[m[K’}
[1.580s]    30 |     std::string [01;31m[Kmessage[m[K;
[1.580s]       |                 [01;31m[K^~~~~~~[m[K
[1.580s] In file included from [01m[K/usr/include/c++/13/iosfwd:41[m[K,
[1.580s]                  from [01m[K/usr/include/c++/13/bits/shared_ptr.h:52[m[K,
[1.580s]                  from [01m[K/usr/include/c++/13/memory:80[m[K,
[1.580s]                  from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:5[m[K:
[1.580s] [01m[K/usr/include/c++/13/bits/stringfwd.h:72:11:[m[K [01;36m[Knote: [m[Kdeclaration of ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kclass std::__cxx11::basic_string<char>[m[K’}
[1.580s]    72 |     class [01;36m[Kbasic_string[m[K;
[1.581s]       |           [01;36m[K^~~~~~~~~~~~[m[K
[1.581s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kcould not convert ‘[01m[K""[m[K’ from ‘[01m[Kconst char [1][m[K’ to ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’}
[1.581s]    33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
[1.581s]       |                                                                              [01;31m[K^~[m[K
[1.581s]       |                                                                              [01;31m[K|[m[K
[1.581s]       |                                                                              [01;31m[Kconst char [1][m[K
[1.583s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:136:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
[1.583s]   136 |     std::[01;31m[Kmutex[m[K mutex;
[1.583s]       |          [01;31m[K^~~~~[m[K
[1.583s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[1.583s]     6 | #include <chrono>
[1.583s]   +++ |+[32m[K#include <mutex>[m[K
[1.583s]     7 | #include <cstdint>
[1.583s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:137:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kcondition_variable[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
[1.583s]   137 |     std::[01;31m[Kcondition_variable[m[K cv;
[1.584s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~[m[K
[1.584s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::condition_variable[m[K’ is defined in header ‘[01m[K<condition_variable>[m[K’; did you forget to ‘[01m[K#include <condition_variable>[m[K’?
[1.584s]     6 | #include <chrono>
[1.584s]   +++ |+[32m[K#include <condition_variable>[m[K
[1.584s]     7 | #include <cstdint>
[1.584s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In constructor ‘[01m[Ksl_pmu::common::ResponseWaiter::[01;32m[KResponseWaiter[m[K()[m[K’:
[1.584s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kinvalid initialization of reference of type ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’} from expression of type ‘[01m[Kconst char [1][m[K’
[1.584s]    33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
[1.584s]       |                                                                              [01;31m[K^~[m[K
[1.584s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:72:[m[K [01;36m[Knote: [m[Kin passing argument 2 of ‘[01m[Ksl_pmu::common::AsyncResult::[01;32m[KAsyncResult[m[K(sl_pmu::common::ResultCode, const std::string&, uint32_t)[m[K’
[1.584s]    33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, [01;36m[Kconst std::string& msg = ""[m[K, uint32_t d = 0)
[1.585s]       |                                                     [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kbool sl_pmu::common::ResponseWaiter::[01;32m[KwaitForResponse[m[K(std::chrono::milliseconds)[m[K’:
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;31m[Kerror: [m[K‘[01m[Kunique_lock[m[K’ is not a member of ‘[01m[Kstd[m[K’
[1.585s]   145 |         std::[01;31m[Kunique_lock[m[K<std::mutex> lock(mutex);
[1.585s]       |              [01;31m[K^~~~~~~~~~~[m[K
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::unique_lock[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
[1.585s]   145 |         std::unique_lock<std::[01;31m[Kmutex[m[K> lock(mutex);
[1.585s]       |                               [01;31m[K^~~~~[m[K
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[1.585s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:43:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
[1.586s]   145 |         std::unique_lock<std::mutex> lock([01;31m[Kmutex[m[K);
[1.586s]       |                                           [01;31m[K^~~~~[m[K
[1.587s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:38:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
[1.587s]   145 |         std::unique_lock<std::mutex> [01;31m[Klock[m[K(mutex);
[1.587s]       |                                      [01;31m[K^~~~[m[K
[1.587s]       |                                      [32m[Kclock[m[K
[1.588s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:146:16:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
[1.588s]   146 |         return [01;31m[Kcv[m[K.wait_for(lock, timeout, [this] { return response_received; });
[1.588s]       |                [01;31m[K^~[m[K
[1.589s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kvoid sl_pmu::common::ResponseWaiter::[01;32m[KsignalResponse[m[K(const sl_pmu::common::AsyncResult&)[m[K’:
[1.589s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;31m[Kerror: [m[K‘[01m[Klock_guard[m[K’ is not a member of ‘[01m[Kstd[m[K’
[1.589s]   151 |         std::[01;31m[Klock_guard[m[K<std::mutex> lock(mutex);
[1.589s]       |              [01;31m[K^~~~~~~~~~[m[K
[1.589s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::lock_guard[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[1.589s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
[1.589s]   151 |         std::lock_guard<std::[01;31m[Kmutex[m[K> lock(mutex);
[1.589s]       |                              [01;31m[K^~~~~[m[K
[1.589s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[1.590s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:42:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
[1.591s]   151 |         std::lock_guard<std::mutex> lock([01;31m[Kmutex[m[K);
[1.591s]       |                                          [01;31m[K^~~~~[m[K
[1.592s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:37:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
[1.592s]   151 |         std::lock_guard<std::mutex> [01;31m[Klock[m[K(mutex);
[1.592s]       |                                     [01;31m[K^~~~[m[K
[1.593s]       |                                     [32m[Kclock[m[K
[1.594s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:154:9:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
[1.594s]   154 |         [01;31m[Kcv[m[K.notify_one();
[1.594s]       |         [01;31m[K^~[m[K
[1.699s] gmake[2]: *** [CMakeFiles/sl_pmu_common.dir/build.make:76: CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o] Error 1
[1.699s] gmake[1]: *** [CMakeFiles/Makefile2:149: CMakeFiles/sl_pmu_common.dir/all] Error 2
[1.699s] gmake: *** [Makefile:146: all] Error 2
[1.702s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
