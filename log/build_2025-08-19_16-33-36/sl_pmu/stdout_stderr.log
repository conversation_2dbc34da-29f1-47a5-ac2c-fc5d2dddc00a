-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (1.0s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/common/config_manager.cpp:1[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:30:17:[m[K [01;31m[Kerror: [m[Kfield ‘[01m[Kmessage[m[K’ has incomplete type ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kstd::__cxx11::basic_string<char>[m[K’}
   30 |     std::string [01;31m[Kmessage[m[K;
      |                 [01;31m[K^~~~~~~[m[K
In file included from [01m[K/usr/include/c++/13/iosfwd:41[m[K,
                 from [01m[K/usr/include/c++/13/bits/shared_ptr.h:52[m[K,
                 from [01m[K/usr/include/c++/13/memory:80[m[K,
                 from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:5[m[K:
[01m[K/usr/include/c++/13/bits/stringfwd.h:72:11:[m[K [01;36m[Knote: [m[Kdeclaration of ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kclass std::__cxx11::basic_string<char>[m[K’}
   72 |     class [01;36m[Kbasic_string[m[K;
      |           [01;36m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kcould not convert ‘[01m[K""[m[K’ from ‘[01m[Kconst char [1][m[K’ to ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’}
   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
      |                                                                              [01;31m[K^~[m[K
      |                                                                              [01;31m[K|[m[K
      |                                                                              [01;31m[Kconst char [1][m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:136:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
  136 |     std::[01;31m[Kmutex[m[K mutex;
      |          [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
    6 | #include <chrono>
  +++ |+[32m[K#include <mutex>[m[K
    7 | #include <cstdint>
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:137:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kcondition_variable[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
  137 |     std::[01;31m[Kcondition_variable[m[K cv;
      |          [01;31m[K^~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::condition_variable[m[K’ is defined in header ‘[01m[K<condition_variable>[m[K’; did you forget to ‘[01m[K#include <condition_variable>[m[K’?
    6 | #include <chrono>
  +++ |+[32m[K#include <condition_variable>[m[K
    7 | #include <cstdint>
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In constructor ‘[01m[Ksl_pmu::common::ResponseWaiter::[01;32m[KResponseWaiter[m[K()[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kinvalid initialization of reference of type ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’} from expression of type ‘[01m[Kconst char [1][m[K’
   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
      |                                                                              [01;31m[K^~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:72:[m[K [01;36m[Knote: [m[Kin passing argument 2 of ‘[01m[Ksl_pmu::common::AsyncResult::[01;32m[KAsyncResult[m[K(sl_pmu::common::ResultCode, const std::string&, uint32_t)[m[K’
   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, [01;36m[Kconst std::string& msg = ""[m[K, uint32_t d = 0)
      |                                                     [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kbool sl_pmu::common::ResponseWaiter::[01;32m[KwaitForResponse[m[K(std::chrono::milliseconds)[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;31m[Kerror: [m[K‘[01m[Kunique_lock[m[K’ is not a member of ‘[01m[Kstd[m[K’
  145 |         std::[01;31m[Kunique_lock[m[K<std::mutex> lock(mutex);
      |              [01;31m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::unique_lock[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
  145 |         std::unique_lock<std::[01;31m[Kmutex[m[K> lock(mutex);
      |                               [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:43:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
  145 |         std::unique_lock<std::mutex> lock([01;31m[Kmutex[m[K);
      |                                           [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:38:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
  145 |         std::unique_lock<std::mutex> [01;31m[Klock[m[K(mutex);
      |                                      [01;31m[K^~~~[m[K
      |                                      [32m[Kclock[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:146:16:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
  146 |         return [01;31m[Kcv[m[K.wait_for(lock, timeout, [this] { return response_received; });
      |                [01;31m[K^~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kvoid sl_pmu::common::ResponseWaiter::[01;32m[KsignalResponse[m[K(const sl_pmu::common::AsyncResult&)[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;31m[Kerror: [m[K‘[01m[Klock_guard[m[K’ is not a member of ‘[01m[Kstd[m[K’
  151 |         std::[01;31m[Klock_guard[m[K<std::mutex> lock(mutex);
      |              [01;31m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::lock_guard[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
  151 |         std::lock_guard<std::[01;31m[Kmutex[m[K> lock(mutex);
      |                              [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:42:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
  151 |         std::lock_guard<std::mutex> lock([01;31m[Kmutex[m[K);
      |                                          [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:37:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
  151 |         std::lock_guard<std::mutex> [01;31m[Klock[m[K(mutex);
      |                                     [01;31m[K^~~~[m[K
      |                                     [32m[Kclock[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:154:9:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
  154 |         [01;31m[Kcv[m[K.notify_one();
      |         [01;31m[K^~[m[K
gmake[2]: *** [CMakeFiles/sl_pmu_common.dir/build.make:76: CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:149: CMakeFiles/sl_pmu_common.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
