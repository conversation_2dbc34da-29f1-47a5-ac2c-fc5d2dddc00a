[0.000000] (-) TimerEvent: {}
[0.000627] (-) JobUnselected: {'identifier': 'sl_vcu_all'}
[0.000836] (sl_pmu) JobQueued: {'identifier': 'sl_pmu', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'})}
[0.001005] (sl_pmu) JobStarted: {'identifier': 'sl_pmu'}
[0.012418] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'cmake'}
[0.013441] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 60643 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src', 'SSH_TTY': '/dev/pts/4', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '4103', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:11.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 60643 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.032387] (sl_pmu) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.099629] (-) TimerEvent: {}
[0.199914] (-) TimerEvent: {}
[0.281247] (sl_pmu) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[0.300028] (-) TimerEvent: {}
[0.314874] (sl_pmu) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[0.325515] (sl_pmu) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[0.340627] (sl_pmu) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.356794] (sl_pmu) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.400151] (-) TimerEvent: {}
[0.432914] (sl_pmu) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[0.434999] (sl_pmu) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[0.500277] (-) TimerEvent: {}
[0.516898] (sl_pmu) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.596953] (sl_pmu) StdoutLine: {'line': b'-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)\n'}
[0.600381] (-) TimerEvent: {}
[0.613290] (sl_pmu) StdoutLine: {'line': b'-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)\n'}
[0.630915] (sl_pmu) StdoutLine: {'line': b'-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)\n'}
[0.665836] (sl_pmu) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[0.694626] (sl_pmu) StdoutLine: {'line': b'-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)\n'}
[0.699332] (sl_pmu) StdoutLine: {'line': b'-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)\n'}
[0.700511] (-) TimerEvent: {}
[0.800747] (-) TimerEvent: {}
[0.839964] (sl_pmu) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)\n'}
[0.844646] (sl_pmu) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)\n'}
[0.846494] (sl_pmu) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.848920] (sl_pmu) StdoutLine: {'line': b'-- Found sl_vcu_all: 0.0.1 (/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake)\n'}
[0.900868] (-) TimerEvent: {}
[0.912188] (sl_pmu) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[0.913284] (sl_pmu) StdoutLine: {'line': b'-- Found ament_cmake_gtest: 2.5.4 (/opt/ros/jazzy/share/ament_cmake_gtest/cmake)\n'}
[1.000997] (-) TimerEvent: {}
[1.017407] (sl_pmu) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.020927] (sl_pmu) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.022158] (sl_pmu) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include\n'}
[1.022307] (sl_pmu) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.025295] (sl_pmu) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.025457] (sl_pmu) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.026203] (sl_pmu) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.026365] (sl_pmu) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[1.027364] (sl_pmu) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.028202] (sl_pmu) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.031276] (sl_pmu) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.031426] (sl_pmu) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.032135] (sl_pmu) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.033221] (sl_pmu) StdoutLine: {'line': b'-- Configuring done (1.0s)\n'}
[1.089765] (sl_pmu) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[1.099765] (sl_pmu) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu\n'}
[1.101104] (-) TimerEvent: {}
[1.110712] (sl_pmu) CommandEnded: {'returncode': 0}
[1.112419] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'build'}
[1.112656] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 60643 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src', 'SSH_TTY': '/dev/pts/4', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '4103', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:11.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 60643 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[1.177200] (sl_pmu) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o\x1b[0m\n'}
[1.201235] (-) TimerEvent: {}
[1.301527] (-) TimerEvent: {}
[1.401806] (-) TimerEvent: {}
[1.502093] (-) TimerEvent: {}
[1.580046] (sl_pmu) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/common/config_manager.cpp:1\x1b[m\x1b[K:\n'}
[1.580713] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:30:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kfield \xe2\x80\x98\x1b[01m\x1b[Kmessage\x1b[m\x1b[K\xe2\x80\x99 has incomplete type \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99}\n'}
[1.580821] (sl_pmu) StderrLine: {'line': b'   30 |     std::string \x1b[01;31m\x1b[Kmessage\x1b[m\x1b[K;\n'}
[1.580912] (sl_pmu) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[1.580997] (sl_pmu) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/usr/include/c++/13/iosfwd:41\x1b[m\x1b[K,\n'}
[1.581080] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/usr/include/c++/13/bits/shared_ptr.h:52\x1b[m\x1b[K,\n'}
[1.581162] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/usr/include/c++/13/memory:80\x1b[m\x1b[K,\n'}
[1.581245] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:5\x1b[m\x1b[K:\n'}
[1.581326] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/13/bits/stringfwd.h:72:11:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclaration of \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kclass std::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99}\n'}
[1.581418] (sl_pmu) StderrLine: {'line': b'   72 |     class \x1b[01;36m\x1b[Kbasic_string\x1b[m\x1b[K;\n'}
[1.581537] (sl_pmu) StderrLine: {'line': b'      |           \x1b[01;36m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.581622] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kcould not convert \xe2\x80\x98\x1b[01m\x1b[K""\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kconst char [1]\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kconst std::string&\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kconst std::__cxx11::basic_string<char>&\x1b[m\x1b[K\xe2\x80\x99}\n'}
[1.581719] (sl_pmu) StderrLine: {'line': b'   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = \x1b[01;31m\x1b[K""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[1.581806] (sl_pmu) StderrLine: {'line': b'      |                                                                              \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.581912] (sl_pmu) StderrLine: {'line': b'      |                                                                              \x1b[01;31m\x1b[K|\x1b[m\x1b[K\n'}
[1.582014] (sl_pmu) StderrLine: {'line': b'      |                                                                              \x1b[01;31m\x1b[Kconst char [1]\x1b[m\x1b[K\n'}
[1.583243] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:136:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 in namespace \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.583782] (sl_pmu) StderrLine: {'line': b'  136 |     std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K mutex;\n'}
[1.583943] (sl_pmu) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.584038] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.584132] (sl_pmu) StderrLine: {'line': b'    6 | #include <chrono>\n'}
[1.584219] (sl_pmu) StderrLine: {'line': b'  +++ |+\x1b[32m\x1b[K#include <mutex>\x1b[m\x1b[K\n'}
[1.584303] (sl_pmu) StderrLine: {'line': b'    7 | #include <cstdint>\n'}
[1.584389] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:137:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcondition_variable\x1b[m\x1b[K\xe2\x80\x99 in namespace \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.584484] (sl_pmu) StderrLine: {'line': b'  137 |     std::\x1b[01;31m\x1b[Kcondition_variable\x1b[m\x1b[K cv;\n'}
[1.584569] (sl_pmu) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.584653] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::condition_variable\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<condition_variable>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <condition_variable>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.584739] (sl_pmu) StderrLine: {'line': b'    6 | #include <chrono>\n'}
[1.584821] (sl_pmu) StderrLine: {'line': b'  +++ |+\x1b[32m\x1b[K#include <condition_variable>\x1b[m\x1b[K\n'}
[1.584904] (sl_pmu) StderrLine: {'line': b'    7 | #include <cstdint>\n'}
[1.584986] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KResponseWaiter\x1b[m\x1b[K()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.585070] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kinvalid initialization of reference of type \xe2\x80\x98\x1b[01m\x1b[Kconst std::string&\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kconst std::__cxx11::basic_string<char>&\x1b[m\x1b[K\xe2\x80\x99} from expression of type \xe2\x80\x98\x1b[01m\x1b[Kconst char [1]\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.585159] (sl_pmu) StderrLine: {'line': b'   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, const std::string& msg = \x1b[01;31m\x1b[K""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[1.585244] (sl_pmu) StderrLine: {'line': b'      |                                                                              \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.585328] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:72:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kin passing argument 2 of \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::common::AsyncResult::\x1b[01;32m\x1b[KAsyncResult\x1b[m\x1b[K(sl_pmu::common::ResultCode, const std::string&, uint32_t)\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.585433] (sl_pmu) StderrLine: {'line': b'   33 |     AsyncResult(ResultCode c = ResultCode::SUCCESS, \x1b[01;36m\x1b[Kconst std::string& msg = ""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[1.585528] (sl_pmu) StderrLine: {'line': b'      |                                                     \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~^~~~~~~~\x1b[m\x1b[K\n'}
[1.585613] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool sl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KwaitForResponse\x1b[m\x1b[K(std::chrono::milliseconds)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.585699] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kunique_lock\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.585831] (sl_pmu) StderrLine: {'line': b'  145 |         std::\x1b[01;31m\x1b[Kunique_lock\x1b[m\x1b[K<std::mutex> lock(mutex);\n'}
[1.585921] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;31m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.586003] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::unique_lock\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.586086] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.586178] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K> lock(mutex);\n'}
[1.586261] (sl_pmu) StderrLine: {'line': b'      |                               \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.586342] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.586428] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:43:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[1.586540] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::mutex> lock(\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K);\n'}
[1.586633] (sl_pmu) StderrLine: {'line': b'      |                                           \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.587468] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:38:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kclock\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.587616] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::mutex> \x1b[01;31m\x1b[Klock\x1b[m\x1b[K(mutex);\n'}
[1.587707] (sl_pmu) StderrLine: {'line': b'      |                                      \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[1.587796] (sl_pmu) StderrLine: {'line': b'      |                                      \x1b[32m\x1b[Kclock\x1b[m\x1b[K\n'}
[1.589083] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:146:16:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcv\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[1.589247] (sl_pmu) StderrLine: {'line': b'  146 |         return \x1b[01;31m\x1b[Kcv\x1b[m\x1b[K.wait_for(lock, timeout, [this] { return response_received; });\n'}
[1.589445] (sl_pmu) StderrLine: {'line': b'      |                \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.589545] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid sl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KsignalResponse\x1b[m\x1b[K(const sl_pmu::common::AsyncResult&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.589633] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock_guard\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.589720] (sl_pmu) StderrLine: {'line': b'  151 |         std::\x1b[01;31m\x1b[Klock_guard\x1b[m\x1b[K<std::mutex> lock(mutex);\n'}
[1.589813] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[1.589893] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::lock_guard\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.589975] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.590058] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K> lock(mutex);\n'}
[1.590147] (sl_pmu) StderrLine: {'line': b'      |                              \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.590231] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.591357] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:42:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[1.591529] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::mutex> lock(\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K);\n'}
[1.591624] (sl_pmu) StderrLine: {'line': b'      |                                          \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.593139] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:37:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kclock\x1b[m\x1b[K\xe2\x80\x99?\n'}
[1.593287] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::mutex> \x1b[01;31m\x1b[Klock\x1b[m\x1b[K(mutex);\n'}
[1.593394] (sl_pmu) StderrLine: {'line': b'      |                                     \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[1.593512] (sl_pmu) StderrLine: {'line': b'      |                                     \x1b[32m\x1b[Kclock\x1b[m\x1b[K\n'}
[1.594940] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:154:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcv\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[1.595089] (sl_pmu) StderrLine: {'line': b'  154 |         \x1b[01;31m\x1b[Kcv\x1b[m\x1b[K.notify_one();\n'}
[1.595181] (sl_pmu) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.602206] (-) TimerEvent: {}
[1.699695] (sl_pmu) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/sl_pmu_common.dir/build.make:76: CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o] Error 1\n'}
[1.700187] (sl_pmu) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:149: CMakeFiles/sl_pmu_common.dir/all] Error 2\n'}
[1.700420] (sl_pmu) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[1.702331] (-) TimerEvent: {}
[1.702982] (sl_pmu) CommandEnded: {'returncode': 2}
[1.711969] (sl_pmu) JobEnded: {'identifier': 'sl_pmu', 'rc': 2}
[1.722869] (-) EventReactorShutdown: {}
