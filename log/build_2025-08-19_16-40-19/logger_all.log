[0.120s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'sl_pmu']
[0.121s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['sl_pmu'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7c253ccb1fd0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c253ccb1c10>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c253ccb1c10>>, mixin_verb=('build',))
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.162s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_pmu) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_pmu' with type 'ros.ament_cmake' and name 'sl_pmu'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.198s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.218s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sl_vcu_all' in 'src/sl_vcu_all'
[0.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.222s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.224s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_args' from command line to 'None'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_target' from command line to 'None'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_clean_cache' from command line to 'False'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_clean_first' from command line to 'False'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'cmake_force_configure' from command line to 'False'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'ament_cmake_args' from command line to 'None'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'catkin_cmake_args' from command line to 'None'
[0.283s] Level 5:colcon.colcon_core.verb:set package 'sl_pmu' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.283s] DEBUG:colcon.colcon_core.verb:Building package 'sl_pmu' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu', 'symlink_install': False, 'test_result_base': None}
[0.283s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.284s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.284s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu' with build type 'ament_cmake'
[0.285s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu'
[0.287s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.287s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.288s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.301s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[1.625s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[1.625s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_pmu)
[1.628s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu' for CMake module files
[1.628s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu' for CMake config files
[1.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/bin'
[1.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/pkgconfig/sl_pmu.pc'
[1.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/python3.12/site-packages'
[1.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/bin'
[1.629s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.ps1'
[1.630s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv'
[1.631s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.sh'
[1.631s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.bash'
[1.632s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.zsh'
[1.633s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/colcon-core/packages/sl_pmu)
[1.644s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.644s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.644s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[1.644s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.650s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.650s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.650s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.668s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.669s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[1.670s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[1.671s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[1.672s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[1.673s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[1.674s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[1.675s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[1.675s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[1.676s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[1.677s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
