/usr/bin/ld: libsl_pmu_driver.a(communication_driver_factory.cpp.o): in function `std::__detail::_MakeUniq<sl_pmu::driver::SerialDriver>::__single_object std::make_unique<sl_pmu::driver::SerialDriver>()':
communication_driver_factory.cpp:(.text._ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_[_ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_]+0x2e): undefined reference to `sl_pmu::driver::SerialDriver::SerialDriver()'
collect2: error: ld returned 1 exit status
gmake[2]: *** [CMakeFiles/pmu_node.dir/build.make:239: pmu_node] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:320: CMakeFiles/pmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
