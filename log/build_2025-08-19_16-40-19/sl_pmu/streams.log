[0.016s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.086s] [ 11%] Built target sl_pmu_common
[0.118s] [ 27%] Built target sl_pmu_driver
[0.151s] [ 50%] Built target sl_pmu_protocol
[0.186s] [ 66%] Built target sl_pmu_device
[0.217s] [ 77%] Built target sl_pmu_application
[0.256s] [ 88%] Built target sl_pmu_ros_node
[0.295s] [ 94%] [32m[1mLinking CXX executable pmu_node[0m
[1.253s] /usr/bin/ld: libsl_pmu_driver.a(communication_driver_factory.cpp.o): in function `std::__detail::_MakeUniq<sl_pmu::driver::SerialDriver>::__single_object std::make_unique<sl_pmu::driver::SerialDriver>()':
[1.253s] communication_driver_factory.cpp:(.text._ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_[_ZSt11make_uniqueIN6sl_pmu6driver12SerialDriverEJEENSt8__detail9_MakeUniqIT_E15__single_objectEDpOT0_]+0x2e): undefined reference to `sl_pmu::driver::SerialDriver::SerialDriver()'
[1.334s] collect2: error: ld returned 1 exit status
[1.336s] gmake[2]: *** [CMakeFiles/pmu_node.dir/build.make:239: pmu_node] Error 1
[1.336s] gmake[1]: *** [CMakeFiles/Makefile2:320: CMakeFiles/pmu_node.dir/all] Error 2
[1.336s] gmake: *** [Makefile:146: all] Error 2
[1.339s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
