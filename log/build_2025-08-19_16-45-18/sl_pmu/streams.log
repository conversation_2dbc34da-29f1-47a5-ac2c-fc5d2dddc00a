[0.016s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.084s] [ 10%] Built target sl_pmu_common
[0.118s] [ 31%] Built target sl_pmu_driver
[0.147s] [ 36%] [32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o[0m
[0.701s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:[m[K In constructor ‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::[01;32m[KImpl[m[K()[m[K’:
[0.701s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:401:23:[m[K [01;35m[Kwarning: [m[K‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[0.701s]   401 |     std::atomic<bool> [01;35m[Kinitialized_[m[K;
[0.701s]       |                       [01;35m[K^~~~~~~~~~~~[m[K
[0.701s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:399:14:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[0.702s]   399 |     uint32_t [01;35m[Kcan_id_tx_[m[K;
[0.702s]       |              [01;35m[K^~~~~~~~~~[m[K
[0.702s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
[0.702s]    52 |     [01;35m[KImpl[m[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}
[0.702s]       |     [01;35m[K^~~~[m[K
[1.233s] [ 42%] [32m[1mLinking CXX static library libsl_pmu_protocol.a[0m
[1.286s] [ 52%] Built target sl_pmu_protocol
[1.314s] [ 68%] Built target sl_pmu_device
[1.342s] [ 78%] Built target sl_pmu_application
[1.377s] [ 89%] Built target sl_pmu_ros_node
[1.411s] [ 94%] [32m[1mLinking CXX executable pmu_node[0m
[2.495s] [100%] Built target pmu_node
[2.510s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[2.523s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
[2.534s] -- Install configuration: ""
[2.534s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_common.a
[2.534s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_driver.a
[2.534s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_protocol.a
[2.535s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_device.a
[2.535s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_application.a
[2.536s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_ros_node.a
[2.536s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node
[2.552s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node" to ""
[2.552s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include/
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node/pmu_ros_node.hpp
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol/communication_protocol.hpp
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver
[2.553s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver/communication_driver.hpp
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application/pmu_application.hpp
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/device_interface.hpp
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/motor_controller.hpp
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common/types.hpp
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config/
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config//pmu_config.yaml
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch/
[2.554s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch//pmu.launch.py
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/doc/
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/package_run_dependencies/sl_pmu
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/parent_prefix_path/sl_pmu
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.sh
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.dsv
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.sh
[2.555s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.dsv
[2.556s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.bash
[2.556s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.sh
[2.556s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.zsh
[2.557s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.dsv
[2.557s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv
[2.557s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/packages/sl_pmu
[2.557s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig.cmake
[2.557s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig-version.cmake
[2.557s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.xml
[2.558s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu
