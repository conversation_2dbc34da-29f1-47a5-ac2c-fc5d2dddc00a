[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:[m[K In constructor ‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::[01;32m[KImpl[m[K()[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:401:23:[m[K [01;35m[Kwarning: [m[K‘[01m[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_[m[K’ will be initialized after [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
  401 |     std::atomic<bool> [01;35m[Kinitialized_[m[K;
      |                       [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:399:14:[m[K [01;35m[Kwarning: [m[K  ‘[01m[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
  399 |     uint32_t [01;35m[Kcan_id_tx_[m[K;
      |              [01;35m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:[m[K [01;35m[Kwarning: [m[K  when initialized here [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder-Wreorder]8;;[m[K]
   52 |     [01;35m[KImpl[m[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}
      |     [01;35m[K^~~~[m[K
