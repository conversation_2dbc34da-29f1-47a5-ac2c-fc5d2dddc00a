[0.000000] (-) TimerEvent: {}
[0.000823] (-) JobUnselected: {'identifier': 'sl_vcu_all'}
[0.001026] (sl_pmu) JobQueued: {'identifier': 'sl_pmu', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'})}
[0.001101] (sl_pmu) JobStarted: {'identifier': 'sl_pmu'}
[0.013100] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'cmake'}
[0.014028] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'build'}
[0.014795] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.085296] (sl_pmu) StdoutLine: {'line': b'[ 10%] Built target sl_pmu_common\n'}
[0.099691] (-) TimerEvent: {}
[0.118829] (sl_pmu) StdoutLine: {'line': b'[ 31%] Built target sl_pmu_driver\n'}
[0.147924] (sl_pmu) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_protocol.dir/src/protocol/canopen_sdo_protocol.cpp.o\x1b[0m\n'}
[0.199816] (-) TimerEvent: {}
[0.300123] (-) TimerEvent: {}
[0.400423] (-) TimerEvent: {}
[0.500714] (-) TimerEvent: {}
[0.601012] (-) TimerEvent: {}
[0.701298] (-) TimerEvent: {}
[0.702091] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::\x1b[01;32m\x1b[KImpl\x1b[m\x1b[K()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.702343] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:401:23:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::protocol::CanopenSdoProtocol::Impl::initialized_\x1b[m\x1b[K\xe2\x80\x99 will be initialized after [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.702445] (sl_pmu) StderrLine: {'line': b'  401 |     std::atomic<bool> \x1b[01;35m\x1b[Kinitialized_\x1b[m\x1b[K;\n'}
[0.702530] (sl_pmu) StderrLine: {'line': b'      |                       \x1b[01;35m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.702612] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:399:14:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  \xe2\x80\x98\x1b[01m\x1b[Kuint32_t sl_pmu::protocol::CanopenSdoProtocol::Impl::can_id_tx_\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.702694] (sl_pmu) StderrLine: {'line': b'  399 |     uint32_t \x1b[01;35m\x1b[Kcan_id_tx_\x1b[m\x1b[K;\n'}
[0.702778] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[0.702858] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/protocol/canopen_sdo_protocol.cpp:52:5:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K  when initialized here [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wreorder\x07-Wreorder\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.702941] (sl_pmu) StderrLine: {'line': b'   52 |     \x1b[01;35m\x1b[KImpl\x1b[m\x1b[K() : node_id_(1), initialized_(false), can_id_tx_(0x600), can_id_rx_(0x580), running_(false) {}\n'}
[0.703019] (sl_pmu) StderrLine: {'line': b'      |     \x1b[01;35m\x1b[K^~~~\x1b[m\x1b[K\n'}
[0.801430] (-) TimerEvent: {}
[0.901729] (-) TimerEvent: {}
[1.002032] (-) TimerEvent: {}
[1.102343] (-) TimerEvent: {}
[1.202634] (-) TimerEvent: {}
[1.233601] (sl_pmu) StdoutLine: {'line': b'[ 42%] \x1b[32m\x1b[1mLinking CXX static library libsl_pmu_protocol.a\x1b[0m\n'}
[1.286709] (sl_pmu) StdoutLine: {'line': b'[ 52%] Built target sl_pmu_protocol\n'}
[1.302764] (-) TimerEvent: {}
[1.315276] (sl_pmu) StdoutLine: {'line': b'[ 68%] Built target sl_pmu_device\n'}
[1.343288] (sl_pmu) StdoutLine: {'line': b'[ 78%] Built target sl_pmu_application\n'}
[1.377579] (sl_pmu) StdoutLine: {'line': b'[ 89%] Built target sl_pmu_ros_node\n'}
[1.402924] (-) TimerEvent: {}
[1.412395] (sl_pmu) StdoutLine: {'line': b'[ 94%] \x1b[32m\x1b[1mLinking CXX executable pmu_node\x1b[0m\n'}
[1.503057] (-) TimerEvent: {}
[1.603610] (-) TimerEvent: {}
[1.703941] (-) TimerEvent: {}
[1.804285] (-) TimerEvent: {}
[1.904612] (-) TimerEvent: {}
[2.004937] (-) TimerEvent: {}
[2.105265] (-) TimerEvent: {}
[2.205585] (-) TimerEvent: {}
[2.305884] (-) TimerEvent: {}
[2.406175] (-) TimerEvent: {}
[2.496090] (sl_pmu) StdoutLine: {'line': b'[100%] Built target pmu_node\n'}
[2.506241] (-) TimerEvent: {}
[2.510819] (sl_pmu) CommandEnded: {'returncode': 0}
[2.511644] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'install'}
[2.523961] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[2.534744] (sl_pmu) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[2.535385] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_common.a\n'}
[2.535549] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_driver.a\n'}
[2.535647] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_protocol.a\n'}
[2.536277] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_device.a\n'}
[2.536626] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_application.a\n'}
[2.536733] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/libsl_pmu_ros_node.a\n'}
[2.536878] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node\n'}
[2.553028] (sl_pmu) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/lib/sl_pmu/pmu_node" to ""\n'}
[2.553386] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include/\n'}
[2.553696] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu\n'}
[2.553868] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node\n'}
[2.554038] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/ros_node/pmu_ros_node.hpp\n'}
[2.554200] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol\n'}
[2.554345] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/protocol/communication_protocol.hpp\n'}
[2.554471] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver\n'}
[2.554559] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/driver/communication_driver.hpp\n'}
[2.554684] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application\n'}
[2.554806] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/application/pmu_application.hpp\n'}
[2.554893] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device\n'}
[2.554979] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/device_interface.hpp\n'}
[2.555082] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/device/motor_controller.hpp\n'}
[2.555183] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common\n'}
[2.555272] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/include//sl_pmu/common/types.hpp\n'}
[2.555357] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config/\n'}
[2.555442] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/config//pmu_config.yaml\n'}
[2.555523] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch/\n'}
[2.555609] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/launch//pmu.launch.py\n'}
[2.555691] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/doc/\n'}
[2.555778] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/package_run_dependencies/sl_pmu\n'}
[2.555865] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/parent_prefix_path/sl_pmu\n'}
[2.555952] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.sh\n'}
[2.556042] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/ament_prefix_path.dsv\n'}
[2.556129] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.sh\n'}
[2.556314] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/environment/path.dsv\n'}
[2.556542] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.bash\n'}
[2.556863] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.sh\n'}
[2.557445] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.zsh\n'}
[2.557728] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/local_setup.dsv\n'}
[2.557911] (sl_pmu) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.dsv\n'}
[2.558062] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/ament_index/resource_index/packages/sl_pmu\n'}
[2.558274] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig.cmake\n'}
[2.558434] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/cmake/sl_pmuConfig-version.cmake\n'}
[2.558630] (sl_pmu) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu/share/sl_pmu/package.xml\n'}
[2.558747] (sl_pmu) CommandEnded: {'returncode': 0}
[2.576171] (sl_pmu) JobEnded: {'identifier': 'sl_pmu', 'rc': 0}
[2.577183] (-) EventReactorShutdown: {}
