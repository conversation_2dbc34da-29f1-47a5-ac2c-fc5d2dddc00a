[ 25%] [32mBuilding CXX object CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o[0m
[ 50%] [32m[1mLinking CXX static library libslpmu_jack.a[0m
[ 50%] Built target slpmu_jack
[ 75%] [32mBuilding CXX object CMakeFiles/jack_utils.dir/src/jack_utils.cpp.o[0m
[100%] [32m[1mLinking CXX executable jack_utils[0m
[100%] Built target jack_utils
-- Install configuration: "RelWithDebInfo"
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp
