[ 50%] Built target slpmu_motor
[100%] Built target motor_utils
-- Install configuration: "RelWithDebInfo"
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-relwithdebinfo.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils
