[0.012s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan
[0.032s] -- Configuring done (0.0s)
[0.036s] -- Generating done (0.0s)
[0.038s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.040s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan
[0.042s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.109s] [ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/canopen.cpp.o[0m
[0.109s] [ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/socket_can.cpp.o[0m
[0.624s] [100%] [32m[1mLinking CXX static library libslcan.a[0m
[0.668s] [100%] Built target slcan
[0.683s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.695s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.706s] -- Install configuration: "RelWithDebInfo"
[0.706s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp
[0.707s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp
[0.708s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake
[0.708s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake
[0.708s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake
[0.710s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
