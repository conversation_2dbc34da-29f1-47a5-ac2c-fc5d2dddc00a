-- Configuring done (0.0s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/canopen.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/slcan.dir/src/socket_can.cpp.o[0m
[100%] [32m[1mLinking CXX static library libslcan.a[0m
[100%] Built target slcan
-- Install configuration: "RelWithDebInfo"
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake
