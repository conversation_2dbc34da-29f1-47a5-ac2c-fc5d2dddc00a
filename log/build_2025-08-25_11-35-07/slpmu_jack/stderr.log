In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp:1[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::can_interface[m[K’ required before the end of its enclosing class
   20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
      |                                                           [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:15:35:[m[K [01;36m[Knote: [m[Kdefined here
   15 |         std::string can_interface [01;36m[K{"can0"};[m[K
      |                                   [01;36m[K^~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::node_id[m[K’ required before the end of its enclosing class
   20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
      |                                                           [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:16:31:[m[K [01;36m[Knote: [m[Kdefined here
   16 |         std::uint16_t node_id [01;36m[K{0x07};[m[K // jack BK seems to use request COB-ID 0x607
      |                               [01;36m[K^~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::sdo_timeout_ns[m[K’ required before the end of its enclosing class
   20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
      |                                                           [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:17:37:[m[K [01;36m[Knote: [m[Kdefined here
   17 |         std::int64_t sdo_timeout_ns [01;36m[K{200'000'000};[m[K // 200ms
      |                                     [01;36m[K^~~~~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/slpmu_jack.dir/build.make:76: CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:85: CMakeFiles/slpmu_jack.dir/all] Error 2
gmake: *** [Makefile:136: all] Error 2
