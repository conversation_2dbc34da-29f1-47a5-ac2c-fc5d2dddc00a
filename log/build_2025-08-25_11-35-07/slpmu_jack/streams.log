[0.011s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack
[0.080s] -- The C compiler identification is GNU 13.3.0
[0.153s] -- The CXX compiler identification is GNU 13.3.0
[0.174s] -- Detecting C compiler ABI info
[0.285s] -- Detecting C compiler ABI info - done
[0.297s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.297s] -- Detecting C compile features
[0.298s] -- Detecting C compile features - done
[0.309s] -- Detecting CXX compiler ABI info
[0.463s] -- Detecting CXX compiler ABI info - done
[0.478s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.479s] -- Detecting CXX compile features
[0.480s] -- Detecting CXX compile features - done
[0.482s] -- Configuring done (0.5s)
[0.491s] -- Generating done (0.0s)
[0.494s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.502s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack
[0.503s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.567s] [ 25%] [32mBuilding CXX object CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o[0m
[0.865s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp:1[m[K:
[0.865s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::can_interface[m[K’ required before the end of its enclosing class
[0.865s]    20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
[0.865s]       |                                                           [01;31m[K^[m[K
[0.866s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:15:35:[m[K [01;36m[Knote: [m[Kdefined here
[0.866s]    15 |         std::string can_interface [01;36m[K{"can0"};[m[K
[0.866s]       |                                   [01;36m[K^~~~~~~~~[m[K
[0.866s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::node_id[m[K’ required before the end of its enclosing class
[0.866s]    20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
[0.866s]       |                                                           [01;31m[K^[m[K
[0.866s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:16:31:[m[K [01;36m[Knote: [m[Kdefined here
[0.866s]    16 |         std::uint16_t node_id [01;36m[K{0x07};[m[K // jack BK seems to use request COB-ID 0x607
[0.866s]       |                               [01;36m[K^~~~~~~[m[K
[0.866s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:20:59:[m[K [01;31m[Kerror: [m[Kdefault member initializer for ‘[01m[Kslpmu::JackControllerCan::Config::sdo_timeout_ns[m[K’ required before the end of its enclosing class
[0.867s]    20 |     explicit JackControllerCan(const Config& cfg = Config([01;31m[K)[m[K);
[0.867s]       |                                                           [01;31m[K^[m[K
[0.867s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp:17:37:[m[K [01;36m[Knote: [m[Kdefined here
[0.867s]    17 |         std::int64_t sdo_timeout_ns [01;36m[K{200'000'000};[m[K // 200ms
[0.867s]       |                                     [01;36m[K^~~~~~~~~~~~~~[m[K
[0.989s] gmake[2]: *** [CMakeFiles/slpmu_jack.dir/build.make:76: CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o] Error 1
[0.990s] gmake[1]: *** [CMakeFiles/Makefile2:85: CMakeFiles/slpmu_jack.dir/all] Error 2
[0.990s] gmake: *** [Makefile:136: all] Error 2
[0.993s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
