-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[ 25%] [32mBuilding CXX object CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o[0m
[ 50%] [32m[1mLinking CXX static library libslpmu_motor.a[0m
[ 50%] Built target slpmu_motor
[ 75%] [32mBuilding CXX object CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o[0m
[100%] [32m[1mLinking CXX executable motor_utils[0m
[100%] Built target motor_utils
