[0.176s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'sl_vcu_all']
[0.177s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['sl_vcu_all'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7c5a8e1a1fd0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c5a8eb779e0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c5a8eb779e0>>, mixin_verb=('build',))
[0.248s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.248s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.248s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.248s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.248s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.249s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.249s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.282s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.283s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.284s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.285s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.289s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.289s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.289s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.290s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.290s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.290s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.313s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.313s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.319s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.321s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_args' from command line to 'None'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target' from command line to 'None'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_cache' from command line to 'False'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_first' from command line to 'False'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_force_configure' from command line to 'False'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'ament_cmake_args' from command line to 'None'
[0.381s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_cmake_args' from command line to 'None'
[0.382s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.382s] DEBUG:colcon.colcon_core.verb:Building package 'sl_vcu_all' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'symlink_install': False, 'test_result_base': None}
[0.382s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.383s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.383s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all' with build type 'ament_cmake'
[0.383s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all'
[0.387s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.387s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.388s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.399s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[26.974s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[26.987s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[27.103s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[27.103s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[27.106s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[27.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[27.108s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[27.108s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[27.109s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[27.109s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[27.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[27.110s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[27.110s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[27.111s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[27.111s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[27.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[27.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[27.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[27.112s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[27.112s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[27.113s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[27.113s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[27.114s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[27.114s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[27.115s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[27.116s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[27.116s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[27.117s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[27.118s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[27.119s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[27.119s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[27.120s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[27.121s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[27.121s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[27.122s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[27.122s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[27.123s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[27.123s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[27.123s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[27.124s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[27.124s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[27.125s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[27.125s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[27.125s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[27.126s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[27.126s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[27.126s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[27.127s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[27.128s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[27.128s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[27.129s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[27.129s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[27.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[27.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[27.131s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[27.131s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[27.132s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[27.132s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[27.132s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[27.138s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[27.138s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[27.138s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[27.150s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[27.150s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[27.151s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[27.153s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[27.154s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[27.154s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[27.155s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[27.156s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[27.157s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[27.158s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[27.158s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
