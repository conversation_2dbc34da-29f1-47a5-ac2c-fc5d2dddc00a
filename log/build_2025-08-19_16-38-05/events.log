[0.000000] (-) TimerEvent: {}
[0.000917] (-) JobUnselected: {'identifier': 'sl_vcu_all'}
[0.001026] (sl_pmu) JobQueued: {'identifier': 'sl_pmu', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all'})}
[0.001124] (sl_pmu) JobStarted: {'identifier': 'sl_pmu'}
[0.012955] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'cmake'}
[0.013878] (sl_pmu) JobProgress: {'identifier': 'sl_pmu', 'progress': 'build'}
[0.014615] (sl_pmu) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 61093 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh', 'LESS': '-FX', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws', 'TERM_PROGRAM_VERSION': '1.103.1', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-33b72314-873d-4935-a9fb-303a34ddaa3a.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js', 'PS1': '\\[\x1b]633;A\x07\\]\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$ \\[\x1b]633;B\x07\\]', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/cert.pem', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '4107', 'PATH': '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/remote-cli:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-6d9f5595481f9fb1.txt', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-f0e82be88f.sock', 'TERM_PROGRAM': 'vscode', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 61093 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.086232] (sl_pmu) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o\x1b[0m\n'}
[0.099656] (-) TimerEvent: {}
[0.199957] (-) TimerEvent: {}
[0.300276] (-) TimerEvent: {}
[0.400573] (-) TimerEvent: {}
[0.476548] (sl_pmu) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/common/config_manager.cpp:1\x1b[m\x1b[K:\n'}
[0.476947] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:30:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kfield \xe2\x80\x98\x1b[01m\x1b[Kmessage\x1b[m\x1b[K\xe2\x80\x99 has incomplete type \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99}\n'}
[0.477078] (sl_pmu) StderrLine: {'line': b'   30 |     std::string \x1b[01;31m\x1b[Kmessage\x1b[m\x1b[K;\n'}
[0.477167] (sl_pmu) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.477253] (sl_pmu) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/usr/include/c++/13/iosfwd:41\x1b[m\x1b[K,\n'}
[0.477338] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/usr/include/c++/13/bits/shared_ptr.h:52\x1b[m\x1b[K,\n'}
[0.477421] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/usr/include/c++/13/memory:80\x1b[m\x1b[K,\n'}
[0.477521] (sl_pmu) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:5\x1b[m\x1b[K:\n'}
[0.477605] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/13/bits/stringfwd.h:72:11:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclaration of \xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kclass std::__cxx11::basic_string<char>\x1b[m\x1b[K\xe2\x80\x99}\n'}
[0.477703] (sl_pmu) StderrLine: {'line': b'   72 |     class \x1b[01;36m\x1b[Kbasic_string\x1b[m\x1b[K;\n'}
[0.477784] (sl_pmu) StderrLine: {'line': b'      |           \x1b[01;36m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.477867] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kcould not convert \xe2\x80\x98\x1b[01m\x1b[K""\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kconst char [1]\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kconst std::string&\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kconst std::__cxx11::basic_string<char>&\x1b[m\x1b[K\xe2\x80\x99}\n'}
[0.477954] (sl_pmu) StderrLine: {'line': b'   33 | esultCode c = ResultCode::SUCCESS, const std::string& msg = \x1b[01;31m\x1b[K""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[0.478065] (sl_pmu) StderrLine: {'line': b'      |                                                             \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[0.478161] (sl_pmu) StderrLine: {'line': b'      |                                                             \x1b[01;31m\x1b[K|\x1b[m\x1b[K\n'}
[0.478245] (sl_pmu) StderrLine: {'line': b'      |                                                             \x1b[01;31m\x1b[Kconst char [1]\x1b[m\x1b[K\n'}
[0.478326] (sl_pmu) StderrLine: {'line': b'\n'}
[0.479639] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:136:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 in namespace \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[0.479925] (sl_pmu) StderrLine: {'line': b'  136 |     std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K mutex;\n'}
[0.480554] (sl_pmu) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.480665] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.480774] (sl_pmu) StderrLine: {'line': b'    6 | #include <chrono>\n'}
[0.480864] (sl_pmu) StderrLine: {'line': b'  +++ |+\x1b[32m\x1b[K#include <mutex>\x1b[m\x1b[K\n'}
[0.480953] (sl_pmu) StderrLine: {'line': b'    7 | #include <cstdint>\n'}
[0.481049] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:137:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcondition_variable\x1b[m\x1b[K\xe2\x80\x99 in namespace \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[0.481146] (sl_pmu) StderrLine: {'line': b'  137 |     std::\x1b[01;31m\x1b[Kcondition_variable\x1b[m\x1b[K cv;\n'}
[0.481234] (sl_pmu) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.481320] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::condition_variable\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<condition_variable>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <condition_variable>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.481410] (sl_pmu) StderrLine: {'line': b'    6 | #include <chrono>\n'}
[0.481494] (sl_pmu) StderrLine: {'line': b'  +++ |+\x1b[32m\x1b[K#include <condition_variable>\x1b[m\x1b[K\n'}
[0.481577] (sl_pmu) StderrLine: {'line': b'    7 | #include <cstdint>\n'}
[0.481660] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KResponseWaiter\x1b[m\x1b[K()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.481747] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kinvalid initialization of reference of type \xe2\x80\x98\x1b[01m\x1b[Kconst std::string&\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kconst std::__cxx11::basic_string<char>&\x1b[m\x1b[K\xe2\x80\x99} from expression of type \xe2\x80\x98\x1b[01m\x1b[Kconst char [1]\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.481835] (sl_pmu) StderrLine: {'line': b'   33 | esultCode c = ResultCode::SUCCESS, const std::string& msg = \x1b[01;31m\x1b[K""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[0.481922] (sl_pmu) StderrLine: {'line': b'      |                                                             \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[0.482007] (sl_pmu) StderrLine: {'line': b'\n'}
[0.482111] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:72:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kin passing argument 2 of \xe2\x80\x98\x1b[01m\x1b[Ksl_pmu::common::AsyncResult::\x1b[01;32m\x1b[KAsyncResult\x1b[m\x1b[K(sl_pmu::common::ResultCode, const std::string&, uint32_t)\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.482205] (sl_pmu) StderrLine: {'line': b'   33 | sult(ResultCode c = ResultCode::SUCCESS, \x1b[01;36m\x1b[Kconst std::string& msg = ""\x1b[m\x1b[K, uint32_t d = 0)\n'}
[0.482347] (sl_pmu) StderrLine: {'line': b'      |                                          \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~^~~~~~~~\x1b[m\x1b[K\n'}
[0.482525] (sl_pmu) StderrLine: {'line': b'\n'}
[0.482610] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool sl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KwaitForResponse\x1b[m\x1b[K(std::chrono::milliseconds)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.482698] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kunique_lock\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.482786] (sl_pmu) StderrLine: {'line': b'  145 |         std::\x1b[01;31m\x1b[Kunique_lock\x1b[m\x1b[K<std::mutex> lock(mutex);\n'}
[0.482872] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;31m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.482956] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::unique_lock\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.483048] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.483135] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K> lock(mutex);\n'}
[0.483220] (sl_pmu) StderrLine: {'line': b'      |                               \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.483304] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.483388] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:43:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.483473] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::mutex> lock(\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K);\n'}
[0.483553] (sl_pmu) StderrLine: {'line': b'      |                                           \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.483997] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:38:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kclock\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.484155] (sl_pmu) StderrLine: {'line': b'  145 |         std::unique_lock<std::mutex> \x1b[01;31m\x1b[Klock\x1b[m\x1b[K(mutex);\n'}
[0.484255] (sl_pmu) StderrLine: {'line': b'      |                                      \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[0.484346] (sl_pmu) StderrLine: {'line': b'      |                                      \x1b[32m\x1b[Kclock\x1b[m\x1b[K\n'}
[0.485617] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:146:16:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcv\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.485782] (sl_pmu) StderrLine: {'line': b'  146 |         return \x1b[01;31m\x1b[Kcv\x1b[m\x1b[K.wait_for(lock, timeout, [this] { return response_received; });\n'}
[0.485991] (sl_pmu) StderrLine: {'line': b'      |                \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[0.486106] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid sl_pmu::common::ResponseWaiter::\x1b[01;32m\x1b[KsignalResponse\x1b[m\x1b[K(const sl_pmu::common::AsyncResult&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.486197] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock_guard\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.486289] (sl_pmu) StderrLine: {'line': b'  151 |         std::\x1b[01;31m\x1b[Klock_guard\x1b[m\x1b[K<std::mutex> lock(mutex);\n'}
[0.486374] (sl_pmu) StderrLine: {'line': b'      |              \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[0.486456] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::lock_guard\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.486543] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.486625] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K> lock(mutex);\n'}
[0.486709] (sl_pmu) StderrLine: {'line': b'      |                              \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.486796] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::mutex\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<mutex>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <mutex>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.487757] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:42:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kmutex\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.487903] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::mutex> lock(\x1b[01;31m\x1b[Kmutex\x1b[m\x1b[K);\n'}
[0.487998] (sl_pmu) StderrLine: {'line': b'      |                                          \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.489449] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:37:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Klock\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kclock\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.489694] (sl_pmu) StderrLine: {'line': b'  151 |         std::lock_guard<std::mutex> \x1b[01;31m\x1b[Klock\x1b[m\x1b[K(mutex);\n'}
[0.489790] (sl_pmu) StderrLine: {'line': b'      |                                     \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[0.489876] (sl_pmu) StderrLine: {'line': b'      |                                     \x1b[32m\x1b[Kclock\x1b[m\x1b[K\n'}
[0.491264] (sl_pmu) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:154:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcv\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.491437] (sl_pmu) StderrLine: {'line': b'  154 |         \x1b[01;31m\x1b[Kcv\x1b[m\x1b[K.notify_one();\n'}
[0.491536] (sl_pmu) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[0.500698] (-) TimerEvent: {}
[0.600964] (-) TimerEvent: {}
[0.601352] (sl_pmu) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/sl_pmu_common.dir/build.make:76: CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o] Error 1\n'}
[0.601737] (sl_pmu) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:149: CMakeFiles/sl_pmu_common.dir/all] Error 2\n'}
[0.601944] (sl_pmu) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[0.604329] (sl_pmu) CommandEnded: {'returncode': 2}
[0.613681] (sl_pmu) JobEnded: {'identifier': 'sl_pmu', 'rc': 2}
[0.624226] (-) EventReactorShutdown: {}
