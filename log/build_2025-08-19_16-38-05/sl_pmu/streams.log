[0.015s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
[0.085s] [  5%] [32mBuilding CXX object CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o[0m
[0.476s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/src/common/config_manager.cpp:1[m[K:
[0.476s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:30:17:[m[K [01;31m[Kerror: [m[Kfield ‘[01m[Kmessage[m[K’ has incomplete type ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kstd::__cxx11::basic_string<char>[m[K’}
[0.476s]    30 |     std::string [01;31m[Kmessage[m[K;
[0.476s]       |                 [01;31m[K^~~~~~~[m[K
[0.476s] In file included from [01m[K/usr/include/c++/13/iosfwd:41[m[K,
[0.476s]                  from [01m[K/usr/include/c++/13/bits/shared_ptr.h:52[m[K,
[0.476s]                  from [01m[K/usr/include/c++/13/memory:80[m[K,
[0.476s]                  from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:5[m[K:
[0.477s] [01m[K/usr/include/c++/13/bits/stringfwd.h:72:11:[m[K [01;36m[Knote: [m[Kdeclaration of ‘[01m[Kstd::string[m[K’ {aka ‘[01m[Kclass std::__cxx11::basic_string<char>[m[K’}
[0.477s]    72 |     class [01;36m[Kbasic_string[m[K;
[0.477s]       |           [01;36m[K^~~~~~~~~~~~[m[K
[0.477s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kcould not convert ‘[01m[K""[m[K’ from ‘[01m[Kconst char [1][m[K’ to ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’}
[0.477s]    33 | esultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
[0.477s]       |                                                             [01;31m[K^~[m[K
[0.477s]       |                                                             [01;31m[K|[m[K
[0.477s]       |                                                             [01;31m[Kconst char [1][m[K
[0.477s] 
[0.479s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:136:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
[0.479s]   136 |     std::[01;31m[Kmutex[m[K mutex;
[0.479s]       |          [01;31m[K^~~~~[m[K
[0.480s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[0.480s]     6 | #include <chrono>
[0.480s]   +++ |+[32m[K#include <mutex>[m[K
[0.480s]     7 | #include <cstdint>
[0.480s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:137:10:[m[K [01;31m[Kerror: [m[K‘[01m[Kcondition_variable[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
[0.480s]   137 |     std::[01;31m[Kcondition_variable[m[K cv;
[0.480s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~[m[K
[0.480s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:7:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::condition_variable[m[K’ is defined in header ‘[01m[K<condition_variable>[m[K’; did you forget to ‘[01m[K#include <condition_variable>[m[K’?
[0.480s]     6 | #include <chrono>
[0.480s]   +++ |+[32m[K#include <condition_variable>[m[K
[0.480s]     7 | #include <cstdint>
[0.481s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In constructor ‘[01m[Ksl_pmu::common::ResponseWaiter::[01;32m[KResponseWaiter[m[K()[m[K’:
[0.481s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:78:[m[K [01;31m[Kerror: [m[Kinvalid initialization of reference of type ‘[01m[Kconst std::string&[m[K’ {aka ‘[01m[Kconst std::__cxx11::basic_string<char>&[m[K’} from expression of type ‘[01m[Kconst char [1][m[K’
[0.481s]    33 | esultCode c = ResultCode::SUCCESS, const std::string& msg = [01;31m[K""[m[K, uint32_t d = 0)
[0.481s]       |                                                             [01;31m[K^~[m[K
[0.481s] 
[0.481s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:33:72:[m[K [01;36m[Knote: [m[Kin passing argument 2 of ‘[01m[Ksl_pmu::common::AsyncResult::[01;32m[KAsyncResult[m[K(sl_pmu::common::ResultCode, const std::string&, uint32_t)[m[K’
[0.481s]    33 | sult(ResultCode c = ResultCode::SUCCESS, [01;36m[Kconst std::string& msg = ""[m[K, uint32_t d = 0)
[0.481s]       |                                          [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~~[m[K
[0.481s] 
[0.481s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kbool sl_pmu::common::ResponseWaiter::[01;32m[KwaitForResponse[m[K(std::chrono::milliseconds)[m[K’:
[0.482s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;31m[Kerror: [m[K‘[01m[Kunique_lock[m[K’ is not a member of ‘[01m[Kstd[m[K’
[0.482s]   145 |         std::[01;31m[Kunique_lock[m[K<std::mutex> lock(mutex);
[0.482s]       |              [01;31m[K^~~~~~~~~~~[m[K
[0.482s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::unique_lock[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[0.482s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
[0.482s]   145 |         std::unique_lock<std::[01;31m[Kmutex[m[K> lock(mutex);
[0.482s]       |                               [01;31m[K^~~~~[m[K
[0.482s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:31:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[0.482s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:43:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
[0.482s]   145 |         std::unique_lock<std::mutex> lock([01;31m[Kmutex[m[K);
[0.482s]       |                                           [01;31m[K^~~~~[m[K
[0.483s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:145:38:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
[0.483s]   145 |         std::unique_lock<std::mutex> [01;31m[Klock[m[K(mutex);
[0.483s]       |                                      [01;31m[K^~~~[m[K
[0.483s]       |                                      [32m[Kclock[m[K
[0.485s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:146:16:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
[0.485s]   146 |         return [01;31m[Kcv[m[K.wait_for(lock, timeout, [this] { return response_received; });
[0.485s]       |                [01;31m[K^~[m[K
[0.485s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:[m[K In member function ‘[01m[Kvoid sl_pmu::common::ResponseWaiter::[01;32m[KsignalResponse[m[K(const sl_pmu::common::AsyncResult&)[m[K’:
[0.485s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;31m[Kerror: [m[K‘[01m[Klock_guard[m[K’ is not a member of ‘[01m[Kstd[m[K’
[0.485s]   151 |         std::[01;31m[Klock_guard[m[K<std::mutex> lock(mutex);
[0.485s]       |              [01;31m[K^~~~~~~~~~[m[K
[0.485s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:14:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::lock_guard[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[0.485s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ is not a member of ‘[01m[Kstd[m[K’
[0.486s]   151 |         std::lock_guard<std::[01;31m[Kmutex[m[K> lock(mutex);
[0.486s]       |                              [01;31m[K^~~~~[m[K
[0.486s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:30:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::mutex[m[K’ is defined in header ‘[01m[K<mutex>[m[K’; did you forget to ‘[01m[K#include <mutex>[m[K’?
[0.487s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:42:[m[K [01;31m[Kerror: [m[K‘[01m[Kmutex[m[K’ was not declared in this scope
[0.487s]   151 |         std::lock_guard<std::mutex> lock([01;31m[Kmutex[m[K);
[0.487s]       |                                          [01;31m[K^~~~~[m[K
[0.488s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:151:37:[m[K [01;31m[Kerror: [m[K‘[01m[Klock[m[K’ was not declared in this scope; did you mean ‘[01m[Kclock[m[K’?
[0.489s]   151 |         std::lock_guard<std::mutex> [01;31m[Klock[m[K(mutex);
[0.489s]       |                                     [01;31m[K^~~~[m[K
[0.489s]       |                                     [32m[Kclock[m[K
[0.490s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include/sl_pmu/common/types.hpp:154:9:[m[K [01;31m[Kerror: [m[K‘[01m[Kcv[m[K’ was not declared in this scope
[0.490s]   154 |         [01;31m[Kcv[m[K.notify_one();
[0.490s]       |         [01;31m[K^~[m[K
[0.600s] gmake[2]: *** [CMakeFiles/sl_pmu_common.dir/build.make:76: CMakeFiles/sl_pmu_common.dir/src/common/config_manager.cpp.o] Error 1
[0.601s] gmake[1]: *** [CMakeFiles/Makefile2:149: CMakeFiles/sl_pmu_common.dir/all] Error 2
[0.601s] gmake: *** [Makefile:146: all] Error 2
[0.603s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu -- -j4 -l4
