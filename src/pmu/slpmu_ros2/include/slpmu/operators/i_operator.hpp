/**
 * i_operator.h
 * Abstract interface for operator
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include <optional>

#include <cstdint>

namespace slpmu::operators {

struct TimeWindow {
    std::int64_t starts_in_ns;
    std::int64_t duration_in_ns;
};

class IOperator {
public:
    static constexpr std::int64_t kRoundTripDurationInNs = 1000; // 1ms

public:
    virtual ~IOperator() = default;
    IOperator(const IOperator&) = delete;
    IOperator& operator=(const IOperator&) = delete;
    IOperator(IOperator&&) = delete;
    IOperator& operator=(IOperator&&) = delete;

protected:
    IOperator() = default;

public:
    virtual std::optional<TimeWindow> requested_time_window(std::int64_t total_duration_in_ns) = 0;
    virtual void tick() = 0;
};

}
