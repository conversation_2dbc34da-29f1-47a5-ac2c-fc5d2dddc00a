/**
 * control_operator.hpp
 * Motor controlling operator
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "i_operator.hpp"

#include <slpmu/slpmu_motor.hpp>

#include <functional>
#include <array>

namespace slpmu::operators {

class ControlOperator : public IOperator {
public:
    template < typename T >
    using TimestampedValue = IMotorController::TimestampedValue<T>;

    static constexpr std::size_t kNumMotors = 2;

    using motor_positions_t = std::array<TimestampedValue<std::int32_t>, kNumMotors>;
    using motor_speeds_t = std::array<TimestampedValue<std::int32_t>, kNumMotors>;
    using gpio_inputs_t = TimestampedValue<std::uint32_t>;
    using motor_speed_reqs_t = std::array<std::int32_t, kNumMotors>;

public:

    explicit ControlOperator(std::shared_ptr<slpmu::IMotorController> motor_ctrl);
    virtual ~ControlOperator() = default;

    virtual std::optional<TimeWindow> requested_time_window(std::int64_t total_duration_in_ns) override;
    virtual void tick() override;

public:
    using position_callback_t = std::function<void(const motor_positions_t& motor_pos)>;
    using speed_callback_t = std::function<void(const motor_speeds_t& motor_speed)>;
    using gpio_callback_t = std::function<void(const gpio_inputs_t& gpio_inputs)>;

    void set_position_callback(position_callback_t callback);
    void set_speed_callback(speed_callback_t callback);
    void set_gpio_callback(gpio_callback_t callback);

    void set_speed(const motor_speed_reqs_t& rpms);

private:
    std::shared_ptr<slpmu::IMotorController> motor_ctrl_;

private:
    motor_speed_reqs_t motion_request_rpm_ { 0, 0 };
    position_callback_t position_callback_;
    speed_callback_t speed_callback_;
    gpio_callback_t gpio_callback_;
};

}
