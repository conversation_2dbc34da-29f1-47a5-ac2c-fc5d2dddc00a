/**
 * operators.hpp
 * Slamtec PMU operators
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

/*
# Operators

In our system, we actually only have one can bus controller, which is shared by all the nodes.
But we do have a bunch of nodes connected to the can bus, if we do not coordinate the access to the can bus,
they might collide with each other and breaks our timeing assumptions.

Therefore, what we do here is to provide an abstract interface for the operators, and schedule there ticks.

In this design, we have one operator as the major ticker, which is the control operator (set speed, read position, read emergency gpio, etc.)
Between two ticks of the motor operator, we ask all other operators if they want to do something in this window.

We do motor control in 40hz, so each iteration we have 25ms to do everything.
For safety, we always keep 10ms as buffer time for doing nothing.

What must be done in each tick:
1. set_speed: 1ms * 2
2. read_positions: 1ms * 2
3. read_speed: 1ms * 2
4. read_gpio_inputs: 1ms
sum all up: 7ms
we got 8ms left for other operators.
*/

#pragma once

#include "i_operator.hpp"
#include "control_operator.hpp"
