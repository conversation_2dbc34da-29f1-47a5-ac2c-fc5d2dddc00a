/**
 * slpmu_node.hpp
 * Slamtec PMU ROS2 node
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "operators/operators.hpp"
#include <slpmu/slpmu_process.hpp>

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/joint_state.hpp>

namespace slpmu::node {

class PMUNode : public rclcpp::Node {
public:
    PMUNode(const rclcpp::NodeOptions& options = rclcpp::NodeOptions());
    virtual ~PMUNode() = default;

private:
    void init_parameters();
    bool init_devices();
    void init_process();
    void init_operators();
    void init_topics();
    void start();

private:
    void on_motor_position(const operators::ControlOperator::motor_positions_t& motor_pos);
    void on_motor_speed(const operators::ControlOperator::motor_speeds_t& motor_speed);
    void on_gpio_inputs(const operators::ControlOperator::gpio_inputs_t& gpio_inputs);

    void on_tick();

private:
    // parameters
    double control_freq_ { 40. };

    std::string can_interface_ { "can0" };
    size_t motor_ctrl_node_id_ { 1 };
    double wheel_diameter_left_ { 0.14 };
    double wheel_diameter_right_ { 0.14 };
    double wheel_span_ { 0.39 };
    double gear_ratio_ { 1.0 };
    int encoder_resolution_ { 16384 };

    std::string odom_frame_id_ { "odom" };
    std::string base_frame_id_ { "base_link" };

    std::string cmd_vel_topic_ { "cmd_vel" };
    std::string odom_topic_ { "odom" };
    std::string filtered_imu_topic_ { "imu/filtered" };
    std::string joint_state_topic_ { "joint_state" };

    // devices
    std::shared_ptr<IMotorController> motor_ctrl_;

    // process
    std::unique_ptr<slpmu::process::ImuOdomFusion> imu_odom_fusion_;

    // operators
    std::unique_ptr<operators::ControlOperator> control_operator_;
    std::vector<std::unique_ptr<operators::IOperator>> operators_;

    // topics
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    rclcpp::Publisher<sensor_msgs::msg::JointState>::SharedPtr joint_state_pub_;
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr filtered_imu_sub_;

    // timers
    rclcpp::TimerBase::SharedPtr tick_timer_;
    
    // Joint state tracking
    std::int32_t last_left_position_ {0};
    std::int32_t last_right_position_ {0};
    double last_left_speed_rad_s_ {0.0};
    double last_right_speed_rad_s_ {0.0};
    std::int64_t last_position_timestamp_ns_ {0};
};

}
