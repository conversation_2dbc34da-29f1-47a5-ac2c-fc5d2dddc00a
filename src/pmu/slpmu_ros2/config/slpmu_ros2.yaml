# Slamtec PMU Driver Node configuration

slpmu_node:
  ros__parameters:
    # Control loop frequency (Hz)
    control_freq: 40.0
    
    # CAN interface configuration
    can_interface: "can0"
    motor_ctrl_node_id: 1
    
    # Wheel parameters
    wheel_diameter_left: 0.14   # meters
    wheel_diameter_right: 0.14  # meters
    wheel_span: 0.39            # meters (distance between wheels)
    
    # Motor/Encoder parameters
    gear_ratio: 1.0             # gear ratio between motor and wheel
    encoder_resolution: 16384   # encoder ticks per motor revolution
    
    # IMU-Odometry fusion parameters
    imu_time_offset_ns: 6000000           # 6ms - how much IMU messages are ahead of odometry
    imu_buffer_size: 200                  # Keep last 2 seconds at 100Hz
    imu_interpolation_tolerance_ns: 20000000  # 20ms - max time tolerance for interpolation
    
    # Frame IDs
    odom_frame_id: "odom"
    base_frame_id: "base_link"
    
    # Topic names
    cmd_vel_topic: "cmd_vel"
    odom_topic: "odom"
    filtered_imu_topic: "imu/filtered"
    joint_state_topic: "joint_state"