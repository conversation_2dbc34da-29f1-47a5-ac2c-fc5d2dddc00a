/**
 * slpmu_node.cpp
 * Slamtec PMU ROS2 node
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slpmu/slpmu_node.hpp>
#include <slpmu/motor/motor_controller_can.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <thread>

namespace slpmu::node {

static inline std::int64_t steady_clock_now_ns() {
    return std::chrono::steady_clock::now().time_since_epoch().count();
}

PMUNode::PMUNode(const rclcpp::NodeOptions& options)
    : Node("slpmu_node", options) {
    RCLCPP_INFO(this->get_logger(), "Initializing SLAMTEC PMU Node...");
    
    try {
        // Initialize in sequence
        init_parameters();
        
        if (!init_devices()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize devices");
            throw std::runtime_error("Device initialization failed");
        }
        
        init_process();
        init_operators();
        init_topics();
        start();
        
        RCLCPP_INFO(this->get_logger(), "SLAMTEC PMU Node initialization completed successfully");
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize PMU node: %s", e.what());
        throw;
    }
}

void PMUNode::init_parameters() {
    control_freq_ = this->declare_parameter<double>("control_freq", 40.0);
    
    can_interface_ = this->declare_parameter<std::string>("can_interface", "can0");
    motor_ctrl_node_id_ = this->declare_parameter<int>("motor_ctrl_node_id", 1);
    
    wheel_diameter_left_ = this->declare_parameter<double>("wheel_diameter_left", 0.14);
    wheel_diameter_right_ = this->declare_parameter<double>("wheel_diameter_right", 0.14);
    wheel_span_ = this->declare_parameter<double>("wheel_span", 0.39);
    gear_ratio_ = this->declare_parameter<double>("gear_ratio", 1.0);
    encoder_resolution_ = this->declare_parameter<int>("encoder_resolution", 16384);
    
    odom_frame_id_ = this->declare_parameter<std::string>("odom_frame_id", "odom");
    base_frame_id_ = this->declare_parameter<std::string>("base_frame_id", "base_link");
    
    cmd_vel_topic_ = this->declare_parameter<std::string>("cmd_vel_topic", "cmd_vel");
    odom_topic_ = this->declare_parameter<std::string>("odom_topic", "odom");
    filtered_imu_topic_ = this->declare_parameter<std::string>("filtered_imu_topic", "imu/filtered");
    joint_state_topic_ = this->declare_parameter<std::string>("joint_state_topic", "joint_state");
    
    RCLCPP_INFO(this->get_logger(), "PMU Node Parameters:");
    RCLCPP_INFO(this->get_logger(), "  Control frequency: %.1f Hz", control_freq_);
    RCLCPP_INFO(this->get_logger(), "  CAN interface: %s", can_interface_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Motor controller node ID: %zu", motor_ctrl_node_id_);
    RCLCPP_INFO(this->get_logger(), "  Wheel diameter (L/R): %.3f / %.3f m", wheel_diameter_left_, wheel_diameter_right_);
    RCLCPP_INFO(this->get_logger(), "  Wheel span: %.3f m", wheel_span_);
    RCLCPP_INFO(this->get_logger(), "  Gear ratio: %.3f", gear_ratio_);
    RCLCPP_INFO(this->get_logger(), "  Encoder resolution: %d", encoder_resolution_);
}

bool PMUNode::init_devices() {
    RCLCPP_INFO(this->get_logger(), "Initializing motor controller...");
    
    MotorControllerCan::Config motor_config;
    motor_config.can_interface = can_interface_;
    motor_config.node_id = static_cast<std::uint16_t>(motor_ctrl_node_id_);
    motor_config.sdo_timeout_ns = 100'000'000;  // 100ms timeout
    
    motor_ctrl_ = std::make_shared<MotorControllerCan>(motor_config);
    
    if (!motor_ctrl_->open()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to open motor controller on CAN interface %s with node ID %zu",
                     can_interface_.c_str(), motor_ctrl_node_id_);
        return false;
    }
    
    RCLCPP_INFO(this->get_logger(), "Motor controller initialized successfully on %s (node ID: %zu)",
                can_interface_.c_str(), motor_ctrl_node_id_);
    
    if (!motor_ctrl_->send_control_command(IMotorController::ControlCommand::Enable)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to enable motor controller");
        return false;
    }
    
    if (!motor_ctrl_->set_run_mode(IMotorController::RunMode::Speed)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to set motor controller to speed mode");
        return false;
    }
    
    RCLCPP_INFO(this->get_logger(), "Motor controller enabled and set to speed mode");
    
    return true;
}

void PMUNode::init_process() {
    RCLCPP_INFO(this->get_logger(), "Initializing IMU-Odometry fusion process...");
    
    // Create ImuOdomFusion with custom config
    process::ImuOdomFusion::Config fusion_config;
    fusion_config.imu_time_offset_ns = this->declare_parameter<int64_t>("imu_time_offset_ns", 
                                                                        process::ImuOdomFusion::kDefaultImuTimeOffsetNs);
    fusion_config.max_buffer_size = this->declare_parameter<int>("imu_buffer_size", 
                                                                  process::ImuOdomFusion::kDefaultMaxBufferSize);
    fusion_config.interpolation_tolerance_ns = this->declare_parameter<int64_t>("imu_interpolation_tolerance_ns",
                                                                                 process::ImuOdomFusion::kDefaultInterpolationToleranceNs);
    
    imu_odom_fusion_ = std::make_unique<process::ImuOdomFusion>(fusion_config);
    
    // Set wheel parameters - now using diameters directly
    imu_odom_fusion_->set_wheel_parameters(wheel_span_, wheel_diameter_left_, wheel_diameter_right_);
    
    // Set encoder resolution (counts per revolution)
    // Note: encoder_resolution_ is counts per motor revolution
    // Need to account for gear ratio to get counts per wheel revolution
    std::int32_t counts_per_wheel_revolution = static_cast<std::int32_t>(encoder_resolution_ * gear_ratio_);
    imu_odom_fusion_->set_encoder_resolution(counts_per_wheel_revolution);
    
    // Set fusion callback to publish odometry
    imu_odom_fusion_->set_fusion_callback(
        [this](const process::ImuOdomFusion::FusedOdom& fused_odom) {
            // Convert to ROS odometry message and publish
            auto odom_msg = nav_msgs::msg::Odometry();
            odom_msg.header.stamp = rclcpp::Time(fused_odom.timestamp_ns);
            odom_msg.header.frame_id = odom_frame_id_;
            odom_msg.child_frame_id = base_frame_id_;
            
            // Set position
            odom_msg.pose.pose.position.x = fused_odom.x;
            odom_msg.pose.pose.position.y = fused_odom.y;
            odom_msg.pose.pose.position.z = 0.0;
            
            // Set orientation (quaternion from yaw)
            tf2::Quaternion q;
            q.setRPY(0, 0, fused_odom.theta);
            odom_msg.pose.pose.orientation = tf2::toMsg(q);
            
            // Set velocities
            odom_msg.twist.twist.linear.x = fused_odom.linear_vel;
            odom_msg.twist.twist.linear.y = 0.0;
            odom_msg.twist.twist.linear.z = 0.0;
            odom_msg.twist.twist.angular.x = 0.0;
            odom_msg.twist.twist.angular.y = 0.0;
            odom_msg.twist.twist.angular.z = fused_odom.angular_vel;
            
            // Publish odometry if publisher is initialized
            if (odom_pub_) {
                odom_pub_->publish(odom_msg);
            }
        }
    );
    
    RCLCPP_INFO(this->get_logger(), "IMU-Odometry fusion initialized:");
    RCLCPP_INFO(this->get_logger(), "  IMU time offset: %ld ns", fusion_config.imu_time_offset_ns);
    RCLCPP_INFO(this->get_logger(), "  IMU buffer size: %zu", fusion_config.max_buffer_size);
    RCLCPP_INFO(this->get_logger(), "  Interpolation tolerance: %ld ns", fusion_config.interpolation_tolerance_ns);
    RCLCPP_INFO(this->get_logger(), "  Counts per wheel revolution: %d", counts_per_wheel_revolution);
}

void PMUNode::init_operators() {
    RCLCPP_INFO(this->get_logger(), "Initializing operators...");
    
    // Create the control operator
    control_operator_ = std::make_unique<operators::ControlOperator>(motor_ctrl_);
    
    // Set callbacks using std::bind
    control_operator_->set_position_callback(
        std::bind(&PMUNode::on_motor_position, this, std::placeholders::_1));
    control_operator_->set_speed_callback(
        std::bind(&PMUNode::on_motor_speed, this, std::placeholders::_1));
    control_operator_->set_gpio_callback(
        std::bind(&PMUNode::on_gpio_inputs, this, std::placeholders::_1));
    
    // Note: operators_ vector is reserved for additional operators (not control_operator)
    
    RCLCPP_INFO(this->get_logger(), "Control operator initialized");
}

void PMUNode::init_topics() {
    RCLCPP_INFO(this->get_logger(), "Initializing ROS2 topics...");
    
    // Create publishers
    odom_pub_ = this->create_publisher<nav_msgs::msg::Odometry>(
        odom_topic_, rclcpp::QoS(10));
    
    joint_state_pub_ = this->create_publisher<sensor_msgs::msg::JointState>(
        joint_state_topic_, rclcpp::QoS(10));
    
    // Create subscriptions
    cmd_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
        cmd_vel_topic_, 
        rclcpp::QoS(10),
        [this](const geometry_msgs::msg::Twist::SharedPtr msg) {
            // Convert twist to motor speeds
            if (control_operator_) {
                // Calculate wheel speeds from twist
                double linear_vel = msg->linear.x;
                double angular_vel = msg->angular.z;
                
                // Differential drive kinematics
                double left_wheel_vel = linear_vel - (angular_vel * wheel_span_ / 2.0);
                double right_wheel_vel = linear_vel + (angular_vel * wheel_span_ / 2.0);
                
                // Convert to RPM
                // wheel_vel (m/s) = (RPM * 2π * radius) / 60
                // RPM = (wheel_vel * 60) / (2π * radius)
                double left_radius = wheel_diameter_left_ / 2.0;
                double right_radius = wheel_diameter_right_ / 2.0;
                
                std::int32_t left_rpm = static_cast<std::int32_t>(
                    (left_wheel_vel * 60.0) / (2.0 * M_PI * left_radius) * gear_ratio_);
                std::int32_t right_rpm = static_cast<std::int32_t>(
                    (right_wheel_vel * 60.0) / (2.0 * M_PI * right_radius) * gear_ratio_);
                
                // Set motor speeds
                operators::ControlOperator::motor_speed_reqs_t speeds = {left_rpm, right_rpm};
                control_operator_->set_speed(speeds);
            }
        });
    
    filtered_imu_sub_ = this->create_subscription<sensor_msgs::msg::Imu>(
        filtered_imu_topic_,
        rclcpp::QoS(10),
        [this](const sensor_msgs::msg::Imu::SharedPtr msg) {
            // Extract yaw from quaternion
            if (imu_odom_fusion_) {
                tf2::Quaternion q(
                    msg->orientation.x,
                    msg->orientation.y,
                    msg->orientation.z,
                    msg->orientation.w);
                
                double roll, pitch, yaw;
                tf2::Matrix3x3(q).getRPY(roll, pitch, yaw);
                
                // Add IMU yaw sample to fusion
                std::int64_t timestamp_ns = rclcpp::Time(msg->header.stamp).nanoseconds();
                imu_odom_fusion_->add_imu_yaw_sample(yaw, timestamp_ns);
            }
        });
    
    RCLCPP_INFO(this->get_logger(), "Topics initialized:");
    RCLCPP_INFO(this->get_logger(), "  Publishing odometry on: %s", odom_topic_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Publishing joint states on: %s", joint_state_topic_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Subscribing to cmd_vel on: %s", cmd_vel_topic_.c_str());
    RCLCPP_INFO(this->get_logger(), "  Subscribing to filtered IMU on: %s", filtered_imu_topic_.c_str());
}

void PMUNode::start() {
    RCLCPP_INFO(this->get_logger(), "Starting PMU node...");
    
    // Reset IMU-odometry fusion to start fresh
    if (imu_odom_fusion_) {
        imu_odom_fusion_->reset();
    }
    
    // Create timer for control loop
    auto timer_period = std::chrono::duration<double>(1.0 / control_freq_);
    tick_timer_ = this->create_wall_timer(
        std::chrono::duration_cast<std::chrono::nanoseconds>(timer_period),
        std::bind(&PMUNode::on_tick, this));
    
    RCLCPP_INFO(this->get_logger(), "PMU node started with control loop at %.1f Hz", control_freq_);
}

void PMUNode::on_tick() {
    const std::int64_t tick_start_ns = steady_clock_now_ns();
    
    // Calculate tick period and deadline
    // Period = 1/control_freq_ (e.g., 25ms for 40Hz)
    // Buffer time = 10ms as per design doc
    const std::int64_t period_ns = static_cast<std::int64_t>(1e9 / control_freq_);
    const std::int64_t buffer_time_ns = 10'000'000;  // 10ms buffer
    const std::int64_t deadline_ns = tick_start_ns + period_ns - buffer_time_ns;
    
    // Execute control operator (mandatory tick)
    if (control_operator_) {
        control_operator_->tick();
    }
    
    // Schedule and execute other operators if we have time
    while (!operators_.empty()) {
        std::int64_t now_ns = steady_clock_now_ns();
        std::int64_t remaining_ns = deadline_ns - now_ns;
        
        if (remaining_ns <= 0) {
            break;  // No time left
        }
        
        // Find the operator with earliest start time that fits in remaining window
        std::unique_ptr<operators::IOperator>* best_operator = nullptr;
        operators::TimeWindow best_window = {0, 0};
        bool found_operator = false;
        
        for (auto& op : operators_) {
            if (!op) continue;
            
            auto requested_window = op->requested_time_window(remaining_ns);
            if (!requested_window.has_value()) continue;
            
            // Check if this operator can fit in the remaining time
            if (requested_window->starts_in_ns + requested_window->duration_in_ns <= remaining_ns) {
                // Select the one with earliest start time
                if (!found_operator || requested_window->starts_in_ns < best_window.starts_in_ns) {
                    best_operator = &op;
                    best_window = requested_window.value();
                    found_operator = true;
                }
            }
        }
        
        // If no operator found that fits, we're done
        if (!found_operator) {
            break;
        }
        
        // Wait until the operator's requested start time if needed
        if (best_window.starts_in_ns > 0) {
            std::this_thread::sleep_for(std::chrono::nanoseconds(best_window.starts_in_ns));
        }
        
        // Execute the selected operator
        (*best_operator)->tick();
    }
}

void PMUNode::on_motor_position(const operators::ControlOperator::motor_positions_t& motor_pos) {
    // Process encoder positions through IMU-Odom fusion
    if (imu_odom_fusion_ && motor_pos[0].success && motor_pos[1].success) {
        process::ImuOdomFusion::OdomData odom_data;
        odom_data.left_encoder = motor_pos[0].value;  // Left motor is index 0
        odom_data.right_encoder = motor_pos[1].value; // Right motor is index 1
        
        // Use hardware timestamp if available, otherwise use system timestamp
        std::int64_t timestamp_ns;
        if (motor_pos[0].hw_timestamp_ns.has_value()) {
            timestamp_ns = motor_pos[0].hw_timestamp_ns.value();
        } else {
            timestamp_ns = motor_pos[0].timestamp_ns.value_or(steady_clock_now_ns());
        }
        odom_data.timestamp_ns = timestamp_ns;
        
        // Process odometry through fusion
        imu_odom_fusion_->process_odometry(odom_data);
        
        // Calculate velocities for joint states
        if (last_position_timestamp_ns_ > 0) {
            double dt = (timestamp_ns - last_position_timestamp_ns_) / 1e9;  // Convert to seconds
            
            if (dt > 0) {
                // Calculate position differences (handling overflow)
                std::int32_t left_diff = motor_pos[0].value - last_left_position_;
                std::int32_t right_diff = motor_pos[1].value - last_right_position_;
                
                // Convert to radians
                double counts_to_radians = (2.0 * M_PI) / (encoder_resolution_ * gear_ratio_);
                double left_angle_diff = left_diff * counts_to_radians;
                double right_angle_diff = right_diff * counts_to_radians;
                
                // Calculate velocities
                last_left_speed_rad_s_ = left_angle_diff / dt;
                last_right_speed_rad_s_ = right_angle_diff / dt;
            }
        }
        
        // Update last positions and timestamp
        last_left_position_ = motor_pos[0].value;
        last_right_position_ = motor_pos[1].value;
        last_position_timestamp_ns_ = timestamp_ns;
    }
    
    // Publish joint states
    if (joint_state_pub_ && motor_pos[0].success && motor_pos[1].success) {
        // Convert encoder counts to radians
        double counts_to_radians = (2.0 * M_PI) / (encoder_resolution_ * gear_ratio_);
        double left_position_rad = motor_pos[0].value * counts_to_radians;
        double right_position_rad = motor_pos[1].value * counts_to_radians;
        
        auto joint_state_msg = sensor_msgs::msg::JointState();
        joint_state_msg.header.stamp = this->now();
        joint_state_msg.name.push_back("left");
        joint_state_msg.name.push_back("right");
        joint_state_msg.position.push_back(left_position_rad);
        joint_state_msg.position.push_back(right_position_rad);
        joint_state_msg.velocity.push_back(last_left_speed_rad_s_);
        joint_state_msg.velocity.push_back(last_right_speed_rad_s_);
        
        joint_state_pub_->publish(joint_state_msg);
    }
}

void PMUNode::on_motor_speed(const operators::ControlOperator::motor_speeds_t& motor_speed) {
    // Log motor speeds for debugging (at reduced rate)
    static std::int64_t last_speed_log_ns = 0;
    std::int64_t now_ns = steady_clock_now_ns();
    
    // Log every second
    if (now_ns - last_speed_log_ns > 1'000'000'000) {
        if (motor_speed[0].success && motor_speed[1].success) {
            RCLCPP_DEBUG(this->get_logger(), 
                        "Motor speeds - Left: %d RPM, Right: %d RPM", 
                        motor_speed[0].value, motor_speed[1].value);
        }
        last_speed_log_ns = now_ns;
    }
}

void PMUNode::on_gpio_inputs(const operators::ControlOperator::gpio_inputs_t& gpio_inputs) {
    // Handle GPIO inputs (e.g., emergency stop, brake release, etc.)
    if (gpio_inputs.success) {
        static std::uint32_t last_gpio_state = 0;
        
        // GPIO bit definitions
        constexpr std::uint32_t GPIO_I_EMERGENCY_BIT0 = 0x01;  // Emergency signal bit 0 (reverse logic)
        constexpr std::uint32_t GPIO_I_EMERGENCY_BIT1 = 0x02;  // Emergency signal bit 1
        constexpr std::uint32_t GPIO_I_BRAKE_RELEASE = 0x04;   // Brake release signal bit 2
        
        // Check if GPIO state changed
        if (gpio_inputs.value != last_gpio_state) {
            // Check emergency stop bit0 (reverse logic: LOW triggers emergency stop)
            bool bit0_high = (gpio_inputs.value & GPIO_I_EMERGENCY_BIT0) != 0;
            bool emergency_stop_bit0 = !bit0_high;  // Emergency stop when bit0 is LOW
            
            bool prev_bit0_high = (last_gpio_state & GPIO_I_EMERGENCY_BIT0) != 0;
            bool prev_emergency_stop_bit0 = !prev_bit0_high;
            
            // Check emergency stop bit1 (normal logic: HIGH triggers emergency stop)
            bool emergency_stop_bit1 = (gpio_inputs.value & GPIO_I_EMERGENCY_BIT1) != 0;
            bool prev_emergency_stop_bit1 = (last_gpio_state & GPIO_I_EMERGENCY_BIT1) != 0;
            
            // Check brake release (HIGH triggers brake release)
            bool brake_release = (gpio_inputs.value & GPIO_I_BRAKE_RELEASE) != 0;
            bool prev_brake_release = (last_gpio_state & GPIO_I_BRAKE_RELEASE) != 0;
            
            // Combined emergency stop condition
            bool emergency_stop = emergency_stop_bit0 || emergency_stop_bit1;
            bool prev_emergency_stop = prev_emergency_stop_bit0 || prev_emergency_stop_bit1;
            
            // Log state changes for emergency stop bit0
            if (emergency_stop_bit0 != prev_emergency_stop_bit0) {
                RCLCPP_INFO(this->get_logger(), "Emergency signal bit0 changed: %s",
                           emergency_stop_bit0 ? "TRIGGERED" : "RELEASED");
            }
            
            // Log state changes for emergency stop bit1
            if (emergency_stop_bit1 != prev_emergency_stop_bit1) {
                RCLCPP_INFO(this->get_logger(), "Emergency signal bit1 changed: %s",
                           emergency_stop_bit1 ? "TRIGGERED" : "RELEASED");
            }
            
            // Log state changes for brake release
            if (brake_release != prev_brake_release) {
                RCLCPP_INFO(this->get_logger(), "Brake release signal changed: %s",
                           brake_release ? "TRIGGERED" : "RELEASED");
            }
            
            // Stop motors if emergency stop or brake release is triggered
            if ((emergency_stop || brake_release) && control_operator_) {
                operators::ControlOperator::motor_speed_reqs_t stop_speeds = {0, 0};
                control_operator_->set_speed(stop_speeds);
                
                if (emergency_stop && !prev_emergency_stop) {
                    RCLCPP_WARN(this->get_logger(), "Motors stopped due to emergency stop");
                } else if (brake_release && !prev_brake_release) {
                    RCLCPP_WARN(this->get_logger(), "Motors stopped due to brake release");
                }
            }
            
            // Log GPIO state change
            RCLCPP_DEBUG(this->get_logger(), "GPIO inputs changed: 0x%08X -> 0x%08X", 
                        last_gpio_state, gpio_inputs.value);
            
            last_gpio_state = gpio_inputs.value;
        }
        
        // Throttled warnings for active safety conditions
        static std::int64_t last_warning_ns = 0;
        std::int64_t now_ns = steady_clock_now_ns();
        
        if (now_ns - last_warning_ns > 2'000'000'000) {  // Log every 2 seconds
            bool emergency_stop_bit0 = !(gpio_inputs.value & GPIO_I_EMERGENCY_BIT0);
            bool emergency_stop_bit1 = (gpio_inputs.value & GPIO_I_EMERGENCY_BIT1) != 0;
            bool brake_release = (gpio_inputs.value & GPIO_I_BRAKE_RELEASE) != 0;
            
            if (emergency_stop_bit0) {
                RCLCPP_WARN(this->get_logger(), "Emergency signal bit0 is triggered");
            }
            if (emergency_stop_bit1) {
                RCLCPP_WARN(this->get_logger(), "Emergency signal bit1 is triggered");
            }
            if (brake_release) {
                RCLCPP_WARN(this->get_logger(), "Brake release signal is triggered");
            }
            
            if (emergency_stop_bit0 || emergency_stop_bit1 || brake_release) {
                last_warning_ns = now_ns;
            }
        }
    }
}

}
