/**
 * control_operator.cpp
 * Motor controlling operator implementation
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slpmu/operators/control_operator.hpp>
#include <algorithm>

namespace slpmu::operators {

ControlOperator::ControlOperator(std::shared_ptr<slpmu::IMotorController> motor_ctrl)
    : motor_ctrl_(std::move(motor_ctrl))
{
}

std::optional<TimeWindow> ControlOperator::requested_time_window(std::int64_t)
{
    TimeWindow window;
    window.starts_in_ns = 0;
    window.duration_in_ns = kRoundTripDurationInNs * 7;
    
    return window;
}

void ControlOperator::tick()
{
    if (!motor_ctrl_ || !motor_ctrl_->is_open()) {
        return;
    }
    
    motor_ctrl_->set_speed(IMotorController::MotorIndex::Left, motion_request_rpm_[0]);
    motor_ctrl_->set_speed(IMotorController::MotorIndex::Right, motion_request_rpm_[1]);
    
    motor_positions_t positions;
    positions[0] = motor_ctrl_->read_position(IMotorController::MotorIndex::Left);
    positions[1] = motor_ctrl_->read_position(IMotorController::MotorIndex::Right);
    
    if (position_callback_) {
        position_callback_(positions);
    }
    
    motor_speeds_t speeds;
    speeds[0] = motor_ctrl_->read_speed(IMotorController::MotorIndex::Left);
    speeds[1] = motor_ctrl_->read_speed(IMotorController::MotorIndex::Right);
    
    if (speed_callback_) {
        speed_callback_(speeds);
    }
    
    auto gpio_inputs = motor_ctrl_->read_gpio_inputs();
    if (gpio_callback_) {
        gpio_callback_(gpio_inputs);
    }
}

void ControlOperator::set_position_callback(position_callback_t callback)
{
    position_callback_ = std::move(callback);
}

void ControlOperator::set_speed_callback(speed_callback_t callback)
{
    speed_callback_ = std::move(callback);
}

void ControlOperator::set_gpio_callback(gpio_callback_t callback)
{
    gpio_callback_ = std::move(callback);
}

void ControlOperator::set_speed(const motor_speed_reqs_t& rpms)
{
    motion_request_rpm_ = rpms;
}

}