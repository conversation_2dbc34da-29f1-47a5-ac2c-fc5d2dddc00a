#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # declare launch arguments
    config_file_arg = DeclareLaunchArgument(
        "config_file",
        default_value=PathJoinSubstitution([
            FindPackageShare('slpmu_ros2'),
            "config",
            "slpmu_ros2.yaml"
        ]),
        description="Path to the PMU configuration file"
    )
    
    namespace_arg = DeclareLaunchArgument(
        "namespace",
        default_value="",
        description="Namespace for the PMU node"
    )

    # PMU node
    pmu_node = Node(
        package="slpmu_ros2",
        executable="slpmu_node",
        name="slpmu_node",
        namespace=LaunchConfiguration("namespace"),
        parameters=[
            LaunchConfiguration("config_file")
        ],
        output="screen",
        arguments=['--ros-args', '--log-level', 'info'],
        remappings=[
            # Add any topic remappings if needed
        ]
    )

    return LaunchDescription([
        config_file_arg,
        namespace_arg,
        pmu_node,
    ])