/**
 * battery_can.cpp
 * CAN-based battery monitor implementation
 */

#include <slpmu/power/battery_can.hpp>
#include <cstring>
#include <endian.h>
#include <chrono>
#include <iostream>

namespace slpmu {


constexpr std::uint32_t kBatteryInforId = 0x100;
constexpr std::uint32_t kBatteryStatusId = 0x101;

BatteryCan::BatteryCan(const IBattery::Config& cfg)
    : config_(cfg)
    , can_dev_(slcan::SocketCan::Config{
        cfg.can_interface,
        slcan::SocketCan::CanDeviceFlags::All,
        {{kBatteryInforId, 0x7FF}, {kBatteryStatusId, 0x7FF}}
      })
    , is_open_(false) 
{}

BatteryCan::~BatteryCan() {
    if (is_open_) close();
}

bool BatteryCan::open() {
    if (is_open_) return true;
    if (can_dev_.open()) { is_open_ = true; return true; }
    return false;
}

bool BatteryCan::close() {
    if (!is_open_) return true;
    if (can_dev_.close()) { is_open_ = false; return true; }
    return false;
}

bool BatteryCan::is_open() const { return is_open_; }

IBattery::TimestampedValue<std::uint16_t> BatteryCan::read_voltage() {
    TimestampedValue<std::uint16_t> out{false, 0, std::nullopt, std::nullopt};

    struct __attribute__((packed)) battery_voltage_data_t {
        std::uint16_t voltage;
        std::uint8_t padding[4];
        std::uint16_t crc;
    };

    battery_voltage_data_t data_frame;

    memset(&data_frame, 0, sizeof(data_frame));
    auto res = battery_read(config_.can_id_battery_info, &data_frame, sizeof(data_frame), config_.read_timeout_ns);
    if (!res.success) return out;

    if (be16toh(data_frame.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&data_frame),  sizeof(data_frame) - sizeof(data_frame.crc))) return out;

    out.success = true;
    out.value = be16toh(data_frame.voltage);
    out.timestamp_ns = res.timestamp_ns;
    out.hw_timestamp_ns = res.hw_timestamp_ns;
    return out;
}

IBattery::TimestampedValue<std::int16_t> BatteryCan::read_current() {
    TimestampedValue<std::int16_t> out{false, 0, std::nullopt, std::nullopt};
    
    struct __attribute__((packed)) battery_current_data_t {
        std::uint8_t padding[2];
        std::int16_t current;
        std::uint8_t padding_2[2];
        std::uint16_t crc;
    };

    battery_current_data_t data_frame;

    memset(&data_frame, 0, sizeof(data_frame));
    auto res = battery_read(config_.can_id_battery_info, &data_frame, sizeof(data_frame), config_.read_timeout_ns);
    if (!res.success) return out;

    if (be16toh(data_frame.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&data_frame),  sizeof(data_frame) - sizeof(data_frame.crc))) return out;

    out.success = true;
    out.value = be16toh(data_frame.current);
    out.timestamp_ns = res.timestamp_ns;
    out.hw_timestamp_ns = res.hw_timestamp_ns;
    return out;
}

IBattery::TimestampedValue<std::uint16_t> BatteryCan::read_capacity() {
    TimestampedValue<std::uint16_t> out{false, 0, std::nullopt, std::nullopt};
    
    struct __attribute__((packed)) battery_capacity_data_t {
        std::uint16_t capacity;
        std::uint8_t padding[4];
        std::uint16_t crc;
    };

    battery_capacity_data_t data_frame;
    memset(&data_frame, 0, sizeof(data_frame));
    auto res = battery_read(config_.can_id_battery_info, &data_frame, sizeof(data_frame), config_.read_timeout_ns);
    if (!res.success) return out;

    if (be16toh(data_frame.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&data_frame),  sizeof(data_frame) - sizeof(data_frame.crc))) return out;

    out.success = true;
    out.value = be16toh(data_frame.capacity);
    out.timestamp_ns = res.timestamp_ns;
    out.hw_timestamp_ns = res.hw_timestamp_ns;
    return out;
}

IBattery::TimestampedValue<std::int16_t> BatteryCan::read_cycle_count() {
    TimestampedValue<std::int16_t> out{false, 0, std::nullopt, std::nullopt};
    
    struct __attribute__((packed)) battery_cycle_count_data_t {
        std::uint8_t padding[2];
        std::int16_t cycle_count;
        std::uint8_t padding_2[2];
        std::uint16_t crc;
    };

    battery_cycle_count_data_t data_frame;
    memset(&data_frame, 0, sizeof(data_frame));
    auto res = battery_read(config_.can_id_battery_info, &data_frame, sizeof(data_frame), config_.read_timeout_ns);
    if (!res.success) return out;

    if (be16toh(data_frame.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&data_frame),  sizeof(data_frame) - sizeof(data_frame.crc))) return out;

    out.success = true;
    out.value = be16toh(data_frame.cycle_count);
    out.timestamp_ns = res.timestamp_ns;
    out.hw_timestamp_ns = res.hw_timestamp_ns;
    return out;
}

IBattery::TimestampedValue<std::uint16_t> BatteryCan::read_percentage() {
    TimestampedValue<std::uint16_t> out{false, 0, std::nullopt, std::nullopt};
    
    struct __attribute__((packed)) battery_percentage_data_t {
        std::uint8_t padding[4];
        std::uint16_t percentage;
        std::uint16_t crc;
    };

    battery_percentage_data_t data_frame;

    memset(&data_frame, 0, sizeof(data_frame));
    auto res = battery_read(config_.can_id_battery_info, &data_frame, sizeof(data_frame), config_.read_timeout_ns);
    if (!res.success) return out;

    if (be16toh(data_frame.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&data_frame),  sizeof(data_frame) - sizeof(data_frame.crc))) return out;

    out.success = true;
    out.value = be16toh(data_frame.percentage);
    out.timestamp_ns = res.timestamp_ns;
    out.hw_timestamp_ns = res.hw_timestamp_ns;
    return out;
}

float BatteryCan::get_voltage_scale() { return 0.01; }
float BatteryCan::get_current_scale() { return 0.01; }
float BatteryCan::get_capacity_scale() { return 0.01; }


IBattery::FullStatus BatteryCan::read_full_status() {
    FullStatus fs{false, 0, 0, 0, 0, 0, std::nullopt, std::nullopt};
    
    struct __attribute__((packed)) battery_info_data_t {
        std::uint16_t voltage;
        std::int16_t current;
        std::uint8_t padding[2];
        std::uint16_t crc;
    };

    struct __attribute__((packed)) battery_status_data_t {
        std::uint16_t capacity;
        std::int16_t cycle_count;
        std::uint16_t percentage;
        std::uint16_t crc;
    };

    battery_info_data_t info;
    battery_status_data_t stat;

    memset(&info, 0, sizeof(info));
    memset(&stat, 0, sizeof(stat));
    auto ri = battery_read(config_.can_id_battery_info, &info, sizeof(info), config_.read_timeout_ns);
    auto rs = battery_read(config_.can_id_battery_status, &stat, sizeof(stat), config_.read_timeout_ns);
    if (!ri.success || !rs.success) return fs;

    if (be16toh(info.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&info),  sizeof(info) - sizeof(info.crc))) return fs;
    if (be16toh(stat.crc) != check_crc16(reinterpret_cast<const std::uint8_t*>(&stat),  sizeof(stat) - sizeof(stat.crc))) return fs;


    fs.success = true;
    fs.voltage = be16toh(info.voltage);
    fs.current = be16toh(info.current);
    fs.capacity = be16toh(stat.capacity);
    fs.cycle_count = be16toh(stat.cycle_count);
    fs.percentage = be16toh(stat.percentage);
    fs.timestamp_ns = rs.timestamp_ns;
    fs.hw_timestamp_ns = rs.hw_timestamp_ns;
    return fs;
}

BatteryCan::BatteryReadResult BatteryCan::battery_read(std::uint32_t id, void* data, std::size_t size, std::int64_t timeout_ns) {
    BatteryReadResult result{false, 0, std::nullopt, std::nullopt};
    
    if (!data || size == 0 || size > 8) {
        return result;
    }

    struct can_frame tx_frame;
    std::memset(&tx_frame, 0, sizeof(tx_frame));
    tx_frame.can_id = id;
    tx_frame.can_dlc = size;
    
    for (std::size_t i = 0; i < size; i++) {
        tx_frame.data[i] = static_cast<std::uint8_t*>(data)[i];
    }

    if (!can_dev_.send_frame(tx_frame)) {
        return result;
    }

    auto start_time = std::chrono::steady_clock::now();
    std::int64_t remaining_timeout = timeout_ns;

    while (true) {
        if (timeout_ns >= 0) {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto elapsed_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(elapsed).count();
            remaining_timeout = timeout_ns - elapsed_ns;
            if (remaining_timeout <= 0) {
                return result;
            }
        }

        slcan::SocketCan::ReceiveFrame rx_frame;
        if (!can_dev_.recv_frame(rx_frame, remaining_timeout)) {
            return result;
        }

        if (rx_frame.frame.can_id != id) {
            continue;
        }
        std::size_t data_size = size > rx_frame.frame.can_dlc? rx_frame.frame.can_dlc : size;
        std::memcpy(data, &rx_frame.frame.data[0], data_size);
        result.success = true;
        result.data_size = data_size;
        result.timestamp_ns = rx_frame.timestamp_ns;
        result.hw_timestamp_ns = rx_frame.hw_timestamp_ns;
        return result;
    }

    return result;
}

std::uint16_t BatteryCan::check_crc16(const std::uint8_t* data, std::size_t size)
{
    uint16_t crc = 0xFFFF; // Initial value
    // 0xA001 is the polynomial used in this CRC-16 algorithm
    for (size_t i = 0; i < size; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

} // namespace slpmu

