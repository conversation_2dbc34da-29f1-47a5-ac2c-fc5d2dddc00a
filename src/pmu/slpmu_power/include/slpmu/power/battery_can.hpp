/**
 * battery_can.hpp
 * CAN-based battery monitor implementation BatteryCom
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-25
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "i_battery.hpp"
#include <slcan/slcan/socket_can.hpp>
#include <memory>

namespace slpmu {

class BatteryCan : public IBattery {
public:
    explicit BatteryCan(const IBattery::Config& cfg = IBattery::Config::defaultConfig());
    ~BatteryCan();

    bool open() override;
    bool close() override;
    bool is_open() const override;

public:
    TimestampedValue<std::uint16_t> read_voltage() override;
    TimestampedValue<std::int16_t> read_current() override;
    TimestampedValue<std::uint16_t> read_capacity() override;
    TimestampedValue<std::int16_t> read_cycle_count() override;
    TimestampedValue<std::uint16_t> read_percentage() override;

    float get_voltage_scale() override;
    float get_current_scale() override;
    float get_capacity_scale() override;

    FullStatus read_full_status() override;

private:
    struct BatteryReadResult {
        bool success;
        std::size_t data_size;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

private:
    BatteryReadResult battery_read(std::uint32_t id, void* data, std::size_t size, std::int64_t timeout_ns = -1);
    std::uint16_t check_crc16(const std::uint8_t* data, std::size_t size);

private:
    IBattery::Config config_;
    slcan::SocketCan can_dev_;
    bool is_open_ {false};
};

} // namespace slpmu

