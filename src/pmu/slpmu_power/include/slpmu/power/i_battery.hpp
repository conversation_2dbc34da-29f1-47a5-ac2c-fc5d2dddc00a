/**
 * i_battery.hpp
 * Abstract interface for battery monitor
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-25
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include <cstdint>
#include <optional>
#include <string>

namespace slpmu {

class IBattery {
public:
    struct Config {
        std::string can_interface;
        std::uint32_t can_id_battery_info;   // 0x100
        std::uint32_t can_id_battery_status; // 0x101
        std::int64_t read_timeout_ns;        // e.g., 100ms

        static inline Config defaultConfig() {
            return {"can0", 0x100, 0x101, 100'000'000};
        }
    };

    template<typename T>
    struct TimestampedValue {
        bool success;
        T value;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

public:
    virtual ~IBattery() = default;

    virtual bool open() = 0;
    virtual bool close() = 0;
    virtual bool is_open() const = 0;

    virtual TimestampedValue<std::uint16_t> read_voltage() = 0;      //
    virtual TimestampedValue<std::int16_t> read_current() = 0;      //
    virtual TimestampedValue<std::uint16_t> read_capacity() = 0;     //
    virtual TimestampedValue<std::int16_t> read_cycle_count() = 0;
    virtual TimestampedValue<std::uint16_t> read_percentage() = 0;   // %

    virtual float get_voltage_scale() = 0;
    virtual float get_current_scale() = 0;
    virtual float get_capacity_scale() = 0;


    // Convenience method to read full status in one shot
    struct FullStatus {
        bool success;
        std::uint16_t voltage;
        std::int16_t current;
        std::uint16_t capacity;
        std::int16_t cycle_count;
        std::uint16_t percentage;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };
    virtual FullStatus read_full_status() = 0;
};

} // namespace slpmu

