cmake_minimum_required(VERSION 3.8)
project(slpmu_power)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(slcan REQUIRED)

file(GLOB SLPMU_POWER_SRC src/*.cpp)

add_library(slpmu_power STATIC ${SLPMU_POWER_SRC})
target_include_directories(slpmu_power PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_link_libraries(slpmu_power PUBLIC slcan)

install(TARGETS slpmu_power
  EXPORT slpmu_power_targets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES DESTINATION include)

install(DIRECTORY include/
  DESTINATION include)

set(SLPMU_POWER_CMAKE_DIR share/slpmu_power/cmake)
include(CMakePackageConfigHelpers)
configure_package_config_file(
  slpmu_power-config.cmake.in
  ${PROJECT_BINARY_DIR}/cmake/slpmu_power/slpmu_power-config.cmake
  PATH_VARS SLPMU_POWER_CMAKE_DIR
  INSTALL_DESTINATION ${CMAKE_INSTALL_PREFIX}/share/slpmu_power
)

install(
  EXPORT slpmu_power_targets
  DESTINATION share/slpmu_power/cmake/
  FILE slpmu_power_targets.cmake
)

install(
  FILES ${PROJECT_BINARY_DIR}/cmake/slpmu_power/slpmu_power-config.cmake
  DESTINATION share/slpmu_power/
)

# Build power_utils binary if bin directory exists
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/bin/power_utils.cpp)
  add_executable(power_utils bin/power_utils.cpp)
  target_link_libraries(power_utils PRIVATE slpmu_power)
  target_compile_features(power_utils PRIVATE cxx_std_17)
  
  install(TARGETS power_utils
    RUNTIME DESTINATION bin
  )
endif()

