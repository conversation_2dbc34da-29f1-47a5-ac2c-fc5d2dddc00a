/**
 * power_utils.cpp
 * Battery diagnostic utilities
 */

#include <slpmu/power/battery_can.hpp>
#include <iostream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <csignal>
#include <cstring>
#include <memory>

static volatile bool g_running = true;

void signal_handler(int) { g_running = false; }

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " --driver can --if <interface> [--timeout <ms>] <command>\n"
              << "\nCommands:\n"
              << "  info        Read voltage & current\n"
              << "  status      Read capacity/cycle/percentage\n"
              << "  all         Read everything\n"
              << "\nOptions:\n"
              << "  --if <iface>      CAN interface (default: can0)\n"
              << "  --timeout <ms>    Read timeout (default: 100)\n"
              << std::endl;
}

enum class Command { Info, Status, All };

std::string ts(std::optional<std::int64_t> t) {
    if (!t.has_value()) return "N/A";
    auto s = t.value() / 1'000'000'000;
    auto ns = t.value() % 1'000'000'000;
    std::ostringstream oss; oss << s << "." << std::setw(9) << std::setfill('0') << ns; return oss.str();
}

int main(int argc, char* argv[]) {
    std::string iface = "can0";
    std::int64_t timeout_ms = 100;
    Command cmd = Command::All;

    if (argc < 2) { print_usage(argv[0]); return 1; }

    for (int i = 1; i < argc; ++i) {
        if (std::strcmp(argv[i], "--if") == 0 && i + 1 < argc) {
            iface = argv[++i];
        } else if (std::strcmp(argv[i], "--timeout") == 0 && i + 1 < argc) {
            timeout_ms = std::stoll(argv[++i]);
        } else if (std::strcmp(argv[i], "info") == 0) {
            cmd = Command::Info;
        } else if (std::strcmp(argv[i], "status") == 0) {
            cmd = Command::Status;
        } else if (std::strcmp(argv[i], "all") == 0) {
            cmd = Command::All;
        }
    }

    slpmu::IBattery::Config cfg;
    cfg.can_interface = iface;
    cfg.can_id_battery_info = 0x100;
    cfg.can_id_battery_status = 0x101;
    cfg.read_timeout_ns = timeout_ms * 1'000'000;

    slpmu::BatteryCan battery(cfg);
    std::cout << "Opening battery on " << iface << " ..." << std::endl;
    if (!battery.open()) {
        std::cerr << "Failed to open CAN battery device on " << iface << std::endl;
        return 1;
    }

    std::signal(SIGINT, signal_handler);

    auto sleep_dur = std::chrono::milliseconds(1000);
    while (g_running) {
        if (cmd == Command::Info) {
            auto v = battery.read_voltage();
            auto c = battery.read_current();
            auto scale_v = battery.get_voltage_scale();
            auto scale_c = battery.get_current_scale();
            if (v.success && c.success) {
                std::cout << "Voltage: " << std::fixed << std::setprecision(2) << v.value * scale_v << " V"
                          << " | Current: " << c.value * scale_c << " A"
                          << " | TS: " << ts(c.hw_timestamp_ns) << std::endl;
            } else {
                std::cout << "Failed to read info" << std::endl;
            }
        }

        if (cmd == Command::Status) {
            auto cap = battery.read_capacity();
            auto cc = battery.read_cycle_count();
            auto pct = battery.read_percentage();
            auto scale_cap = battery.get_capacity_scale();
            if (cap.success && cc.success && pct.success) {
                std::cout << "Capacity: " << cap.value * scale_cap << " Ah"
                          << " | Cycles: " << cc.value
                          << " | Percent: " << pct.value << "%"
                          << " | TS: " << ts(pct.hw_timestamp_ns) << std::endl;
            } else {
                std::cout << "Failed to read status" << std::endl;
            }
        }

        if (cmd == Command::All) {
            auto fs = battery.read_full_status();
            auto scale_v = battery.get_voltage_scale();
            auto scale_c = battery.get_current_scale();
            auto scale_cap = battery.get_capacity_scale();
            if (fs.success) {
                std::cout << "ALL => V=" << fs.voltage * scale_v << "V, I=" << fs.current * scale_c << "A, Cap=" << fs.capacity * scale_cap
                          << "Ah, Cycles=" << fs.cycle_count << ", %=" << fs.percentage
                          << " | TS: " << ts(fs.hw_timestamp_ns) << std::endl;
            }
            else
            {
                std::cout << "Failed to read all" << std::endl;
            }
        }

        std::this_thread::sleep_for(sleep_dur);
    }

    battery.close();
    return 0;
}

