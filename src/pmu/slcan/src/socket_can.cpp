/**
 * socket_can.cpp
 * Low level SocketCAN interface
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slcan/slcan/socket_can.hpp>

#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <linux/can/raw.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <poll.h>

namespace slcan {

SocketCan::SocketCan(const Config& config)
    : config_(config) {
}

SocketCan::~SocketCan() {
    close();
}

bool SocketCan::open() {
    if (socket_fd_ >= 0) {
        return true;
    }

    socket_fd_ = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (socket_fd_ < 0) {
        return false;
    }

    struct ifreq ifr;
    std::memset(&ifr, 0, sizeof(ifr));
    std::strncpy(ifr.ifr_name, config_.iface.c_str(), IFNAMSIZ - 1);
    
    if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
        ::close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    struct sockaddr_can addr;
    std::memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;

    if (bind(socket_fd_, reinterpret_cast<struct sockaddr*>(&addr), sizeof(addr)) < 0) {
        ::close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    if (!config_.filters.empty()) {
        if (setsockopt(socket_fd_, SOL_CAN_RAW, CAN_RAW_FILTER,
                      config_.filters.data(),
                      config_.filters.size() * sizeof(struct can_filter)) < 0) {
            ::close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }
    }

    if (static_cast<std::uint64_t>(config_.flags) & static_cast<std::uint64_t>(CanDeviceFlags::Timestamp)) {
        int enable = 1;
        if (setsockopt(socket_fd_, SOL_SOCKET, SO_TIMESTAMP, &enable, sizeof(enable)) < 0) {
            ::close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }
    }

    if (static_cast<std::uint64_t>(config_.flags) & static_cast<std::uint64_t>(CanDeviceFlags::HardwareTimestamp)) {
        int enable = 1;
        if (setsockopt(socket_fd_, SOL_SOCKET, SO_TIMESTAMPNS, &enable, sizeof(enable)) < 0) {
            ::close(socket_fd_);
            socket_fd_ = -1;
            return false;
        }
    }

    return true;
}

bool SocketCan::close() {
    if (socket_fd_ >= 0) {
        ::close(socket_fd_);
        socket_fd_ = -1;
    }
    return true;
}

bool SocketCan::send_frame(const struct can_frame& frame) {
    if (socket_fd_ < 0) {
        return false;
    }

    ssize_t nbytes = write(socket_fd_, &frame, sizeof(frame));
    return nbytes == sizeof(frame);
}

bool SocketCan::recv_frame(ReceiveFrame& frame, std::int64_t timeout_ns) {
    if (socket_fd_ < 0) {
        return false;
    }

    if (timeout_ns >= 0) {
        struct pollfd pfd;
        pfd.fd = socket_fd_;
        pfd.events = POLLIN;
        pfd.revents = 0;

        int timeout_ms = (timeout_ns == 0) ? 0 : (timeout_ns / 1000000);
        int ret = poll(&pfd, 1, timeout_ms);
        
        if (ret <= 0) {
            return false;
        }
    }

    struct iovec iov;
    iov.iov_base = &frame.frame;
    iov.iov_len = sizeof(frame.frame);

    char control[256];
    struct msghdr msg;
    std::memset(&msg, 0, sizeof(msg));
    msg.msg_iov = &iov;
    msg.msg_iovlen = 1;
    msg.msg_control = control;
    msg.msg_controllen = sizeof(control);

    ssize_t nbytes = recvmsg(socket_fd_, &msg, 0);
    if (nbytes != sizeof(frame.frame)) {
        return false;
    }

    frame.timestamp_ns = std::nullopt;
    frame.hw_timestamp_ns = std::nullopt;

    struct cmsghdr* cmsg;
    for (cmsg = CMSG_FIRSTHDR(&msg); cmsg != nullptr; cmsg = CMSG_NXTHDR(&msg, cmsg)) {
        if (cmsg->cmsg_level == SOL_SOCKET) {
            if (cmsg->cmsg_type == SO_TIMESTAMP) {
                struct timeval* tv = reinterpret_cast<struct timeval*>(CMSG_DATA(cmsg));
                frame.timestamp_ns = tv->tv_sec * 1000000000LL + tv->tv_usec * 1000LL;
            } else if (cmsg->cmsg_type == SO_TIMESTAMPNS) {
                struct timespec* ts = reinterpret_cast<struct timespec*>(CMSG_DATA(cmsg));
                frame.hw_timestamp_ns = ts->tv_sec * 1000000000LL + ts->tv_nsec;
            }
        }
    }

    return true;
}

}

