/**
 * canopen.cpp
 * CANopen interface
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slcan/slcan/canopen.hpp>
#include <cstring>
#include <chrono>

namespace slcan {

constexpr std::uint32_t kSdoTxCobIdBase = 0x600;
constexpr std::uint32_t kSdoRxCobIdBase = 0x580;

constexpr std::uint8_t kSdoCcsDownloadInit = 0x20;
constexpr std::uint8_t kSdoCcsUploadInit = 0x40;
constexpr std::uint8_t kSdoScsDownloadInit = 0x60;
constexpr std::uint8_t kSdoScsUploadInit = 0x40;
constexpr std::uint8_t kSdoCsAbort = 0x80;

constexpr std::uint8_t kSdoExpedited = 0x02;
constexpr std::uint8_t kSdoSizeIndicated = 0x01;

CANopen::CANopen(const Config& config)
    : config_(config)
    , can_dev_(SocketCan::Config{
        config.iface,
        config.flags,
        {{kSdoRxCobIdBase + config.node_id, 0x7FF}}
      }) {
}

CANopen::~CANopen() {
    close();
}

bool CANopen::open() {
    return can_dev_.open();
}

bool CANopen::close() {
    return can_dev_.close();
}

CANopen::SdoReadResult CANopen::sdo_read(std::uint16_t index, std::uint8_t subindex, void* data, std::size_t size, std::int64_t timeout_ns) {
    SdoReadResult result{false, 0, std::nullopt, std::nullopt};
    
    if (!data || size == 0 || size > 4) {
        return result;
    }

    struct can_frame tx_frame;
    std::memset(&tx_frame, 0, sizeof(tx_frame));
    tx_frame.can_id = kSdoTxCobIdBase + config_.node_id;
    tx_frame.can_dlc = 8;
    
    tx_frame.data[0] = kSdoCcsUploadInit;
    tx_frame.data[1] = index & 0xFF;
    tx_frame.data[2] = (index >> 8) & 0xFF;
    tx_frame.data[3] = subindex;
    std::memset(&tx_frame.data[4], 0, 4);

    if (!can_dev_.send_frame(tx_frame)) {
        return result;
    }

    auto start_time = std::chrono::steady_clock::now();
    std::int64_t remaining_timeout = timeout_ns;

    while (true) {
        if (timeout_ns >= 0) {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto elapsed_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(elapsed).count();
            remaining_timeout = timeout_ns - elapsed_ns;
            if (remaining_timeout <= 0) {
                return result;
            }
        }

        SocketCan::ReceiveFrame rx_frame;
        if (!can_dev_.recv_frame(rx_frame, remaining_timeout)) {
            return result;
        }

        if (rx_frame.frame.can_id != (kSdoRxCobIdBase + config_.node_id)) {
            continue;
        }

        if (rx_frame.frame.can_dlc < 8) {
            return result;
        }

        std::uint8_t cs = rx_frame.frame.data[0] & 0xE0;
        
        if (cs == kSdoCsAbort) {
            return result;
        }

        if (cs == kSdoScsUploadInit) {
            std::uint16_t rx_index = rx_frame.frame.data[1] | (rx_frame.frame.data[2] << 8);
            std::uint8_t rx_subindex = rx_frame.frame.data[3];
            
            if (rx_index != index || rx_subindex != subindex) {
                return result;
            }

            if (rx_frame.frame.data[0] & kSdoExpedited) {
                std::size_t data_size = size;
                if (rx_frame.frame.data[0] & kSdoSizeIndicated) {
                    std::uint8_t n = (rx_frame.frame.data[0] >> 2) & 0x03;
                    data_size = 4 - n;
                    if (data_size > size) {
                        data_size = size;
                    }
                }
                
                std::memcpy(data, &rx_frame.frame.data[4], data_size);
                result.success = true;
                result.data_size = data_size;
                result.timestamp_ns = rx_frame.timestamp_ns;
                result.hw_timestamp_ns = rx_frame.hw_timestamp_ns;
                return result;
            } else {
                return result;
            }
        }
    }

    return result;
}

bool CANopen::sdo_write(std::uint16_t index, std::uint8_t subindex, const void* data, std::size_t size, std::int64_t timeout_ns) {
    if (!data || size == 0 || size > 4) {
        return false;
    }

    struct can_frame tx_frame;
    std::memset(&tx_frame, 0, sizeof(tx_frame));
    tx_frame.can_id = kSdoTxCobIdBase + config_.node_id;
    tx_frame.can_dlc = 8;
    
    std::uint8_t n = 4 - size;
    tx_frame.data[0] = kSdoCcsDownloadInit | kSdoExpedited | kSdoSizeIndicated | (n << 2);
    tx_frame.data[1] = index & 0xFF;
    tx_frame.data[2] = (index >> 8) & 0xFF;
    tx_frame.data[3] = subindex;
    
    std::memcpy(&tx_frame.data[4], data, size);
    if (size < 4) {
        std::memset(&tx_frame.data[4 + size], 0, 4 - size);
    }

    if (!can_dev_.send_frame(tx_frame)) {
        return false;
    }

    auto start_time = std::chrono::steady_clock::now();
    std::int64_t remaining_timeout = timeout_ns;

    while (true) {
        if (timeout_ns >= 0) {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto elapsed_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(elapsed).count();
            remaining_timeout = timeout_ns - elapsed_ns;
            if (remaining_timeout <= 0) {
                return false;
            }
        }

        SocketCan::ReceiveFrame rx_frame;
        if (!can_dev_.recv_frame(rx_frame, remaining_timeout)) {
            return false;
        }

        if (rx_frame.frame.can_id != (kSdoRxCobIdBase + config_.node_id)) {
            continue;
        }

        if (rx_frame.frame.can_dlc < 8) {
            return false;
        }

        std::uint8_t cs = rx_frame.frame.data[0] & 0xE0;
        
        if (cs == kSdoCsAbort) {
            return false;
        }

        if (cs == kSdoScsDownloadInit) {
            std::uint16_t rx_index = rx_frame.frame.data[1] | (rx_frame.frame.data[2] << 8);
            std::uint8_t rx_subindex = rx_frame.frame.data[3];
            
            if (rx_index != index || rx_subindex != subindex) {
                return false;
            }

            return true;
        }
    }

    return false;
}

}