cmake_minimum_required(VERSION 3.8)
project(slcan)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

FILE(GLOB SLCAN_SRC src/*.cpp)

add_library(slcan STATIC ${SLCAN_SRC})
target_include_directories(slcan PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)

install(TARGETS slcan
  EXPORT slcan_targets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES DESTINATION include)

install(DIRECTORY include/
  DESTINATION include)

set(SLCAN_CMAKE_DIR share/slcan/cmake)
include(CMakePackageConfigHelpers)
configure_package_config_file(
  slcan-config.cmake.in
  ${PROJECT_BINARY_DIR}/cmake/slcan/slcan-config.cmake
  PATH_VARS SLCAN_CMAKE_DIR
  INSTALL_DESTINATION ${CMAKE_INSTALL_PREFIX}/share/slcan
)

install(
  EXPORT slcan_targets
  DESTINATION share/slcan/cmake/
  FILE slcan_targets.cmake
)

install(
  FILES ${PROJECT_BINARY_DIR}/cmake/slcan/slcan-config.cmake
  DESTINATION share/slcan/
)