/**
 * canopen.hpp
 * CANopen interface
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "socket_can.hpp"
#include <optional>

namespace slcan {

class CANopen {
public:
    struct Config {
        std::string iface;
        std::uint16_t node_id;
        SocketCan::CanDeviceFlags flags;

        static inline Config defaultConfig() {
            return {
                "can0",
                1,
                SocketCan::CanDeviceFlags::All,
            };
        }
    };

public:
    explicit CANopen(const Config& config = Config::defaultConfig());
    ~CANopen();

    CANopen(const CANopen&) = delete;
    CANopen& operator=(const CANopen&) = delete;

    CANopen(CANopen&&) = delete;
    CANopen& operator=(CANopen&&) = delete;

public:
    struct SdoReadResult {
        bool success;
        std::size_t data_size;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

public:
    bool open();
    bool close();

    SdoReadResult sdo_read(std::uint16_t index, std::uint8_t subindex, void* data, std::size_t size, std::int64_t timeout_ns = -1);
    bool sdo_write(std::uint16_t index, std::uint8_t subindex, const void* data, std::size_t size, std::int64_t timeout_ns = -1);

private:
    Config config_;
    SocketCan can_dev_;
};

}
