/**
 * motor_controller_can.cpp
 * CANopen-based motor controller implementation
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slpmu/motor/motor_controller_can.hpp>
#include <slcan/slcan/canopen.hpp>
#include <cstring>
#include <iostream>

namespace slpmu {

namespace {

constexpr std::uint16_t kIndexControl = 0x6040;
constexpr std::uint16_t kIndexRunMode = 0x6060;
constexpr std::uint16_t kIndexDestSpeed = 0x60FF;
constexpr std::uint16_t kIndexPosition = 0x6064;
constexpr std::uint16_t kIndexSpeed = 0x606C;
constexpr std::uint16_t kIndexTorque = 0x6071;
constexpr std::uint16_t kIndexAccTime = 0x6083;
constexpr std::uint16_t kIndexDecTime = 0x6084;
constexpr std::uint16_t kIndexCurrentFb = 0x6077;
constexpr std::uint16_t kIndexTemperature = 0x2032;
constexpr std::uint16_t kIndexLastAlarm = 0x603F;
constexpr std::uint16_t kIndexDinStatus = 0x2003;
constexpr std::uint16_t kIndexDoutControl = 0x2030;

constexpr std::uint8_t kSubindexDefault = 0x00;
constexpr std::uint8_t kSubindexLeft = 0x01;
constexpr std::uint8_t kSubindexRight = 0x02;
constexpr std::uint8_t kSubindexBoth = 0x03;
constexpr std::uint8_t kSubindexDoutControl = 0x04;
constexpr std::uint8_t kSubindexDriverTemp = 0x03;

std::uint8_t motor_index_to_subindex(IMotorController::MotorIndex motor) {
    switch (motor) {
        case IMotorController::MotorIndex::Left:
            return kSubindexLeft;
        case IMotorController::MotorIndex::Right:
            return kSubindexRight;
        case IMotorController::MotorIndex::Both:
            return kSubindexBoth;
        default:
            return kSubindexDefault;
    }
}

}  // namespace

MotorControllerCan::MotorControllerCan(const Config& config)
    : config_(config)
    , canopen_(std::make_unique<slcan::CANopen>(slcan::CANopen::Config{
        config.can_interface,
        config.node_id,
        slcan::SocketCan::CanDeviceFlags::All
      }))
    , is_open_(false) {
}

MotorControllerCan::~MotorControllerCan() {
    if (is_open_) {
        close();
    }
}

bool MotorControllerCan::open() {
    if (is_open_) {
        return true;
    }
    
    if (canopen_->open()) {
        is_open_ = true;
        return true;
    }
    
    std::cerr << "MotorControllerCan: Failed to open CANopen device on " << config_.can_interface 
              << " with node ID " << config_.node_id << std::endl;
    return false;
}

bool MotorControllerCan::close() {
    if (!is_open_) {
        return true;
    }
    
    if (canopen_->close()) {
        is_open_ = false;
        return true;
    }
    
    return false;
}

bool MotorControllerCan::is_open() const {
    return is_open_;
}

bool MotorControllerCan::send_control_command(ControlCommand command) {
    if (!is_open_) {
        return false;
    }
    
    std::uint16_t cmd_value = static_cast<std::uint16_t>(command);
    return canopen_->sdo_write(kIndexControl, kSubindexDefault, &cmd_value, sizeof(cmd_value), config_.sdo_timeout_ns);
}

bool MotorControllerCan::set_run_mode(RunMode mode) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t mode_value = static_cast<std::uint8_t>(mode);
    return canopen_->sdo_write(kIndexRunMode, kSubindexDefault, &mode_value, sizeof(mode_value), config_.sdo_timeout_ns);
}

bool MotorControllerCan::set_speed(MotorIndex motor, std::int32_t speed_rpm) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    
    if (motor == MotorIndex::Both) {
        std::uint32_t combined_speed = (static_cast<std::uint32_t>(speed_rpm) << 16) | 
                                       (static_cast<std::uint32_t>(speed_rpm) & 0xFFFF);
        return canopen_->sdo_write(kIndexDestSpeed, subindex, &combined_speed, sizeof(combined_speed), config_.sdo_timeout_ns);
    } else {
        return canopen_->sdo_write(kIndexDestSpeed, subindex, &speed_rpm, sizeof(speed_rpm), config_.sdo_timeout_ns);
    }
}

bool MotorControllerCan::set_position(MotorIndex motor, std::int32_t position) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    return canopen_->sdo_write(kIndexPosition, subindex, &position, sizeof(position), config_.sdo_timeout_ns);
}

bool MotorControllerCan::set_torque(MotorIndex motor, std::int16_t torque) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    return canopen_->sdo_write(kIndexTorque, subindex, &torque, sizeof(torque), config_.sdo_timeout_ns);
}

bool MotorControllerCan::set_acceleration_time(MotorIndex motor, std::uint32_t time_ms) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    return canopen_->sdo_write(kIndexAccTime, subindex, &time_ms, sizeof(time_ms), config_.sdo_timeout_ns);
}

bool MotorControllerCan::set_deceleration_time(MotorIndex motor, std::uint32_t time_ms) {
    if (!is_open_) {
        return false;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    return canopen_->sdo_write(kIndexDecTime, subindex, &time_ms, sizeof(time_ms), config_.sdo_timeout_ns);
}

MotorControllerCan::TimestampedValue<std::int32_t> MotorControllerCan::read_position(MotorIndex motor) {
    TimestampedValue<std::int32_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    std::int32_t position = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexPosition, subindex, &position, sizeof(position), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = position;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

MotorControllerCan::TimestampedValue<std::int32_t> MotorControllerCan::read_speed(MotorIndex motor) {
    TimestampedValue<std::int32_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    
    if (motor == MotorIndex::Both) {
        std::uint32_t combined_speed = 0;
        auto sdo_result = canopen_->sdo_read(kIndexSpeed, subindex, &combined_speed, sizeof(combined_speed), config_.sdo_timeout_ns);
        
        result.success = sdo_result.success;
        result.value = static_cast<std::int32_t>(combined_speed);
        result.timestamp_ns = sdo_result.timestamp_ns;
        result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    } else {
        std::int32_t speed = 0;
        auto sdo_result = canopen_->sdo_read(kIndexSpeed, subindex, &speed, sizeof(speed), config_.sdo_timeout_ns);
        
        result.success = sdo_result.success;
        result.value = speed;
        result.timestamp_ns = sdo_result.timestamp_ns;
        result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    }
    
    return result;
}

MotorControllerCan::TimestampedValue<std::int16_t> MotorControllerCan::read_current(MotorIndex motor) {
    TimestampedValue<std::int16_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    std::int16_t current = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexCurrentFb, subindex, &current, sizeof(current), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = current;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

MotorControllerCan::TimestampedValue<std::int16_t> MotorControllerCan::read_temperature(MotorIndex motor) {
    TimestampedValue<std::int16_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint8_t subindex = motor_index_to_subindex(motor);
    std::int16_t temperature = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexTemperature, subindex, &temperature, sizeof(temperature), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = temperature;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

MotorControllerCan::TimestampedValue<std::int16_t> MotorControllerCan::read_driver_temperature() {
    TimestampedValue<std::int16_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::int16_t temperature = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexTemperature, kSubindexDriverTemp, &temperature, sizeof(temperature), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = temperature;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

MotorControllerCan::TimestampedValue<std::uint32_t> MotorControllerCan::read_alarm_status() {
    TimestampedValue<std::uint32_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint32_t alarm_code = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexLastAlarm, kSubindexDefault, &alarm_code, sizeof(alarm_code), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = alarm_code;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

MotorControllerCan::TimestampedValue<std::uint32_t> MotorControllerCan::read_gpio_inputs() {
    TimestampedValue<std::uint32_t> result{false, 0, std::nullopt, std::nullopt};
    
    if (!is_open_) {
        return result;
    }
    
    std::uint32_t gpio_status = 0;
    
    auto sdo_result = canopen_->sdo_read(kIndexDinStatus, kSubindexDefault, &gpio_status, sizeof(gpio_status), config_.sdo_timeout_ns);
    
    result.success = sdo_result.success;
    result.value = gpio_status;
    result.timestamp_ns = sdo_result.timestamp_ns;
    result.hw_timestamp_ns = sdo_result.hw_timestamp_ns;
    
    return result;
}

bool MotorControllerCan::set_gpio_outputs(std::uint32_t outputs, std::uint32_t mask) {
    if (!is_open_) {
        return false;
    }
    
    std::uint32_t current_outputs = 0;
    auto read_result = canopen_->sdo_read(kIndexDoutControl, kSubindexDoutControl, &current_outputs, sizeof(current_outputs), config_.sdo_timeout_ns);
    
    if (!read_result.success) {
        return false;
    }
    
    std::uint32_t new_outputs = (current_outputs & ~mask) | (outputs & mask);
    
    return canopen_->sdo_write(kIndexDoutControl, kSubindexDoutControl, &new_outputs, sizeof(new_outputs), config_.sdo_timeout_ns);
}

}