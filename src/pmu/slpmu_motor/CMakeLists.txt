cmake_minimum_required(VERSION 3.8)
project(slpmu_motor)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(slcan REQUIRED)

FILE(GLOB SLPMU_MOTOR_SRC src/*.cpp)

add_library(slpmu_motor STATIC ${SLPMU_MOTOR_SRC})
target_include_directories(slpmu_motor PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_link_libraries(slpmu_motor PUBLIC slcan)

install(TARGETS slpmu_motor
  EXPORT slpmu_motor_targets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES DESTINATION include)

install(DIRECTORY include/
  DESTINATION include)

set(SLPMU_MOTOR_CMAKE_DIR share/slpmu_motor/cmake)
include(CMakePackageConfigHelpers)
configure_package_config_file(
  slpmu_motor-config.cmake.in
  ${PROJECT_BINARY_DIR}/cmake/slpmu_motor/slpmu_motor-config.cmake
  PATH_VARS SLPMU_MOTOR_CMAKE_DIR
  INSTALL_DESTINATION ${CMAKE_INSTALL_PREFIX}/share/slpmu_motor
)

install(
  EXPORT slpmu_motor_targets
  DESTINATION share/slpmu_motor/cmake/
  FILE slpmu_motor_targets.cmake
)

install(
  FILES ${PROJECT_BINARY_DIR}/cmake/slpmu_motor/slpmu_motor-config.cmake
  DESTINATION share/slpmu_motor/
)

# Build motor_utils binary if bin directory exists
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/bin/motor_utils.cpp)
  add_executable(motor_utils bin/motor_utils.cpp)
  target_link_libraries(motor_utils PRIVATE slpmu_motor)
  target_compile_features(motor_utils PRIVATE cxx_std_17)
  
  install(TARGETS motor_utils
    RUNTIME DESTINATION bin
  )
endif()