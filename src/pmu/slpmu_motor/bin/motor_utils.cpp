/**
 * motor_utils.cpp
 * Motor controller diagnostic utilities
 *
 * Created by <PERSON> (<EMAIL>) on 2025-08-24
 * Copyright (c) 2025 SLAMTEC Co., Ltd.
 */

#include <slpmu/motor/motor_controller_can.hpp>
#include <iostream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <csignal>
#include <cstring>
#include <memory>
#include <string>
#include <map>

static volatile bool g_running = true;

void signal_handler(int signal) {
    (void)signal;
    g_running = false;
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " --driver <driver_type> --if <interface> --node <node_id> <command> [options]\n"
              << "\nDriver options:\n"
              << "  --driver can       Use CAN driver\n"
              << "  --if <interface>   Network interface (e.g., can0)\n"
              << "  --node <node_id>   CANopen node ID (e.g., 1)\n"
              << "\nCommands:\n"
              << "  position           Read motor positions\n"
              << "  speed              Read motor speeds\n"
              << "  current            Read motor currents\n"
              << "  temperature        Read motor temperatures\n"
              << "  driver-temp        Read driver temperature\n"
              << "  alarm              Read alarm status\n"
              << "  gpio               Read GPIO inputs\n"
              << "  all                Read all status\n"
              << "\nOptions:\n"
              << "  --rate <hz>        Update rate in Hz (default: 1)\n"
              << "  --motor <l|r|b>    Motor selection: left, right, both (default: both)\n"
              << "  --timeout <ms>     SDO timeout in milliseconds (default: 100)\n"
              << std::endl;
}

enum class Command {
    Position,
    Speed,
    Current,
    Temperature,
    DriverTemp,
    Alarm,
    Gpio,
    All
};

std::string format_timestamp(std::optional<std::int64_t> timestamp_ns) {
    if (!timestamp_ns.has_value()) {
        return "N/A";
    }
    
    // ROS2 format: seconds.nanoseconds
    auto seconds = timestamp_ns.value() / 1'000'000'000;
    auto nanoseconds = timestamp_ns.value() % 1'000'000'000;
    
    std::ostringstream oss;
    oss << seconds << "." << std::setfill('0') << std::setw(9) << nanoseconds;
    return oss.str();
}

void print_motor_position(slpmu::IMotorController& controller, slpmu::IMotorController::MotorIndex motor) {
    // Position cannot be read with "Both" subindex, need to read left and right separately
    if (motor == slpmu::IMotorController::MotorIndex::Both) {
        auto left_result = controller.read_position(slpmu::IMotorController::MotorIndex::Left);
        auto right_result = controller.read_position(slpmu::IMotorController::MotorIndex::Right);
        
        if (!left_result.success || !right_result.success) {
            std::cout << "Failed to read position (SDO timeout or communication error)" << std::endl;
            if (!left_result.success) std::cout << "  Left motor failed" << std::endl;
            if (!right_result.success) std::cout << "  Right motor failed" << std::endl;
            return;
        }
        
        std::cout << "[Left]  Position: " << std::setw(10) << left_result.value
                  << " | SW_TS: " << format_timestamp(left_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(left_result.hw_timestamp_ns)
                  << std::endl;
        std::cout << "[Right] Position: " << std::setw(10) << right_result.value
                  << " | SW_TS: " << format_timestamp(right_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(right_result.hw_timestamp_ns)
                  << std::endl;
    } else {
        auto result = controller.read_position(motor);
        
        if (!result.success) {
            std::cout << "Failed to read position for motor index " << static_cast<int>(motor) 
                      << " (SDO timeout or communication error)" << std::endl;
            return;
        }
        
        std::string motor_name = (motor == slpmu::IMotorController::MotorIndex::Left) ? "Left" : "Right";
        
        std::cout << "[" << motor_name << "] Position: " << result.value 
                  << " | SW_TS: " << format_timestamp(result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
                  << std::endl;
    }
}

void print_motor_speed(slpmu::IMotorController& controller, slpmu::IMotorController::MotorIndex motor) {
    auto result = controller.read_speed(motor);
    
    if (!result.success) {
        std::cout << "Failed to read speed" << std::endl;
        return;
    }
    
    std::string motor_name;
    switch (motor) {
        case slpmu::IMotorController::MotorIndex::Left:
            motor_name = "Left";
            break;
        case slpmu::IMotorController::MotorIndex::Right:
            motor_name = "Right";
            break;
        case slpmu::IMotorController::MotorIndex::Both:
            motor_name = "Both";
            break;
    }
    
    if (motor == slpmu::IMotorController::MotorIndex::Both) {
        std::int16_t left_speed = (result.value >> 16) & 0xFFFF;
        std::int16_t right_speed = result.value & 0xFFFF;
        std::cout << "[Both] Speed: L=" << left_speed << " R=" << right_speed << " RPM"
                  << " | SW_TS: " << format_timestamp(result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
                  << std::endl;
    } else {
        std::cout << "[" << motor_name << "] Speed: " << result.value << " RPM"
                  << " | SW_TS: " << format_timestamp(result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
                  << std::endl;
    }
}

void print_motor_current(slpmu::IMotorController& controller, slpmu::IMotorController::MotorIndex motor) {
    // Current cannot be read with "Both" subindex, need to read left and right separately
    if (motor == slpmu::IMotorController::MotorIndex::Both) {
        auto left_result = controller.read_current(slpmu::IMotorController::MotorIndex::Left);
        auto right_result = controller.read_current(slpmu::IMotorController::MotorIndex::Right);
        
        if (!left_result.success || !right_result.success) {
            std::cout << "Failed to read current (SDO timeout or communication error)" << std::endl;
            if (!left_result.success) std::cout << "  Left motor failed" << std::endl;
            if (!right_result.success) std::cout << "  Right motor failed" << std::endl;
            return;
        }
        
        std::cout << "[Left]  Current: " << std::setw(6) << left_result.value << " mA"
                  << " | SW_TS: " << format_timestamp(left_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(left_result.hw_timestamp_ns)
                  << std::endl;
        std::cout << "[Right] Current: " << std::setw(6) << right_result.value << " mA"
                  << " | SW_TS: " << format_timestamp(right_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(right_result.hw_timestamp_ns)
                  << std::endl;
    } else {
        auto result = controller.read_current(motor);
        
        if (!result.success) {
            std::cout << "Failed to read current for motor index " << static_cast<int>(motor)
                      << " (SDO timeout or communication error)" << std::endl;
            return;
        }
        
        std::string motor_name = (motor == slpmu::IMotorController::MotorIndex::Left) ? "Left" : "Right";
        
        std::cout << "[" << motor_name << "] Current: " << result.value << " mA"
                  << " | SW_TS: " << format_timestamp(result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
                  << std::endl;
    }
}

void print_motor_temperature(slpmu::IMotorController& controller, slpmu::IMotorController::MotorIndex motor) {
    // Temperature cannot be read with "Both" subindex, need to read left and right separately
    if (motor == slpmu::IMotorController::MotorIndex::Both) {
        auto left_result = controller.read_temperature(slpmu::IMotorController::MotorIndex::Left);
        auto right_result = controller.read_temperature(slpmu::IMotorController::MotorIndex::Right);
        
        if (!left_result.success || !right_result.success) {
            std::cout << "Failed to read temperature (SDO timeout or communication error)" << std::endl;
            if (!left_result.success) std::cout << "  Left motor failed" << std::endl;
            if (!right_result.success) std::cout << "  Right motor failed" << std::endl;
            return;
        }
        
        std::cout << "[Left]  Temperature: " << std::fixed << std::setprecision(1) << std::setw(5) 
                  << left_result.value / 10.0 << " °C"
                  << " | SW_TS: " << format_timestamp(left_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(left_result.hw_timestamp_ns)
                  << std::endl;
        std::cout << "[Right] Temperature: " << std::fixed << std::setprecision(1) << std::setw(5) 
                  << right_result.value / 10.0 << " °C"
                  << " | SW_TS: " << format_timestamp(right_result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(right_result.hw_timestamp_ns)
                  << std::endl;
    } else {
        auto result = controller.read_temperature(motor);
        
        if (!result.success) {
            std::cout << "Failed to read temperature for motor index " << static_cast<int>(motor)
                      << " (SDO timeout or communication error)" << std::endl;
            return;
        }
        
        std::string motor_name = (motor == slpmu::IMotorController::MotorIndex::Left) ? "Left" : "Right";
        
        std::cout << "[" << motor_name << "] Temperature: " << result.value / 10.0 << " °C"
                  << " | SW_TS: " << format_timestamp(result.timestamp_ns)
                  << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
                  << std::endl;
    }
}

void print_driver_temperature(slpmu::IMotorController& controller) {
    auto result = controller.read_driver_temperature();
    
    if (!result.success) {
        std::cout << "Failed to read driver temperature" << std::endl;
        return;
    }
    
    std::cout << "[Driver] Temperature: " << result.value / 10.0 << " °C"
              << " | SW_TS: " << format_timestamp(result.timestamp_ns)
              << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
              << std::endl;
}

void print_alarm_status(slpmu::IMotorController& controller) {
    auto result = controller.read_alarm_status();
    
    if (!result.success) {
        std::cout << "Failed to read alarm status" << std::endl;
        return;
    }
    
    std::cout << "Alarm Status: 0x" << std::hex << result.value << std::dec;
    if (result.value == 0) {
        std::cout << " (No alarms)";
    }
    std::cout << " | SW_TS: " << format_timestamp(result.timestamp_ns)
              << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
              << std::endl;
}

void print_gpio_inputs(slpmu::IMotorController& controller) {
    auto result = controller.read_gpio_inputs();
    
    if (!result.success) {
        std::cout << "Failed to read GPIO inputs" << std::endl;
        return;
    }
    
    std::cout << "GPIO Inputs: 0x" << std::hex << result.value << std::dec
              << " (Binary: ";
    for (int i = 31; i >= 0; --i) {
        std::cout << ((result.value >> i) & 1);
        if (i % 4 == 0 && i > 0) std::cout << " ";
    }
    std::cout << ")"
              << " | SW_TS: " << format_timestamp(result.timestamp_ns)
              << " | HW_TS: " << format_timestamp(result.hw_timestamp_ns)
              << std::endl;
}

void print_all_status(slpmu::IMotorController& controller) {
    std::cout << "=== Motor Status ===" << std::endl;
    
    print_motor_position(controller, slpmu::IMotorController::MotorIndex::Left);
    print_motor_position(controller, slpmu::IMotorController::MotorIndex::Right);
    
    print_motor_speed(controller, slpmu::IMotorController::MotorIndex::Both);
    
    print_motor_current(controller, slpmu::IMotorController::MotorIndex::Left);
    print_motor_current(controller, slpmu::IMotorController::MotorIndex::Right);
    
    print_motor_temperature(controller, slpmu::IMotorController::MotorIndex::Left);
    print_motor_temperature(controller, slpmu::IMotorController::MotorIndex::Right);
    print_driver_temperature(controller);
    
    print_alarm_status(controller);
    print_gpio_inputs(controller);
    
    std::cout << "===================" << std::endl;
}

int main(int argc, char* argv[]) {
    // Default parameters
    std::string driver_type = "can";
    std::string interface = "can0";
    std::uint16_t node_id = 1;
    Command command = Command::All;
    double rate_hz = 1.0;
    slpmu::IMotorController::MotorIndex motor = slpmu::IMotorController::MotorIndex::Both;
    std::int64_t timeout_ms = 100;
    
    // Parse command line arguments
    if (argc < 5) {
        print_usage(argv[0]);
        return 1;
    }
    
    for (int i = 1; i < argc; ++i) {
        if (std::strcmp(argv[i], "--driver") == 0 && i + 1 < argc) {
            driver_type = argv[++i];
        } else if (std::strcmp(argv[i], "--if") == 0 && i + 1 < argc) {
            interface = argv[++i];
        } else if (std::strcmp(argv[i], "--node") == 0 && i + 1 < argc) {
            node_id = std::stoi(argv[++i]);
        } else if (std::strcmp(argv[i], "--rate") == 0 && i + 1 < argc) {
            rate_hz = std::stod(argv[++i]);
        } else if (std::strcmp(argv[i], "--motor") == 0 && i + 1 < argc) {
            std::string motor_str = argv[++i];
            if (motor_str == "l" || motor_str == "left") {
                motor = slpmu::IMotorController::MotorIndex::Left;
            } else if (motor_str == "r" || motor_str == "right") {
                motor = slpmu::IMotorController::MotorIndex::Right;
            } else if (motor_str == "b" || motor_str == "both") {
                motor = slpmu::IMotorController::MotorIndex::Both;
            }
        } else if (std::strcmp(argv[i], "--timeout") == 0 && i + 1 < argc) {
            timeout_ms = std::stoi(argv[++i]);
        } else if (std::strcmp(argv[i], "position") == 0) {
            command = Command::Position;
        } else if (std::strcmp(argv[i], "speed") == 0) {
            command = Command::Speed;
        } else if (std::strcmp(argv[i], "current") == 0) {
            command = Command::Current;
        } else if (std::strcmp(argv[i], "temperature") == 0) {
            command = Command::Temperature;
        } else if (std::strcmp(argv[i], "driver-temp") == 0) {
            command = Command::DriverTemp;
        } else if (std::strcmp(argv[i], "alarm") == 0) {
            command = Command::Alarm;
        } else if (std::strcmp(argv[i], "gpio") == 0) {
            command = Command::Gpio;
        } else if (std::strcmp(argv[i], "all") == 0) {
            command = Command::All;
        }
    }
    
    // Create motor controller
    std::unique_ptr<slpmu::IMotorController> controller;
    
    if (driver_type == "can") {
        slpmu::MotorControllerCan::Config config;
        config.can_interface = interface;
        config.node_id = node_id;
        config.sdo_timeout_ns = timeout_ms * 1'000'000;
        controller = std::make_unique<slpmu::MotorControllerCan>(config);
    } else {
        std::cerr << "Unknown driver type: " << driver_type << std::endl;
        return 1;
    }
    
    // Open controller
    std::cout << "Opening motor controller on " << interface << " with node ID " << node_id << "..." << std::endl;
    if (!controller->open()) {
        std::cerr << "Failed to open motor controller" << std::endl;
        std::cerr << "Please check:" << std::endl;
        std::cerr << "  1. CAN interface is up: sudo ip link set " << interface << " up type can bitrate 500000" << std::endl;
        std::cerr << "  2. CAN interface exists: ip link show " << interface << std::endl;
        std::cerr << "  3. User has permission to access CAN interface" << std::endl;
        return 1;
    }
    
    std::cout << "Motor controller opened successfully" << std::endl;
    std::cout << "Interface: " << interface << ", Node ID: " << node_id << std::endl;
    std::cout << "Update rate: " << rate_hz << " Hz" << std::endl;
    std::cout << "SDO timeout: " << timeout_ms << " ms" << std::endl;
    std::cout << "Press Ctrl+C to exit..." << std::endl;
    std::cout << std::endl;
    
    // Install signal handler
    std::signal(SIGINT, signal_handler);
    
    // Calculate sleep duration
    auto sleep_duration = std::chrono::microseconds(static_cast<long>(1'000'000 / rate_hz));
    
    // Main loop
    while (g_running) {
        auto start = std::chrono::steady_clock::now();
        
        switch (command) {
            case Command::Position:
                print_motor_position(*controller, motor);
                break;
            case Command::Speed:
                print_motor_speed(*controller, motor);
                break;
            case Command::Current:
                print_motor_current(*controller, motor);
                break;
            case Command::Temperature:
                print_motor_temperature(*controller, motor);
                break;
            case Command::DriverTemp:
                print_driver_temperature(*controller);
                break;
            case Command::Alarm:
                print_alarm_status(*controller);
                break;
            case Command::Gpio:
                print_gpio_inputs(*controller);
                break;
            case Command::All:
                print_all_status(*controller);
                break;
        }
        
        auto elapsed = std::chrono::steady_clock::now() - start;
        auto remaining = sleep_duration - elapsed;
        
        if (remaining.count() > 0) {
            std::this_thread::sleep_for(remaining);
        }
    }
    
    std::cout << "\nShutting down..." << std::endl;
    controller->close();
    
    return 0;
}