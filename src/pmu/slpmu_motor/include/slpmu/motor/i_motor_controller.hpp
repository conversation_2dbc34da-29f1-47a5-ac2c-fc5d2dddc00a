/**
 * i_motor_controller.h
 * Abstract interface for motor controller
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include <cstdint>
#include <optional>

namespace slpmu {

class IMotorController {
public:
    enum class RunMode : std::uint8_t {
        Position = 0x01,
        Speed = 0x03,
        Torque = 0x04
    };

    enum class ControlCommand : std::uint16_t {
        Release = 0x06,
        Enable = 0x07,
        SpeedEnable = 0x0F,
        AlarmClear = 0x80
    };

    enum class MotorIndex : std::uint8_t {
        Left = 0x01,
        Right = 0x02,
        Both = 0x03
    };

    template<typename T>
    struct TimestampedValue {
        bool success;
        T value;
        std::optional<std::int64_t> timestamp_ns;
        std::optional<std::int64_t> hw_timestamp_ns;
    };

public:
    virtual ~IMotorController() = default;

protected:
    IMotorController() = default;
    IMotorController(const IMotorController&) = delete;
    IMotorController& operator=(const IMotorController&) = delete;
    IMotorController(IMotorController&&) = delete;
    IMotorController& operator=(IMotorController&&) = delete;

public:
    virtual bool open() = 0;
    virtual bool close() = 0;
    virtual bool is_open() const = 0;

    virtual bool send_control_command(ControlCommand command) = 0;
    virtual bool set_run_mode(RunMode mode) = 0;
    
    virtual bool set_speed(MotorIndex motor, std::int32_t speed_rpm) = 0;
    virtual bool set_position(MotorIndex motor, std::int32_t position) = 0;
    virtual bool set_torque(MotorIndex motor, std::int16_t torque) = 0;
    
    virtual bool set_acceleration_time(MotorIndex motor, std::uint32_t time_ms) = 0;
    virtual bool set_deceleration_time(MotorIndex motor, std::uint32_t time_ms) = 0;
    
    virtual TimestampedValue<std::int32_t> read_position(MotorIndex motor) = 0;
    virtual TimestampedValue<std::int32_t> read_speed(MotorIndex motor) = 0;
    virtual TimestampedValue<std::int16_t> read_current(MotorIndex motor) = 0;
    virtual TimestampedValue<std::int16_t> read_temperature(MotorIndex motor) = 0;
    virtual TimestampedValue<std::int16_t> read_driver_temperature() = 0;
    virtual TimestampedValue<std::uint32_t> read_alarm_status() = 0;
    
    virtual TimestampedValue<std::uint32_t> read_gpio_inputs() = 0;
    virtual bool set_gpio_outputs(std::uint32_t outputs, std::uint32_t mask) = 0;
};

}
