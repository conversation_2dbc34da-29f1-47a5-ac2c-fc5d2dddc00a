/**
 * motor_controller_can.h
 * CANopen-based motor controller implementation
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "i_motor_controller.hpp"
#include <memory>
#include <string>

namespace slcan {
class CANopen;
}

namespace slpmu {

class MotorControllerCan : public IMotorController {
public:
    struct Config {
        std::string can_interface;
        std::uint16_t node_id;
        std::int64_t sdo_timeout_ns;
        
        static inline Config defaultConfig() {
            return {
                "can0",
                0x01,
                100'000'000  // 100ms default timeout
            };
        }
    };

public:
    explicit MotorControllerCan(const Config& config = Config::defaultConfig());
    ~MotorControllerCan() override;

    MotorControllerCan(const MotorControllerCan&) = delete;
    MotorControllerCan& operator=(const MotorControllerCan&) = delete;
    MotorControllerCan(MotorControllerCan&&) = delete;
    MotorControllerCan& operator=(MotorControllerCan&&) = delete;

public:
    bool open() override;
    bool close() override;
    bool is_open() const override;

    bool send_control_command(ControlCommand command) override;
    bool set_run_mode(RunMode mode) override;
    
    bool set_speed(MotorIndex motor, std::int32_t speed_rpm) override;
    bool set_position(MotorIndex motor, std::int32_t position) override;
    bool set_torque(MotorIndex motor, std::int16_t torque) override;
    
    bool set_acceleration_time(MotorIndex motor, std::uint32_t time_ms) override;
    bool set_deceleration_time(MotorIndex motor, std::uint32_t time_ms) override;
    
    TimestampedValue<std::int32_t> read_position(MotorIndex motor) override;
    TimestampedValue<std::int32_t> read_speed(MotorIndex motor) override;
    TimestampedValue<std::int16_t> read_current(MotorIndex motor) override;
    TimestampedValue<std::int16_t> read_temperature(MotorIndex motor) override;
    TimestampedValue<std::int16_t> read_driver_temperature() override;
    TimestampedValue<std::uint32_t> read_alarm_status() override;
    
    TimestampedValue<std::uint32_t> read_gpio_inputs() override;
    bool set_gpio_outputs(std::uint32_t outputs, std::uint32_t mask) override;

private:
    Config config_;
    std::unique_ptr<slcan::CANopen> canopen_;
    bool is_open_;
};

}