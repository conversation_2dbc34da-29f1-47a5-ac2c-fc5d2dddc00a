/**
 * jack_utils.cpp
 * Jack controller diagnostic utilities
 *
 * Mirrored format from motor_utils.cpp
 */

#include <slpmu/jack/jack_controller_can.hpp>
#include <iostream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <csignal>
#include <cstring>
#include <memory>
#include <string>

static volatile bool g_running = true;

void signal_handler(int signal) {
    (void)signal;
    g_running = false;
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " --driver <driver_type> --if <interface> --node <node_id> <command> [options]\n"
              << "\nDriver options:\n"
              << "  --driver can       Use CAN driver\n"
              << "  --if <interface>   Network interface (e.g., can0)\n"
              << "  --node <node_id>   CANopen node ID (e.g., 7)\n"
              << "\nCommands:\n"
              << "  status             Read jack status (pos/status/alarm)\n"
              << "  detect-base        Start base detection\n"
              << "  lift-up            Lift to target position at rpm\n"
              << "  lift-down          Lift down to base at rpm\n"
              << "  brake              Brake/stop\n"
              << "  clear-alarm        Clear alarm\n"
              << "\nOptions:\n"
              << "  --pos <position>   Target position for lift-up\n"
              << "  --rpm <rpm>        Target speed (rpm)\n"
              << "  --rate <hz>        Update rate in Hz for status (default: 1)\n"
              << "  --timeout <ms>     SDO timeout in milliseconds (default: 200)\n"
              << std::endl;
}

struct Args {
    std::string driver_type {"can"};
    std::string interface {"can0"};
    std::uint16_t node_id {7};
    std::string command {"status"};
    int pos {0};
    int rpm {1000};
    double rate_hz {1.0};
    std::int64_t timeout_ms {200};
};

bool parse_args(int argc, char* argv[], Args& args) {
    if (argc < 5) {
        print_usage(argv[0]);
        return false;
    }
    for (int i = 1; i < argc; ++i) {
        if (std::strcmp(argv[i], "--driver") == 0 && i + 1 < argc) {
            args.driver_type = argv[++i];
        } else if (std::strcmp(argv[i], "--if") == 0 && i + 1 < argc) {
            args.interface = argv[++i];
        } else if (std::strcmp(argv[i], "--node") == 0 && i + 1 < argc) {
            args.node_id = static_cast<std::uint16_t>(std::stoi(argv[++i]));
        } else if (std::strcmp(argv[i], "--pos") == 0 && i + 1 < argc) {
            args.pos = std::stoi(argv[++i]);
        } else if (std::strcmp(argv[i], "--rpm") == 0 && i + 1 < argc) {
            args.rpm = std::stoi(argv[++i]);
        } else if (std::strcmp(argv[i], "--rate") == 0 && i + 1 < argc) {
            args.rate_hz = std::stod(argv[++i]);
        } else if (std::strcmp(argv[i], "--timeout") == 0 && i + 1 < argc) {
            args.timeout_ms = std::stoi(argv[++i]);
        } else if (std::strcmp(argv[i], "status") == 0) {
            args.command = "status";
        } else if (std::strcmp(argv[i], "detect-base") == 0) {
            args.command = "detect-base";
        } else if (std::strcmp(argv[i], "lift-up") == 0) {
            args.command = "lift-up";
        } else if (std::strcmp(argv[i], "lift-down") == 0) {
            args.command = "lift-down";
        } else if (std::strcmp(argv[i], "brake") == 0) {
            args.command = "brake";
        } else if (std::strcmp(argv[i], "clear-alarm") == 0) {
            args.command = "clear-alarm";
        }
    }
    return true;
}

int main(int argc, char* argv[]) {
    Args args;
    if (!parse_args(argc, argv, args)) {
        return 1;
    }

    if (args.driver_type != "can") {
        std::cerr << "Unknown driver type: " << args.driver_type << std::endl;
        return 1;
    }

    // Create controller
    slpmu::JackControllerCan::Config cfg;
    cfg.can_interface = args.interface;
    cfg.node_id = args.node_id;
    cfg.sdo_timeout_ns = args.timeout_ms * 1'000'000;

    slpmu::JackControllerCan controller(cfg);

    std::cout << "Opening jack controller on " << args.interface << " with node ID " << args.node_id << "..." << std::endl;
    if (!controller.open()) {
        std::cerr << "Failed to open jack controller" << std::endl;
        std::cerr << "Please check:\n"
                  << "  1. CAN interface is up: sudo ip link set " << args.interface << " up type can bitrate 500000\n"
                  << "  2. CAN interface exists: ip link show " << args.interface << "\n"
                  << "  3. User has permission to access CAN interface" << std::endl;
        return 1;
    }

    std::signal(SIGINT, signal_handler);

    if (args.command == "status") {
        auto sleep_duration = std::chrono::microseconds(static_cast<long>(1'000'000 / args.rate_hz));
        while (g_running) {
            auto pos = controller.read_actual_position();
            auto st = controller.read_status();
            auto al = controller.read_alarm();
            if (!pos.success || !st.success || !al.success) {
                std::cout << "Read failed (SDO timeout or comm issue)\n";
            } else {
                std::cout << "pos=" << pos.value
                          << " status=0x" << std::hex << st.value << std::dec
                          << " alarm=0x" << std::hex << al.value << std::dec << "\n";
            }
            std::this_thread::sleep_for(sleep_duration);
        }
    } else if (args.command == "detect-base") {
        if (!controller.set_mode_detect_base()) return 3;
        if (!controller.cmd_detect_base()) return 3;
        std::cout << "Detect base command sent. Use status to monitor.\n";
    } else if (args.command == "lift-up") {
        std::uint32_t can_speed = slpmu::JackControllerCan::rpm_to_can_speed(args.rpm);
        if (!controller.write_target_speed(can_speed)) return 3;
        if (!controller.write_target_position(args.pos)) return 3;
        if (!controller.set_mode_position()) return 3;
        if (!controller.cmd_auto_pos_change()) return 3;
        std::cout << "Lift up command sent to pos=" << args.pos << " rpm=" << args.rpm << "\n";
    } else if (args.command == "lift-down") {
        std::uint32_t can_speed = slpmu::JackControllerCan::rpm_to_can_speed(args.rpm);
        if (!controller.write_target_speed(can_speed)) return 3;
        if (!controller.write_target_position(0)) return 3;
        if (!controller.set_mode_position()) return 3;
        if (!controller.cmd_auto_pos_change()) return 3;
        std::cout << "Lift down command sent rpm=" << args.rpm << "\n";
    } else if (args.command == "brake") {
        if (!controller.cmd_brake()) return 3;
        std::cout << "Brake command sent\n";
    } else if (args.command == "clear-alarm") {
        if (!controller.cmd_clear_alarm()) return 3;
        std::cout << "Clear alarm command sent\n";
    }

    controller.close();
    return 0;
}

