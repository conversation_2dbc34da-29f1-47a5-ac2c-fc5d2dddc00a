cmake_minimum_required(VERSION 3.8)
project(slpmu_jack)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(slcan REQUIRED)

file(GLOB SLPMU_JACK_SRC src/*.cpp)

add_library(slpmu_jack STATIC ${SLPMU_JACK_SRC})
target_include_directories(slpmu_jack PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)

target_link_libraries(slpmu_jack PUBLIC slcan)

# Build jack_utils binary if exists in bin folder (mirror slpmu_motor)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/bin/jack_utils.cpp)
  add_executable(jack_utils bin/jack_utils.cpp)
  target_link_libraries(jack_utils PRIVATE slpmu_jack)
  target_compile_features(jack_utils PRIVATE cxx_std_17)
  install(TARGETS jack_utils RUNTIME DESTINATION bin)
endif()

install(TARGETS slpmu_jack
  EXPORT slpmu_jack_targets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES DESTINATION include)

install(DIRECTORY include/
  DESTINATION include)

# CMake package config (mirror slpmu_motor)
set(SLPMU_JACK_CMAKE_DIR share/slpmu_jack/cmake)
include(CMakePackageConfigHelpers)
configure_package_config_file(
  slpmu_jack-config.cmake.in
  ${PROJECT_BINARY_DIR}/cmake/slpmu_jack/slpmu_jack-config.cmake
  PATH_VARS SLPMU_JACK_CMAKE_DIR
  INSTALL_DESTINATION ${CMAKE_INSTALL_PREFIX}/share/slpmu_jack
)

install(
  EXPORT slpmu_jack_targets
  DESTINATION share/slpmu_jack/cmake/
  FILE slpmu_jack_targets.cmake
)

install(
  FILES ${PROJECT_BINARY_DIR}/cmake/slpmu_jack/slpmu_jack-config.cmake
  DESTINATION share/slpmu_jack/
)
