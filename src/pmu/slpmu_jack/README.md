
# slpmu_jack driver

## jack_utils examples:

```bash
jack_utils --driver can --if can0 --node 7 status
jack_utils --driver can --if can0 --node 7 --rate 2 status
jack_utils --driver can --if can0 --node 7 detect-base
jack_utils --driver can --if can0 --node 7 --pos 26000000 --rpm 1000 lift-up
jack_utils --driver can --if can0 --node 7 --rpm 800 lift-down
jack_utils --driver can --if can0 --node 7 brake
jack_utils --driver can --if can0 --node 7 clear-alarm
```
