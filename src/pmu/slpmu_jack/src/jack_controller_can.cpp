#include <slpmu/jack/jack_controller_can.hpp>
#include <slcan/slcan/canopen.hpp>
#include <cstring>
#include <chrono>

namespace slpmu {

namespace {
// Copy register addresses from jack_control_node.hpp
constexpr std::uint16_t kJackIndexCtrl = 0x6040;
constexpr std::uint8_t  kJackSubIndexCtrl = 0x00;
constexpr std::uint16_t kJackCtrlBrake = 0x0F;
constexpr std::uint16_t kJackCtrlDetectBase = 0x1F;
constexpr std::uint16_t kJackCtrlAutoPosChg = 0x103F;
constexpr std::uint16_t kJackCtrlAlarmCls = 0x86;

constexpr std::uint16_t kJackIndexMode = 0x6060;
constexpr std::uint8_t  kJackSubIndexMode = 0x00;
constexpr std::uint8_t  kJackModePos = 0x01;
constexpr std::uint8_t  kJackModeDetectBase = 0x06;

constexpr std::uint16_t kJackIndexTargetPos = 0x607A;
constexpr std::uint8_t  kJackSubIndexTargetPos = 0x00;
constexpr std::uint16_t kJackIndexTargetSpeed = 0x6081;
constexpr std::uint8_t  kJackSubIndexTargetSpeed = 0x00;
constexpr std::uint16_t kJackIndexActualPos = 0x6063;
constexpr std::uint8_t  kJackSubIndexActualPos = 0x00;

constexpr std::uint16_t kJackIndexStatus = 0x6041;
constexpr std::uint8_t  kJackSubIndexStatus = 0x00;
constexpr std::uint16_t kJackIndexAlarm = 0x2601;
constexpr std::uint8_t  kJackSubindexAlarm = 0x00;

// Speed conversion constants (avoid magic numbers)
constexpr std::uint64_t kSpeedNumerator = 512ull * 131072ull; // from device spec
constexpr std::uint64_t kSpeedDenominator = 1875ull;          // from device spec
}

JackControllerCan::JackControllerCan() : JackControllerCan(Config{}) {}

JackControllerCan::JackControllerCan(const Config& cfg)
    : cfg_(cfg)
    , canopen_(std::make_unique<slcan::CANopen>(slcan::CANopen::Config{cfg.can_interface, cfg.node_id, slcan::SocketCan::CanDeviceFlags::All}))
    , open_(false) {}

JackControllerCan::~JackControllerCan() { if (open_) close(); }

bool JackControllerCan::open() {
    if (open_) return true;
    if (canopen_->open()) { open_ = true; return true; }
    return false;
}

bool JackControllerCan::close() {
    if (!open_) return true;
    open_ = !canopen_->close();
    return !open_;
}

template<typename T>
std::optional<T> JackControllerCan::sdo_read(std::uint16_t index, std::uint8_t sub) {
    T value; std::memset(&value, 0, sizeof(T));
    auto r = canopen_->sdo_read(index, sub, &value, sizeof(T), cfg_.sdo_timeout_ns);
    if (!r.success) return std::nullopt;
    return value;
}

bool JackControllerCan::cmd_brake() {
    std::uint16_t val = kJackCtrlBrake;
    return canopen_->sdo_write(kJackIndexCtrl, kJackSubIndexCtrl, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::cmd_detect_base() {
    std::uint16_t val = kJackCtrlDetectBase;
    return canopen_->sdo_write(kJackIndexCtrl, kJackSubIndexCtrl, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::cmd_auto_pos_change() {
    std::uint16_t val = kJackCtrlAutoPosChg;
    return canopen_->sdo_write(kJackIndexCtrl, kJackSubIndexCtrl, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::cmd_clear_alarm() {
    std::uint16_t val = kJackCtrlAlarmCls;
    return canopen_->sdo_write(kJackIndexCtrl, kJackSubIndexCtrl, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::set_mode_detect_base() {
    std::uint8_t val = kJackModeDetectBase;
    return canopen_->sdo_write(kJackIndexMode, kJackSubIndexMode, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::set_mode_position() {
    std::uint8_t val = kJackModePos;
    return canopen_->sdo_write(kJackIndexMode, kJackSubIndexMode, &val, sizeof(val), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::write_target_speed(std::uint32_t can_speed_value) {
    return canopen_->sdo_write(kJackIndexTargetSpeed, kJackSubIndexTargetSpeed, &can_speed_value, sizeof(can_speed_value), cfg_.sdo_timeout_ns);
}

bool JackControllerCan::write_target_position(std::int32_t pos) {
    std::uint32_t u32 = static_cast<std::uint32_t>(pos);
    return canopen_->sdo_write(kJackIndexTargetPos, kJackSubIndexTargetPos, &u32, sizeof(u32), cfg_.sdo_timeout_ns);
}

IJackController::TimestampedValue<std::int32_t> JackControllerCan::read_actual_position() {
    IJackController::TimestampedValue<std::int32_t> out{false, 0, std::nullopt, std::nullopt};
    auto v = sdo_read<std::int32_t>(kJackIndexActualPos, kJackSubIndexActualPos);
    if (!v.has_value()) return out;
    out.success = true;
    out.value = *v;
    return out;
}

IJackController::TimestampedValue<std::uint16_t> JackControllerCan::read_status() {
    IJackController::TimestampedValue<std::uint16_t> out{false, 0, std::nullopt, std::nullopt};
    auto v = sdo_read<std::uint16_t>(kJackIndexStatus, kJackSubIndexStatus);
    if (!v.has_value()) return out;
    out.success = true;
    out.value = *v;
    return out;
}

IJackController::TimestampedValue<std::uint16_t> JackControllerCan::read_alarm() {
    IJackController::TimestampedValue<std::uint16_t> out{false, 0, std::nullopt, std::nullopt};
    auto v = sdo_read<std::uint16_t>(kJackIndexAlarm, kJackSubindexAlarm);
    if (!v.has_value()) return out;
    out.success = true;
    out.value = *v;
    return out;
}

std::uint32_t JackControllerCan::rpm_to_can_speed(std::uint32_t rpm) {
    return static_cast<std::uint32_t>((static_cast<std::uint64_t>(rpm) * kSpeedNumerator) / kSpeedDenominator);
}

// Explicit instantiations
template std::optional<std::int32_t> JackControllerCan::sdo_read<std::int32_t>(std::uint16_t, std::uint8_t);
template std::optional<std::uint16_t> JackControllerCan::sdo_read<std::uint16_t>(std::uint16_t, std::uint8_t);

}

