/**
 * imu_odom_fusion.cpp
 * IMU and odometry data fusion for chassis control
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright (c) 2025 SLAMTEC Co., Ltd.
 */

#include "slpmu/process/imu_odom_fusion.hpp"
#include <algorithm>
#include <cmath>
#include <cstdlib>

namespace slpmu::process {

namespace {
    double normalize_angle(double angle) {
        while (angle > M_PI) angle -= 2.0 * M_PI;
        while (angle < -M_PI) angle += 2.0 * M_PI;
        return angle;
    }
}

ImuOdomFusion::ImuOdomFusion(Config config)
    : config_(config) {
}

void ImuOdomFusion::reset() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    imu_yaw_buffer_.clear();
    has_last_odom_ = false;
    x_ = 0.0;
    y_ = 0.0;
    theta_ = 0.0;
    fusion_count_ = 0;
    fallback_count_ = 0;
}

void ImuOdomFusion::set_fusion_callback(fusion_callback_t callback) {
    fusion_callback_ = callback;
}

void ImuOdomFusion::set_wheel_parameters(double wheel_base, double wheel_diameter_left, double wheel_diameter_right) {
    wheel_base_ = wheel_base;
    wheel_diameter_left_ = wheel_diameter_left;
    wheel_diameter_right_ = wheel_diameter_right;
}

void ImuOdomFusion::set_encoder_resolution(std::int32_t counts_per_revolution) {
    encoder_resolution_ = counts_per_revolution;
}

void ImuOdomFusion::set_imu_time_offset(std::int64_t offset_ns) {
    config_.imu_time_offset_ns = offset_ns;
}

void ImuOdomFusion::add_imu_yaw_sample(double yaw, std::int64_t timestamp_ns) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    ImuYawSample sample;
    sample.timestamp_ns = timestamp_ns;
    sample.yaw = normalize_angle(yaw);
    
    imu_yaw_buffer_.push_back(sample);
    
    // Keep buffer size reasonable
    while (imu_yaw_buffer_.size() > config_.max_buffer_size) {
        imu_yaw_buffer_.pop_front();
    }
}

void ImuOdomFusion::process_odometry(const OdomData& odom) {
    if (!has_last_odom_) {
        last_odom_ = odom;
        has_last_odom_ = true;
        return;
    }
    
    compute_fused_odometry(odom);
    last_odom_ = odom;
}

std::int32_t ImuOdomFusion::calculate_encoder_diff(std::int32_t current, std::int32_t previous) {
    // According to the motor controller, the accumulated encoder behaviour on overflow is restart from zero
    // which means if the encoder goes beyond 2^31, it will restart from zero
    // so we need to handle the overflow case
    std::int32_t diff = current - previous;
    
    if (std::abs(diff) > 0x40000000) {
        // Over half of the encoder range, so we should think about the overflow
        if (diff > 0) {
            diff -= 0x80000000;
        } else {
            diff += 0x80000000;
        }
    }
    
    return diff;
}

void ImuOdomFusion::compute_fused_odometry(const OdomData& current_odom) {
    // Calculate encoder differences handling overflow
    std::int32_t left_encoder_diff = calculate_encoder_diff(current_odom.left_encoder, last_odom_.left_encoder);
    std::int32_t right_encoder_diff = calculate_encoder_diff(current_odom.right_encoder, last_odom_.right_encoder);
    
    // Convert encoder counts to radians
    double counts_to_radians = (2.0 * M_PI) / encoder_resolution_;
    double left_angle_diff = left_encoder_diff * counts_to_radians;
    double right_angle_diff = right_encoder_diff * counts_to_radians;
    
    // Calculate wheel radii from diameters
    double wheel_radius_left = wheel_diameter_left_ / 2.0;
    double wheel_radius_right = wheel_diameter_right_ / 2.0;
    
    // Calculate displacement for each wheel
    double left_displacement = left_angle_diff * wheel_radius_left;
    double right_displacement = right_angle_diff * wheel_radius_right;
    
    // Calculate linear displacement (average of both wheels)
    double linear_displacement = (left_displacement + right_displacement) / 2.0;
    
    // Apply time offset compensation to get corresponding IMU timestamps
    std::int64_t prev_odom_time_compensated = last_odom_.timestamp_ns - config_.imu_time_offset_ns;
    std::int64_t curr_odom_time_compensated = current_odom.timestamp_ns - config_.imu_time_offset_ns;
    
    double yaw0, yaw1;
    bool got_yaw0 = get_imu_yaw_at_time(prev_odom_time_compensated, yaw0);
    bool got_yaw1 = get_imu_yaw_at_time(curr_odom_time_compensated, yaw1);
    
    double d_theta;
    
    if (got_yaw0 && got_yaw1) {
        // Use IMU-based orientation
        d_theta = yaw1 - yaw0;
        
        // Normalize the difference
        d_theta = normalize_angle(d_theta);
        
        // Update theta from IMU
        theta_ = yaw1;
        fusion_count_++;
    } else {
        // Fallback to encoder-based orientation
        double angular_displacement = (right_displacement - left_displacement) / wheel_base_;
        d_theta = angular_displacement;
        theta_ += d_theta;
        theta_ = normalize_angle(theta_);
        fallback_count_++;
    }
    
    // Update position using the fused orientation
    double avg_theta = theta_ - d_theta / 2.0;
    x_ += linear_displacement * std::cos(avg_theta);
    y_ += linear_displacement * std::sin(avg_theta);
    
    // Calculate velocities
    double dt_s = static_cast<double>(current_odom.timestamp_ns - last_odom_.timestamp_ns) / 1e9;
    double linear_vel = 0.0;
    double angular_vel = 0.0;
    
    if (dt_s > 0.0) {
        linear_vel = linear_displacement / dt_s;
        angular_vel = d_theta / dt_s;
    }
    
    // Notify callback if set
    if (fusion_callback_) {
        FusedOdom fused_odom;
        fused_odom.x = x_;
        fused_odom.y = y_;
        fused_odom.theta = theta_;
        fused_odom.linear_vel = linear_vel;
        fused_odom.angular_vel = angular_vel;
        fused_odom.timestamp_ns = current_odom.timestamp_ns;
        
        fusion_callback_(fused_odom);
    }
}

bool ImuOdomFusion::get_imu_yaw_at_time(std::int64_t target_time_ns, double& yaw) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    if (imu_yaw_buffer_.empty()) {
        return false;
    }
    
    // If target time is before the first sample
    if (target_time_ns <= imu_yaw_buffer_.front().timestamp_ns) {
        // Check if it's within tolerance
        if ((imu_yaw_buffer_.front().timestamp_ns - target_time_ns) > config_.interpolation_tolerance_ns) {
            return false;
        }
        yaw = imu_yaw_buffer_.front().yaw;
        return true;
    }
    
    // If target time is after the last sample
    if (target_time_ns >= imu_yaw_buffer_.back().timestamp_ns) {
        // Check if it's within tolerance
        if ((target_time_ns - imu_yaw_buffer_.back().timestamp_ns) > config_.interpolation_tolerance_ns) {
            return false;
        }
        yaw = imu_yaw_buffer_.back().yaw;
        return true;
    }
    
    // Find the two samples to interpolate between
    for (size_t i = 0; i < imu_yaw_buffer_.size() - 1; i++) {
        if (target_time_ns >= imu_yaw_buffer_[i].timestamp_ns && 
            target_time_ns <= imu_yaw_buffer_[i + 1].timestamp_ns) {
            
            yaw = interpolate_yaw(imu_yaw_buffer_[i], imu_yaw_buffer_[i + 1], target_time_ns);
            return true;
        }
    }
    
    return false;
}

double ImuOdomFusion::interpolate_yaw(const ImuYawSample& sample1, const ImuYawSample& sample2,
                                       std::int64_t target_time_ns) {
    double dt_total = static_cast<double>(sample2.timestamp_ns - sample1.timestamp_ns);
    double dt_target = static_cast<double>(target_time_ns - sample1.timestamp_ns);
    
    if (dt_total <= 0.0) {
        return sample1.yaw;
    }
    
    double ratio = dt_target / dt_total;
    
    // Handle angle wrapping for interpolation
    double yaw0 = sample1.yaw;
    double yaw1 = sample2.yaw;
    double diff = yaw1 - yaw0;
    
    // Normalize the difference to [-pi, pi]
    diff = normalize_angle(diff);
    
    // Interpolate
    double interpolated_yaw = yaw0 + ratio * diff;
    return normalize_angle(interpolated_yaw);
}

size_t ImuOdomFusion::get_buffer_size() const {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    return imu_yaw_buffer_.size();
}

size_t ImuOdomFusion::get_fusion_count() const {
    return fusion_count_;
}

size_t ImuOdomFusion::get_fallback_count() const {
    return fallback_count_;
}

} // namespace slpmu::process