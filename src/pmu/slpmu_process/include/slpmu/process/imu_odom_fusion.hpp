/**
 * imu_odom_fusion.hpp
 * IMU and odometry data fusion for chassis control
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright (c) 2025 SLAMTEC Co., Ltd.
 */

#pragma once

#include <deque>
#include <functional>
#include <mutex>
#include <optional>
#include <cmath>
#include <cstdint>

namespace slpmu::process {

class ImuOdomFusion {
public:
    struct Config {
        std::int64_t imu_time_offset_ns;      // How much nanoseconds are IMU messages ahead of odometry
        size_t max_buffer_size;                // Maximum number of samples to keep in buffer
        std::int64_t interpolation_tolerance_ns;  // Maximum time tolerance for interpolation in nanoseconds
    };

    static constexpr std::int64_t kDefaultImuTimeOffsetNs = 6000000;  // 6ms in nanoseconds
    static constexpr size_t kDefaultMaxBufferSize = 200;  // Keep last 2 seconds at 100Hz
    static constexpr std::int64_t kDefaultInterpolationToleranceNs = 20000000;  // 20ms in nanoseconds
    
    static constexpr const Config kDefaultConfig = {
        kDefaultImuTimeOffsetNs,
        kDefaultMaxBufferSize,
        kDefaultInterpolationToleranceNs
    };

public:
    explicit ImuOdomFusion(Config config = kDefaultConfig);
    ImuOdomFusion(const ImuOdomFusion&) = delete;
    ImuOdomFusion& operator=(const ImuOdomFusion&) = delete;
    ImuOdomFusion(ImuOdomFusion&&) = delete;
    ImuOdomFusion& operator=(ImuOdomFusion&&) = delete;
    ~ImuOdomFusion() = default;

public:
    struct ImuYawSample {
        std::int64_t timestamp_ns;
        double yaw;  // in radians
    };

    struct OdomData {
        std::int64_t timestamp_ns;
        std::int32_t left_encoder;   // raw encoder counts
        std::int32_t right_encoder;  // raw encoder counts
    };

    struct FusedOdom {
        double x;           // position x in meters
        double y;           // position y in meters
        double theta;       // orientation in radians
        double linear_vel;  // linear velocity in m/s
        double angular_vel; // angular velocity in rad/s
        std::int64_t timestamp_ns;
    };

public:
    using fusion_callback_t = std::function<void(const FusedOdom& odom)>;

    void reset();
    void set_fusion_callback(fusion_callback_t callback);
    
    // Configuration
    void set_wheel_parameters(double wheel_base, double wheel_diameter_left, double wheel_diameter_right);
    void set_encoder_resolution(std::int32_t counts_per_revolution);
    void set_imu_time_offset(std::int64_t offset_ns);
    
    // Data input
    void add_imu_yaw_sample(double yaw, std::int64_t timestamp_ns);
    void process_odometry(const OdomData& odom);
    
    // Utility
    bool get_imu_yaw_at_time(std::int64_t target_time_ns, double& yaw);
    
    // Statistics
    size_t get_buffer_size() const;
    size_t get_fusion_count() const;
    size_t get_fallback_count() const;

private:
    void compute_fused_odometry(const OdomData& current_odom);
    double interpolate_yaw(const ImuYawSample& sample1, const ImuYawSample& sample2, 
                           std::int64_t target_time_ns);
    std::int32_t calculate_encoder_diff(std::int32_t current, std::int32_t previous);

private:
    Config config_;
    
    // Wheel parameters
    double wheel_base_ {0.0};
    double wheel_diameter_left_ {0.0};
    double wheel_diameter_right_ {0.0};
    std::int32_t encoder_resolution_ {0};  // counts per revolution
    
    // IMU buffer
    std::deque<ImuYawSample> imu_yaw_buffer_;
    mutable std::mutex buffer_mutex_;
    
    // Odometry state
    OdomData last_odom_;
    bool has_last_odom_ {false};
    
    // Fused state
    double x_ {0.0};
    double y_ {0.0};
    double theta_ {0.0};
    
    // Callback
    fusion_callback_t fusion_callback_ {nullptr};
    
    // Statistics
    size_t fusion_count_ {0};
    size_t fallback_count_ {0};  // When IMU data not available
};

} // namespace slpmu::process