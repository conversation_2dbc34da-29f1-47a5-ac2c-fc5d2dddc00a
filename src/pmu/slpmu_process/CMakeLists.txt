cmake_minimum_required(VERSION 3.8)
project(slpmu_process)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

FILE(GLOB SLPMU_PROCESS_SRC src/*.cpp)

add_library(slpmu_process STATIC ${SLPMU_PROCESS_SRC})
target_include_directories(slpmu_process PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)

install(TARGETS slpmu_process
  EXPORT slpmu_process_targets
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
  RUNTIME DESTINATION bin
  INCLUDES DESTINATION include)

install(DIRECTORY include/
  DESTINATION include)

set(SLPMU_PROCESS_CMAKE_DIR share/slpmu_process/cmake)
include(CMakePackageConfigHelpers)
configure_package_config_file(
  slpmu_process-config.cmake.in
  ${PROJECT_BINARY_DIR}/cmake/slpmu_process/slpmu_process-config.cmake
  PATH_VARS SLPMU_PROCESS_CMAKE_DIR
  INSTALL_DESTINATION ${CMAKE_INSTALL_PREFIX}/share/slpmu_process
)

install(
  EXPORT slpmu_process_targets
  DESTINATION share/slpmu_process/cmake/
  FILE slpmu_process_targets.cmake
)

install(
  FILES ${PROJECT_BINARY_DIR}/cmake/slpmu_process/slpmu_process-config.cmake
  DESTINATION share/slpmu_process/
)