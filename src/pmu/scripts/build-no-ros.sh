#! /bin/bash

set -e

root=$(pwd)
build_root=$root/build
install_root=$root/install

# This script builds the PMU driver without ROS.

components=(
  "slcan"
  "slpmu_motor"
  "slpmu_power"
  "slpmu_jack"
  "slpmu_process"
)

# clean previous build
rm -rf build install

# create build directory
mkdir -p build install

# build and install each component
for component in "${components[@]}"; do
  mkdir -p build/$component
  pushd build/$component
  cmake -DCMAKE_CXX_STANDARD=17 -DCMAKE_PREFIX_PATH=$install_root ../../$component
  make -j$(nproc)
  cmake --install . --prefix $install_root
  popd
done
