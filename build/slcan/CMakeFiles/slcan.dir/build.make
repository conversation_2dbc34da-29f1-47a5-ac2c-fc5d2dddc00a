# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slcan

# Include any dependencies generated for this target.
include CMakeFiles/slcan.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/slcan.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/slcan.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/slcan.dir/flags.make

CMakeFiles/slcan.dir/src/canopen.cpp.o: CMakeFiles/slcan.dir/flags.make
CMakeFiles/slcan.dir/src/canopen.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/canopen.cpp
CMakeFiles/slcan.dir/src/canopen.cpp.o: CMakeFiles/slcan.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slcan/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/slcan.dir/src/canopen.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/slcan.dir/src/canopen.cpp.o -MF CMakeFiles/slcan.dir/src/canopen.cpp.o.d -o CMakeFiles/slcan.dir/src/canopen.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/canopen.cpp

CMakeFiles/slcan.dir/src/canopen.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/slcan.dir/src/canopen.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/canopen.cpp > CMakeFiles/slcan.dir/src/canopen.cpp.i

CMakeFiles/slcan.dir/src/canopen.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/slcan.dir/src/canopen.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/canopen.cpp -o CMakeFiles/slcan.dir/src/canopen.cpp.s

CMakeFiles/slcan.dir/src/socket_can.cpp.o: CMakeFiles/slcan.dir/flags.make
CMakeFiles/slcan.dir/src/socket_can.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/socket_can.cpp
CMakeFiles/slcan.dir/src/socket_can.cpp.o: CMakeFiles/slcan.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slcan/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/slcan.dir/src/socket_can.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/slcan.dir/src/socket_can.cpp.o -MF CMakeFiles/slcan.dir/src/socket_can.cpp.o.d -o CMakeFiles/slcan.dir/src/socket_can.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/socket_can.cpp

CMakeFiles/slcan.dir/src/socket_can.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/slcan.dir/src/socket_can.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/socket_can.cpp > CMakeFiles/slcan.dir/src/socket_can.cpp.i

CMakeFiles/slcan.dir/src/socket_can.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/slcan.dir/src/socket_can.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/src/socket_can.cpp -o CMakeFiles/slcan.dir/src/socket_can.cpp.s

# Object files for target slcan
slcan_OBJECTS = \
"CMakeFiles/slcan.dir/src/canopen.cpp.o" \
"CMakeFiles/slcan.dir/src/socket_can.cpp.o"

# External object files for target slcan
slcan_EXTERNAL_OBJECTS =

libslcan.a: CMakeFiles/slcan.dir/src/canopen.cpp.o
libslcan.a: CMakeFiles/slcan.dir/src/socket_can.cpp.o
libslcan.a: CMakeFiles/slcan.dir/build.make
libslcan.a: CMakeFiles/slcan.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slcan/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libslcan.a"
	$(CMAKE_COMMAND) -P CMakeFiles/slcan.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/slcan.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/slcan.dir/build: libslcan.a
.PHONY : CMakeFiles/slcan.dir/build

CMakeFiles/slcan.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/slcan.dir/cmake_clean.cmake
.PHONY : CMakeFiles/slcan.dir/clean

CMakeFiles/slcan.dir/depend:
	cd /home/<USER>/works/source/amr_vcu_test_ws/build/slcan && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan /home/<USER>/works/source/amr_vcu_test_ws/build/slcan /home/<USER>/works/source/amr_vcu_test_ws/build/slcan /home/<USER>/works/source/amr_vcu_test_ws/build/slcan/CMakeFiles/slcan.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/slcan.dir/depend

