{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-c2d8606233d474d0ef12.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "slcan", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "slcan::@6890427a1f51a3e7e1df", "jsonFile": "target-slcan-RelWithDebInfo-c43188b54783370e206f.json", "name": "slcan", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/works/source/amr_vcu_test_ws/build/slcan", "source": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan"}, "version": {"major": 2, "minor": 6}}