{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 15, "parent": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libslcan.a"], "targetId": "slcan::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "share/slcan/cmake", "exportName": "slcan_targets", "exportTargets": [{"id": "slcan::@6890427a1f51a3e7e1df", "index": 0}], "paths": ["CMakeFiles/Export/3afcb8069ef6d874cd0703eaedd48bfb/slcan_targets.cmake"], "type": "export"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/slcan", "paths": ["/home/<USER>/works/source/amr_vcu_test_ws/build/slcan/cmake/slcan/slcan-config.cmake"], "type": "file"}], "paths": {"build": ".", "source": "."}}