{"archive": {}, "artifacts": [{"path": "libslcan.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 15, "parent": 0}, {"command": 2, "file": 0, "line": 5, "parent": 0}, {"command": 3, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wpedantic"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan/include"}], "language": "CXX", "sourceIndexes": [0, 1]}], "id": "slcan::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/slcan"}}, "name": "slcan", "nameOnDisk": "libslcan.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/canopen.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/socket_can.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}