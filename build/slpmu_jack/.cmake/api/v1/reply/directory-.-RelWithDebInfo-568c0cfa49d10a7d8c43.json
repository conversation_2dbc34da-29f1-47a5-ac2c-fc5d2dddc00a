{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 0, "file": 0, "line": 53, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["jack_utils"], "targetId": "jack_utils::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "lib", "paths": ["libslpmu_jack.a"], "targetId": "slpmu_jack::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 3, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/slpmu_jack/cmake", "exportName": "slpmu_jack_targets", "exportTargets": [{"id": "slpmu_jack::@6890427a1f51a3e7e1df", "index": 1}], "paths": ["CMakeFiles/Export/86d519e50d26eb5921d25071184b6eed/slpmu_jack_targets.cmake"], "type": "export"}, {"backtrace": 5, "component": "Unspecified", "destination": "share/slpmu_jack", "paths": ["/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/cmake/slpmu_jack/slpmu_jack-config.cmake"], "type": "file"}], "paths": {"build": ".", "source": "."}}