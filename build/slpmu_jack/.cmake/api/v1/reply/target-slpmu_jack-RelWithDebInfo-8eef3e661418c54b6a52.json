{"archive": {}, "artifacts": [{"path": "libslpmu_jack.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_options", "target_include_directories", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}, {"command": 1, "file": 0, "line": 27, "parent": 0}, {"command": 2, "file": 0, "line": 5, "parent": 0}, {"command": 3, "file": 0, "line": 13, "parent": 0}, {"command": 4, "file": 0, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wpedantic"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/include"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "slpmu_jack::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack"}}, "name": "slpmu_jack", "nameOnDisk": "libslpmu_jack.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/jack_controller_can.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}