{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-568c0cfa49d10a7d8c43.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "slpmu_jack", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "jack_utils::@6890427a1f51a3e7e1df", "jsonFile": "target-jack_utils-RelWithDebInfo-4e8eebcb329413b64dca.json", "name": "jack_utils", "projectIndex": 0}, {"directoryIndex": 0, "id": "slpmu_jack::@6890427a1f51a3e7e1df", "jsonFile": "target-slpmu_jack-RelWithDebInfo-8eef3e661418c54b6a52.json", "name": "slpmu_jack", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack", "source": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack"}, "version": {"major": 2, "minor": 6}}