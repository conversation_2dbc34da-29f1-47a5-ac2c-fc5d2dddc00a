# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/CMakeFiles /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named slpmu_jack

# Build rule for target.
slpmu_jack: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 slpmu_jack
.PHONY : slpmu_jack

# fast build rule for target.
slpmu_jack/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_jack.dir/build.make CMakeFiles/slpmu_jack.dir/build
.PHONY : slpmu_jack/fast

#=============================================================================
# Target rules for targets named jack_utils

# Build rule for target.
jack_utils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 jack_utils
.PHONY : jack_utils

# fast build rule for target.
jack_utils/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/jack_utils.dir/build.make CMakeFiles/jack_utils.dir/build
.PHONY : jack_utils/fast

bin/jack_utils.o: bin/jack_utils.cpp.o
.PHONY : bin/jack_utils.o

# target to build an object file
bin/jack_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/jack_utils.dir/build.make CMakeFiles/jack_utils.dir/bin/jack_utils.cpp.o
.PHONY : bin/jack_utils.cpp.o

bin/jack_utils.i: bin/jack_utils.cpp.i
.PHONY : bin/jack_utils.i

# target to preprocess a source file
bin/jack_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/jack_utils.dir/build.make CMakeFiles/jack_utils.dir/bin/jack_utils.cpp.i
.PHONY : bin/jack_utils.cpp.i

bin/jack_utils.s: bin/jack_utils.cpp.s
.PHONY : bin/jack_utils.s

# target to generate assembly for a file
bin/jack_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/jack_utils.dir/build.make CMakeFiles/jack_utils.dir/bin/jack_utils.cpp.s
.PHONY : bin/jack_utils.cpp.s

src/jack_controller_can.o: src/jack_controller_can.cpp.o
.PHONY : src/jack_controller_can.o

# target to build an object file
src/jack_controller_can.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_jack.dir/build.make CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o
.PHONY : src/jack_controller_can.cpp.o

src/jack_controller_can.i: src/jack_controller_can.cpp.i
.PHONY : src/jack_controller_can.i

# target to preprocess a source file
src/jack_controller_can.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_jack.dir/build.make CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.i
.PHONY : src/jack_controller_can.cpp.i

src/jack_controller_can.s: src/jack_controller_can.cpp.s
.PHONY : src/jack_controller_can.s

# target to generate assembly for a file
src/jack_controller_can.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_jack.dir/build.make CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.s
.PHONY : src/jack_controller_can.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... jack_utils"
	@echo "... slpmu_jack"
	@echo "... bin/jack_utils.o"
	@echo "... bin/jack_utils.i"
	@echo "... bin/jack_utils.s"
	@echo "... src/jack_controller_can.o"
	@echo "... src/jack_controller_can.i"
	@echo "... src/jack_controller_can.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

