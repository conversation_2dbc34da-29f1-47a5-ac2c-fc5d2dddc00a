# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack

# Include any dependencies generated for this target.
include CMakeFiles/slpmu_jack.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/slpmu_jack.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/slpmu_jack.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/slpmu_jack.dir/flags.make

CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o: CMakeFiles/slpmu_jack.dir/flags.make
CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp
CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o: CMakeFiles/slpmu_jack.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o -MF CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o.d -o CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp

CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp > CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.i

CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack/src/jack_controller_can.cpp -o CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.s

# Object files for target slpmu_jack
slpmu_jack_OBJECTS = \
"CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o"

# External object files for target slpmu_jack
slpmu_jack_EXTERNAL_OBJECTS =

libslpmu_jack.a: CMakeFiles/slpmu_jack.dir/src/jack_controller_can.cpp.o
libslpmu_jack.a: CMakeFiles/slpmu_jack.dir/build.make
libslpmu_jack.a: CMakeFiles/slpmu_jack.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libslpmu_jack.a"
	$(CMAKE_COMMAND) -P CMakeFiles/slpmu_jack.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/slpmu_jack.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/slpmu_jack.dir/build: libslpmu_jack.a
.PHONY : CMakeFiles/slpmu_jack.dir/build

CMakeFiles/slpmu_jack.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/slpmu_jack.dir/cmake_clean.cmake
.PHONY : CMakeFiles/slpmu_jack.dir/clean

CMakeFiles/slpmu_jack.dir/depend:
	cd /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/slpmu_jack.dir/depend

