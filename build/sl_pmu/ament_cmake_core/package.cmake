set(_AMENT_PACKAGE_NAME "sl_pmu")
set(sl_pmu_VERSION "1.0.0")
set(sl_pmu_MAINTAINER "Developer <<EMAIL>>")
set(sl_pmu_BUILD_DEPENDS "rosidl_default_generators" "rclcpp" "std_msgs" "geometry_msgs" "nav_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "sl_vcu_all" "libmodbus-dev")
set(sl_pmu_BUILDTOOL_DEPENDS "ament_cmake")
set(sl_pmu_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "geometry_msgs" "nav_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "sl_vcu_all" "libmodbus-dev")
set(sl_pmu_BUILDTOOL_EXPORT_DEPENDS )
set(sl_pmu_EXEC_DEPENDS "rosidl_default_runtime" "rclcpp" "std_msgs" "geometry_msgs" "nav_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "sl_vcu_all" "libmodbus-dev")
set(sl_pmu_TEST_DEPENDS "ament_lint_auto" "ament_lint_common" "ament_cmake_gtest")
set(sl_pmu_GROUP_DEPENDS )
set(sl_pmu_MEMBER_OF_GROUPS )
set(sl_pmu_DEPRECATED "")
set(sl_pmu_EXPORT_TAGS)
list(APPEND sl_pmu_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
