{"artifacts": [{"path": "pmu_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetools_exportExport.cmake", "/opt/ros/jazzy/share/tracetools/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetoolsConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclExport.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclConfig.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/jazzy/share/tf2_geometry_msgs/cmake/export_tf2_geometry_msgsExport.cmake", "/opt/ros/jazzy/share/tf2_geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tf2_geometry_msgs/cmake/tf2_geometry_msgsConfig.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/export_nav_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/nav_msgsConfig.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/export_nav_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/nav_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/nav_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/nav_msgs__rosidl_typesupport_cppExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake", "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/nav_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/jazzy/share/nav_msgs/cmake/export_nav_msgs__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 110, "parent": 0}, {"command": 1, "file": 0, "line": 127, "parent": 0}, {"command": 2, "file": 0, "line": 114, "parent": 0}, {"command": 2, "file": 0, "line": 101, "parent": 0}, {"command": 2, "file": 0, "line": 68, "parent": 0}, {"command": 3, "file": 0, "line": 113, "parent": 0}, {"command": 2, "file": 1, "line": 146, "parent": 6}, {"command": 6, "file": 0, "line": 21, "parent": 0}, {"file": 4, "parent": 8}, {"command": 5, "file": 4, "line": 41, "parent": 9}, {"file": 3, "parent": 10}, {"command": 5, "file": 3, "line": 9, "parent": 11}, {"file": 2, "parent": 12}, {"command": 4, "file": 2, "line": 61, "parent": 13}, {"command": 6, "file": 0, "line": 15, "parent": 0}, {"file": 7, "parent": 15}, {"command": 5, "file": 7, "line": 41, "parent": 16}, {"file": 6, "parent": 17}, {"command": 5, "file": 6, "line": 9, "parent": 18}, {"file": 5, "parent": 19}, {"command": 4, "file": 5, "line": 61, "parent": 20}, {"command": 5, "file": 4, "line": 41, "parent": 9}, {"file": 11, "parent": 22}, {"command": 6, "file": 11, "line": 21, "parent": 23}, {"file": 10, "parent": 24}, {"command": 5, "file": 10, "line": 41, "parent": 25}, {"file": 9, "parent": 26}, {"command": 5, "file": 9, "line": 9, "parent": 27}, {"file": 8, "parent": 28}, {"command": 4, "file": 8, "line": 61, "parent": 29}, {"command": 5, "file": 7, "line": 41, "parent": 16}, {"file": 15, "parent": 31}, {"command": 6, "file": 15, "line": 21, "parent": 32}, {"file": 14, "parent": 33}, {"command": 5, "file": 14, "line": 41, "parent": 34}, {"file": 13, "parent": 35}, {"command": 5, "file": 13, "line": 9, "parent": 36}, {"file": 12, "parent": 37}, {"command": 4, "file": 12, "line": 61, "parent": 38}, {"command": 6, "file": 15, "line": 21, "parent": 32}, {"file": 20, "parent": 40}, {"command": 5, "file": 20, "line": 41, "parent": 41}, {"file": 19, "parent": 42}, {"command": 6, "file": 19, "line": 21, "parent": 43}, {"file": 18, "parent": 44}, {"command": 5, "file": 18, "line": 41, "parent": 45}, {"file": 17, "parent": 46}, {"command": 5, "file": 17, "line": 9, "parent": 47}, {"file": 16, "parent": 48}, {"command": 4, "file": 16, "line": 61, "parent": 49}, {"command": 6, "file": 0, "line": 22, "parent": 0}, {"file": 23, "parent": 51}, {"command": 5, "file": 23, "line": 41, "parent": 52}, {"file": 22, "parent": 53}, {"command": 5, "file": 22, "line": 9, "parent": 54}, {"file": 21, "parent": 55}, {"command": 4, "file": 21, "line": 61, "parent": 56}, {"command": 6, "file": 0, "line": 23, "parent": 0}, {"file": 26, "parent": 58}, {"command": 5, "file": 26, "line": 41, "parent": 59}, {"file": 25, "parent": 60}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 24, "parent": 62}, {"command": 4, "file": 24, "line": 61, "parent": 63}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 27, "parent": 65}, {"command": 4, "file": 27, "line": 61, "parent": 66}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 28, "parent": 68}, {"command": 4, "file": 28, "line": 61, "parent": 69}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 29, "parent": 71}, {"command": 4, "file": 29, "line": 61, "parent": 72}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 30, "parent": 74}, {"command": 4, "file": 30, "line": 61, "parent": 75}, {"command": 6, "file": 0, "line": 18, "parent": 0}, {"file": 33, "parent": 77}, {"command": 5, "file": 33, "line": 41, "parent": 78}, {"file": 32, "parent": 79}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 31, "parent": 81}, {"command": 4, "file": 31, "line": 61, "parent": 82}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 34, "parent": 84}, {"command": 4, "file": 34, "line": 61, "parent": 85}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 35, "parent": 87}, {"command": 4, "file": 35, "line": 61, "parent": 88}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 36, "parent": 90}, {"command": 4, "file": 36, "line": 61, "parent": 91}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 37, "parent": 93}, {"command": 4, "file": 37, "line": 61, "parent": 94}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 38, "parent": 96}, {"command": 4, "file": 38, "line": 61, "parent": 97}, {"command": 5, "file": 25, "line": 9, "parent": 61}, {"file": 39, "parent": 99}, {"command": 4, "file": 39, "line": 61, "parent": 100}, {"command": 6, "file": 0, "line": 16, "parent": 0}, {"file": 42, "parent": 102}, {"command": 5, "file": 42, "line": 41, "parent": 103}, {"file": 41, "parent": 104}, {"command": 5, "file": 41, "line": 9, "parent": 105}, {"file": 40, "parent": 106}, {"command": 4, "file": 40, "line": 61, "parent": 107}, {"command": 5, "file": 41, "line": 9, "parent": 105}, {"file": 43, "parent": 109}, {"command": 4, "file": 43, "line": 61, "parent": 110}, {"command": 5, "file": 41, "line": 9, "parent": 105}, {"file": 44, "parent": 112}, {"command": 4, "file": 44, "line": 61, "parent": 113}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 45, "parent": 115}, {"command": 4, "file": 45, "line": 61, "parent": 116}, {"command": 5, "file": 32, "line": 9, "parent": 80}, {"file": 46, "parent": 118}, {"command": 4, "file": 46, "line": 61, "parent": 119}, {"command": 6, "file": 15, "line": 21, "parent": 32}, {"file": 51, "parent": 121}, {"command": 5, "file": 51, "line": 41, "parent": 122}, {"file": 50, "parent": 123}, {"command": 6, "file": 50, "line": 21, "parent": 124}, {"file": 49, "parent": 125}, {"command": 5, "file": 49, "line": 41, "parent": 126}, {"file": 48, "parent": 127}, {"command": 5, "file": 48, "line": 9, "parent": 128}, {"file": 47, "parent": 129}, {"command": 4, "file": 47, "line": 61, "parent": 130}, {"command": 6, "file": 50, "line": 21, "parent": 124}, {"file": 56, "parent": 132}, {"command": 5, "file": 56, "line": 41, "parent": 133}, {"file": 55, "parent": 134}, {"command": 6, "file": 55, "line": 21, "parent": 135}, {"file": 54, "parent": 136}, {"command": 5, "file": 54, "line": 41, "parent": 137}, {"file": 53, "parent": 138}, {"command": 5, "file": 53, "line": 9, "parent": 139}, {"file": 52, "parent": 140}, {"command": 4, "file": 52, "line": 61, "parent": 141}, {"command": 7, "file": 0, "line": 10, "parent": 0}, {"command": 8, "file": 0, "line": 30, "parent": 0}, {"command": 8, "file": 0, "line": 31, "parent": 0}, {"command": 9, "file": 1, "line": 142, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}, {"backtrace": 143, "fragment": "-Wall"}, {"backtrace": 143, "fragment": "-Wextra"}, {"backtrace": 143, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 7, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 7, "define": "FASTCDR_DYN_LINK"}], "includes": [{"backtrace": 144, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include"}, {"backtrace": 145, "path": "/usr/include/modbus"}, {"backtrace": 146, "isSystem": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/nav_msgs"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_ros"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/libstatistics_collector"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_logging_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_yaml_param_parser"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/type_description_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcpputils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/statistics_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosgraph_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/tracetools"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/message_filters"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_action"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/action_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/unique_identifier_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_action"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/eigen3"}], "language": "CXX", "languageStandard": {"backtraces": [7], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "sl_pmu_common::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_driver::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_protocol::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_device::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_application::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_ros_node::@6890427a1f51a3e7e1df"}], "id": "pmu_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/sl_pmu"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/ros/jazzy/lib:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:", "role": "libraries"}, {"backtrace": 3, "fragment": "libsl_pmu_ros_node.a", "role": "libraries"}, {"backtrace": 4, "fragment": "libsl_pmu_application.a", "role": "libraries"}, {"backtrace": 4, "fragment": "libsl_pmu_device.a", "role": "libraries"}, {"backtrace": 4, "fragment": "libsl_pmu_protocol.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-lmodbus", "role": "libraries"}, {"backtrace": 4, "fragment": "libsl_pmu_driver.a", "role": "libraries"}, {"backtrace": 4, "fragment": "libsl_pmu_common.a", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libtf2.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 39, "fragment": "-llttng-ust", "role": "libraries"}, {"backtrace": 39, "fragment": "-llttng-ust-common", "role": "libraries"}, {"backtrace": 39, "fragment": "-rdynamic", "role": "libraries"}, {"backtrace": 39, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 57, "fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 67, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 83, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 89, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 67, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 98, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 101, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 117, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 120, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 98, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 101, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 131, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 142, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "CXX"}, "name": "pmu_node", "nameOnDisk": "pmu_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/ros_node/pmu_main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}