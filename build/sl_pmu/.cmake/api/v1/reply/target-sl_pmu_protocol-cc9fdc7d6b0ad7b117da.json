{"archive": {}, "artifacts": [{"path": "libsl_pmu_protocol.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "add_compile_options", "ament_target_dependencies", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 62, "parent": 0}, {"command": 1, "file": 0, "line": 117, "parent": 0}, {"command": 2, "file": 0, "line": 68, "parent": 0}, {"command": 3, "file": 0, "line": 10, "parent": 0}, {"command": 4, "file": 0, "line": 67, "parent": 0}, {"command": 2, "file": 1, "line": 146, "parent": 5}, {"command": 5, "file": 0, "line": 30, "parent": 0}, {"command": 5, "file": 0, "line": 31, "parent": 0}, {"command": 6, "file": 1, "line": 142, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 6, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 6, "define": "FASTCDR_DYN_LINK"}], "includes": [{"backtrace": 7, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu/include"}, {"backtrace": 8, "path": "/usr/include/modbus"}, {"backtrace": 9, "isSystem": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/nav_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_ros"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_geometry_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/libstatistics_collector"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_logging_interface"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_yaml_param_parser"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/type_description_interfaces"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcpputils"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/statistics_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosgraph_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_cpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/tracetools"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/message_filters"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_action"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/action_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/unique_identifier_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_action"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_msgs"}, {"backtrace": 6, "isSystem": true, "path": "/usr/include/eigen3"}], "language": "CXX", "languageStandard": {"backtraces": [6], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"backtrace": 3, "id": "sl_pmu_common::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "sl_pmu_driver::@6890427a1f51a3e7e1df"}], "id": "sl_pmu_protocol::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/sl_pmu"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_pmu"}}, "name": "sl_pmu_protocol", "nameOnDisk": "libsl_pmu_protocol.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/protocol/canopen_sdo_protocol.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/protocol/modbus_rtu_protocol.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/protocol/communication_protocol_factory.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}