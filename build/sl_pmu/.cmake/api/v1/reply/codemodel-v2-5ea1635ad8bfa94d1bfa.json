{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-c9a8ded9de595fe4dcb9.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "sl_pmu", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "targets": [{"directoryIndex": 0, "id": "pmu_node::@6890427a1f51a3e7e1df", "jsonFile": "target-pmu_node-f74706052a786941cef5.json", "name": "pmu_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_application::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_application-bdc787e2feda3534dc58.json", "name": "sl_pmu_application", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_common::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_common-456314228fd304163ad2.json", "name": "sl_pmu_common", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_device::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_device-57c33c230b0432e0c828.json", "name": "sl_pmu_device", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_driver::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_driver-0023a1b7c19eb32677a6.json", "name": "sl_pmu_driver", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_protocol::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_protocol-cc9fdc7d6b0ad7b117da.json", "name": "sl_pmu_protocol", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_ros_node::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_ros_node-abbc7ec3ce579d3bb684.json", "name": "sl_pmu_ros_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_pmu_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_pmu_uninstall-63184c5127eebeedff7d.json", "name": "sl_pmu_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-fd2f966b18c919ef1da5.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_pmu", "source": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_pmu"}, "version": {"major": 2, "minor": 6}}