// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:action/JackControl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/action/jack_control.h"


#ifndef SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_H_
#define SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'command'
#include "rosidl_runtime_c/string.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_Goal
{
  /// Goal - The requested jack operation
  /// One of: "detect_base", "lift_up", "lift_down", "stop", "clear_alarm"
  rosidl_runtime_c__String command;
  /// Target position for lift operations (optional, used for lift_up/lift_down)
  uint32_t target_position;
  /// Speed for lift operations (optional, RPM)
  uint32_t speed;
} sl_vcu_all__action__JackControl_Goal;

// Struct for a sequence of sl_vcu_all__action__JackControl_Goal.
typedef struct sl_vcu_all__action__JackControl_Goal__Sequence
{
  sl_vcu_all__action__JackControl_Goal * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_Goal__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'message'
// already included above
// #include "rosidl_runtime_c/string.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_Result
{
  /// Result - The final result of the jack operation
  /// Whether the operation completed successfully
  bool success;
  /// Human-readable result message
  rosidl_runtime_c__String message;
  /// Final position after operation
  uint32_t final_position;
  /// Final status word from jack controller
  uint16_t final_status;
  /// Any alarm codes present
  uint16_t alarm_code;
} sl_vcu_all__action__JackControl_Result;

// Struct for a sequence of sl_vcu_all__action__JackControl_Result.
typedef struct sl_vcu_all__action__JackControl_Result__Sequence
{
  sl_vcu_all__action__JackControl_Result * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_Result__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'current_stage'
// already included above
// #include "rosidl_runtime_c/string.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_Feedback
{
  /// Feedback - Ongoing feedback during the operation
  /// Current stage: "init", "detecting_base", "base_stop", "lifting_up", "lifting_down", "top_stop", "middle_stop"
  rosidl_runtime_c__String current_stage;
  /// Current position
  uint32_t current_position;
  /// Current status word
  uint16_t current_status;
  /// Current alarm code
  uint16_t current_alarm;
  /// Progress percentage (0.0 to 100.0)
  float progress;
} sl_vcu_all__action__JackControl_Feedback;

// Struct for a sequence of sl_vcu_all__action__JackControl_Feedback.
typedef struct sl_vcu_all__action__JackControl_Feedback__Sequence
{
  sl_vcu_all__action__JackControl_Feedback * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_Feedback__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'goal_id'
#include "unique_identifier_msgs/msg/detail/uuid__struct.h"
// Member 'goal'
#include "sl_vcu_all/action/detail/jack_control__struct.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Request
{
  unique_identifier_msgs__msg__UUID goal_id;
  sl_vcu_all__action__JackControl_Goal goal;
} sl_vcu_all__action__JackControl_SendGoal_Request;

// Struct for a sequence of sl_vcu_all__action__JackControl_SendGoal_Request.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Request__Sequence
{
  sl_vcu_all__action__JackControl_SendGoal_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_SendGoal_Request__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'stamp'
#include "builtin_interfaces/msg/detail/time__struct.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Response
{
  bool accepted;
  builtin_interfaces__msg__Time stamp;
} sl_vcu_all__action__JackControl_SendGoal_Response;

// Struct for a sequence of sl_vcu_all__action__JackControl_SendGoal_Response.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Response__Sequence
{
  sl_vcu_all__action__JackControl_SendGoal_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_SendGoal_Response__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'info'
#include "service_msgs/msg/detail/service_event_info__struct.h"

// constants for array fields with an upper bound
// request
enum
{
  sl_vcu_all__action__JackControl_SendGoal_Event__request__MAX_SIZE = 1
};
// response
enum
{
  sl_vcu_all__action__JackControl_SendGoal_Event__response__MAX_SIZE = 1
};

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Event
{
  service_msgs__msg__ServiceEventInfo info;
  sl_vcu_all__action__JackControl_SendGoal_Request__Sequence request;
  sl_vcu_all__action__JackControl_SendGoal_Response__Sequence response;
} sl_vcu_all__action__JackControl_SendGoal_Event;

// Struct for a sequence of sl_vcu_all__action__JackControl_SendGoal_Event.
typedef struct sl_vcu_all__action__JackControl_SendGoal_Event__Sequence
{
  sl_vcu_all__action__JackControl_SendGoal_Event * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_SendGoal_Event__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__struct.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_GetResult_Request
{
  unique_identifier_msgs__msg__UUID goal_id;
} sl_vcu_all__action__JackControl_GetResult_Request;

// Struct for a sequence of sl_vcu_all__action__JackControl_GetResult_Request.
typedef struct sl_vcu_all__action__JackControl_GetResult_Request__Sequence
{
  sl_vcu_all__action__JackControl_GetResult_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_GetResult_Request__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'result'
// already included above
// #include "sl_vcu_all/action/detail/jack_control__struct.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_GetResult_Response
{
  int8_t status;
  sl_vcu_all__action__JackControl_Result result;
} sl_vcu_all__action__JackControl_GetResult_Response;

// Struct for a sequence of sl_vcu_all__action__JackControl_GetResult_Response.
typedef struct sl_vcu_all__action__JackControl_GetResult_Response__Sequence
{
  sl_vcu_all__action__JackControl_GetResult_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_GetResult_Response__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'info'
// already included above
// #include "service_msgs/msg/detail/service_event_info__struct.h"

// constants for array fields with an upper bound
// request
enum
{
  sl_vcu_all__action__JackControl_GetResult_Event__request__MAX_SIZE = 1
};
// response
enum
{
  sl_vcu_all__action__JackControl_GetResult_Event__response__MAX_SIZE = 1
};

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_GetResult_Event
{
  service_msgs__msg__ServiceEventInfo info;
  sl_vcu_all__action__JackControl_GetResult_Request__Sequence request;
  sl_vcu_all__action__JackControl_GetResult_Response__Sequence response;
} sl_vcu_all__action__JackControl_GetResult_Event;

// Struct for a sequence of sl_vcu_all__action__JackControl_GetResult_Event.
typedef struct sl_vcu_all__action__JackControl_GetResult_Event__Sequence
{
  sl_vcu_all__action__JackControl_GetResult_Event * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_GetResult_Event__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__struct.h"
// Member 'feedback'
// already included above
// #include "sl_vcu_all/action/detail/jack_control__struct.h"

/// Struct defined in action/JackControl in the package sl_vcu_all.
typedef struct sl_vcu_all__action__JackControl_FeedbackMessage
{
  unique_identifier_msgs__msg__UUID goal_id;
  sl_vcu_all__action__JackControl_Feedback feedback;
} sl_vcu_all__action__JackControl_FeedbackMessage;

// Struct for a sequence of sl_vcu_all__action__JackControl_FeedbackMessage.
typedef struct sl_vcu_all__action__JackControl_FeedbackMessage__Sequence
{
  sl_vcu_all__action__JackControl_FeedbackMessage * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__action__JackControl_FeedbackMessage__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_H_
