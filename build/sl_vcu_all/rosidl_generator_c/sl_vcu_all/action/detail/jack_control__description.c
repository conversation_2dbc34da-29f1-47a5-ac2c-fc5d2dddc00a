// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:action/JackControl.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/action/detail/jack_control__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl__get_type_hash(
  const rosidl_action_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xac, 0xe7, 0xb7, 0x43, 0xb6, 0x1d, 0x70, 0x80,
      0x5e, 0xd0, 0x06, 0xaa, 0xa9, 0x20, 0x40, 0xeb,
      0x55, 0x86, 0xf5, 0x69, 0x5b, 0xab, 0x74, 0x82,
      0x1f, 0x3e, 0xd4, 0x5c, 0x5b, 0xec, 0x99, 0x33,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_Goal__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xa3, 0x18, 0x27, 0x37, 0x0a, 0x9a, 0x16, 0xe7,
      0xfb, 0x7b, 0x52, 0x96, 0x8d, 0x74, 0x79, 0x01,
      0x9e, 0xfd, 0x85, 0x88, 0x97, 0xb9, 0x93, 0x6f,
      0x18, 0x76, 0x56, 0xb3, 0xb4, 0xb5, 0xbb, 0x74,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_Result__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x3e, 0xca, 0xb7, 0x3d, 0x46, 0xe9, 0x61, 0x43,
      0xf7, 0x4c, 0xf1, 0x83, 0xca, 0xf4, 0xde, 0x1f,
      0x44, 0xc9, 0x34, 0x9e, 0xe4, 0x5c, 0x7b, 0xb2,
      0x77, 0xd5, 0xf0, 0x4f, 0xe0, 0xa0, 0xa3, 0xdd,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_Feedback__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xde, 0x20, 0xc6, 0x4e, 0x8f, 0x2a, 0x2a, 0xba,
      0x94, 0xcb, 0x37, 0x09, 0xe2, 0xcb, 0xf5, 0xd4,
      0xb1, 0x73, 0xaf, 0xae, 0x55, 0x01, 0x31, 0x17,
      0xe7, 0x0c, 0x6e, 0xf8, 0xb1, 0x2f, 0xf9, 0x18,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_SendGoal__get_type_hash(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x10, 0xe9, 0xd2, 0x6c, 0x94, 0x89, 0x9c, 0x79,
      0x87, 0xba, 0x23, 0xbb, 0xc5, 0xd0, 0x27, 0xc2,
      0xc9, 0x40, 0x72, 0x82, 0xea, 0x8a, 0x05, 0x87,
      0x83, 0xe7, 0x32, 0x6b, 0x3d, 0x81, 0xa2, 0xb6,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_SendGoal_Request__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x85, 0x9c, 0x79, 0xfd, 0x78, 0xea, 0x4f, 0x78,
      0xcb, 0x75, 0x22, 0xa5, 0x0b, 0x9c, 0x7c, 0x65,
      0x6f, 0x7c, 0x59, 0xf0, 0x4d, 0x07, 0xd3, 0x3a,
      0x51, 0xc1, 0x5e, 0xd0, 0xd2, 0x38, 0x3e, 0x54,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_SendGoal_Response__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x65, 0xdf, 0x3f, 0x9f, 0xdc, 0x98, 0x36, 0x11,
      0x53, 0xb7, 0xd6, 0xcb, 0xe6, 0x9e, 0x6f, 0xaf,
      0x38, 0x55, 0x67, 0xff, 0xa6, 0x39, 0x72, 0xd2,
      0x45, 0x7f, 0x00, 0xaf, 0xaf, 0xd8, 0x97, 0x98,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_SendGoal_Event__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xb0, 0x69, 0xde, 0x5e, 0xa6, 0xbc, 0xad, 0xa7,
      0x0d, 0x62, 0xb7, 0x31, 0xb1, 0xff, 0x5c, 0xea,
      0x85, 0xd7, 0xbb, 0x1a, 0x3a, 0x90, 0x9d, 0x8d,
      0xdf, 0x74, 0x4c, 0xa0, 0x55, 0x92, 0x11, 0xfe,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_GetResult__get_type_hash(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x22, 0x8b, 0xc1, 0x69, 0x1c, 0xfe, 0xc5, 0x49,
      0x96, 0x29, 0xf0, 0xb5, 0x54, 0xbf, 0xfe, 0x89,
      0x20, 0xfb, 0x12, 0x50, 0x7f, 0x60, 0x1f, 0x38,
      0x7b, 0x86, 0xd1, 0xc5, 0xe2, 0xeb, 0x72, 0xe5,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_GetResult_Request__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x66, 0x4e, 0xff, 0x60, 0x3d, 0xc5, 0x2e, 0x29,
      0x95, 0x75, 0x06, 0xfc, 0xbd, 0xe1, 0x1f, 0xe4,
      0xbb, 0xb9, 0x6e, 0x4e, 0x44, 0xf2, 0xa3, 0x68,
      0x15, 0xe6, 0xf6, 0x41, 0xb8, 0xf1, 0x81, 0x4a,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_GetResult_Response__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xa1, 0x7a, 0x4c, 0x16, 0x6c, 0xf7, 0x39, 0x6b,
      0x4b, 0xf3, 0xc8, 0x15, 0x57, 0x96, 0x86, 0x89,
      0x02, 0xf6, 0xc9, 0x3f, 0x25, 0x1b, 0x21, 0x45,
      0x41, 0xff, 0xaa, 0x2e, 0x17, 0xc5, 0xe6, 0x67,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_GetResult_Event__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xb0, 0xc5, 0x88, 0x82, 0x0d, 0x28, 0x24, 0x01,
      0x66, 0xe5, 0xc1, 0xf5, 0xd3, 0x77, 0xac, 0x97,
      0x6e, 0xc5, 0x54, 0x64, 0xd9, 0x37, 0x1a, 0x95,
      0xd7, 0xf4, 0xa6, 0x07, 0x5c, 0x9a, 0x75, 0xcf,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__action__JackControl_FeedbackMessage__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xda, 0x71, 0x1e, 0xe6, 0xa2, 0x59, 0xef, 0x68,
      0xd0, 0xb5, 0xd8, 0x98, 0x09, 0x42, 0x57, 0x20,
      0x40, 0x74, 0x64, 0x0c, 0x8a, 0x04, 0x3c, 0x78,
      0xc8, 0x9e, 0xb3, 0xd3, 0xb8, 0xe2, 0xa7, 0xae,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "service_msgs/msg/detail/service_event_info__functions.h"
#include "builtin_interfaces/msg/detail/time__functions.h"
#include "unique_identifier_msgs/msg/detail/uuid__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t builtin_interfaces__msg__Time__EXPECTED_HASH = {1, {
    0xb1, 0x06, 0x23, 0x5e, 0x25, 0xa4, 0xc5, 0xed,
    0x35, 0x09, 0x8a, 0xa0, 0xa6, 0x1a, 0x3e, 0xe9,
    0xc9, 0xb1, 0x8d, 0x19, 0x7f, 0x39, 0x8b, 0x0e,
    0x42, 0x06, 0xce, 0xa9, 0xac, 0xf9, 0xc1, 0x97,
  }};
static const rosidl_type_hash_t service_msgs__msg__ServiceEventInfo__EXPECTED_HASH = {1, {
    0x41, 0xbc, 0xbb, 0xe0, 0x7a, 0x75, 0xc9, 0xb5,
    0x2b, 0xc9, 0x6b, 0xfd, 0x5c, 0x24, 0xd7, 0xf0,
    0xfc, 0x0a, 0x08, 0xc0, 0xcb, 0x79, 0x21, 0xb3,
    0x37, 0x3c, 0x57, 0x32, 0x34, 0x5a, 0x6f, 0x45,
  }};
static const rosidl_type_hash_t unique_identifier_msgs__msg__UUID__EXPECTED_HASH = {1, {
    0x1b, 0x8e, 0x8a, 0xca, 0x95, 0x8c, 0xbe, 0xa2,
    0x8f, 0xe6, 0xef, 0x60, 0xbf, 0x6c, 0x19, 0xb6,
    0x83, 0xc9, 0x7a, 0x9e, 0xf6, 0x0b, 0xb3, 0x47,
    0x52, 0x06, 0x7d, 0x0f, 0x2f, 0x7a, 0xb4, 0x37,
  }};
#endif

static char sl_vcu_all__action__JackControl__TYPE_NAME[] = "sl_vcu_all/action/JackControl";
static char builtin_interfaces__msg__Time__TYPE_NAME[] = "builtin_interfaces/msg/Time";
static char service_msgs__msg__ServiceEventInfo__TYPE_NAME[] = "service_msgs/msg/ServiceEventInfo";
static char sl_vcu_all__action__JackControl_Feedback__TYPE_NAME[] = "sl_vcu_all/action/JackControl_Feedback";
static char sl_vcu_all__action__JackControl_FeedbackMessage__TYPE_NAME[] = "sl_vcu_all/action/JackControl_FeedbackMessage";
static char sl_vcu_all__action__JackControl_GetResult__TYPE_NAME[] = "sl_vcu_all/action/JackControl_GetResult";
static char sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME[] = "sl_vcu_all/action/JackControl_GetResult_Event";
static char sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME[] = "sl_vcu_all/action/JackControl_GetResult_Request";
static char sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME[] = "sl_vcu_all/action/JackControl_GetResult_Response";
static char sl_vcu_all__action__JackControl_Goal__TYPE_NAME[] = "sl_vcu_all/action/JackControl_Goal";
static char sl_vcu_all__action__JackControl_Result__TYPE_NAME[] = "sl_vcu_all/action/JackControl_Result";
static char sl_vcu_all__action__JackControl_SendGoal__TYPE_NAME[] = "sl_vcu_all/action/JackControl_SendGoal";
static char sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME[] = "sl_vcu_all/action/JackControl_SendGoal_Event";
static char sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME[] = "sl_vcu_all/action/JackControl_SendGoal_Request";
static char sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME[] = "sl_vcu_all/action/JackControl_SendGoal_Response";
static char unique_identifier_msgs__msg__UUID__TYPE_NAME[] = "unique_identifier_msgs/msg/UUID";

// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl__FIELD_NAME__goal[] = "goal";
static char sl_vcu_all__action__JackControl__FIELD_NAME__result[] = "result";
static char sl_vcu_all__action__JackControl__FIELD_NAME__feedback[] = "feedback";
static char sl_vcu_all__action__JackControl__FIELD_NAME__send_goal_service[] = "send_goal_service";
static char sl_vcu_all__action__JackControl__FIELD_NAME__get_result_service[] = "get_result_service";
static char sl_vcu_all__action__JackControl__FIELD_NAME__feedback_message[] = "feedback_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__goal, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__result, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__feedback, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__send_goal_service, 17, 17},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_SendGoal__TYPE_NAME, 38, 38},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__get_result_service, 18, 18},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_GetResult__TYPE_NAME, 39, 39},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl__FIELD_NAME__feedback_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_FeedbackMessage__TYPE_NAME, 45, 45},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_FeedbackMessage__TYPE_NAME, 45, 45},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult__TYPE_NAME, 39, 39},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME, 45, 45},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal__TYPE_NAME, 38, 38},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME, 44, 44},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl__get_type_description(
  const rosidl_action_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl__TYPE_NAME, 29, 29},
      {sl_vcu_all__action__JackControl__FIELDS, 6, 6},
    },
    {sl_vcu_all__action__JackControl__REFERENCED_TYPE_DESCRIPTIONS, 15, 15},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__action__JackControl_Feedback__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__action__JackControl_FeedbackMessage__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__action__JackControl_GetResult__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[5].fields = sl_vcu_all__action__JackControl_GetResult_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[6].fields = sl_vcu_all__action__JackControl_GetResult_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[7].fields = sl_vcu_all__action__JackControl_GetResult_Response__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[8].fields = sl_vcu_all__action__JackControl_Goal__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[9].fields = sl_vcu_all__action__JackControl_Result__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[10].fields = sl_vcu_all__action__JackControl_SendGoal__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[11].fields = sl_vcu_all__action__JackControl_SendGoal_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[12].fields = sl_vcu_all__action__JackControl_SendGoal_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[13].fields = sl_vcu_all__action__JackControl_SendGoal_Response__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[14].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_Goal__FIELD_NAME__command[] = "command";
static char sl_vcu_all__action__JackControl_Goal__FIELD_NAME__target_position[] = "target_position";
static char sl_vcu_all__action__JackControl_Goal__FIELD_NAME__speed[] = "speed";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_Goal__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_Goal__FIELD_NAME__command, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Goal__FIELD_NAME__target_position, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Goal__FIELD_NAME__speed, 5, 5},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_Goal__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
      {sl_vcu_all__action__JackControl_Goal__FIELDS, 3, 3},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_Result__FIELD_NAME__success[] = "success";
static char sl_vcu_all__action__JackControl_Result__FIELD_NAME__message[] = "message";
static char sl_vcu_all__action__JackControl_Result__FIELD_NAME__final_position[] = "final_position";
static char sl_vcu_all__action__JackControl_Result__FIELD_NAME__final_status[] = "final_status";
static char sl_vcu_all__action__JackControl_Result__FIELD_NAME__alarm_code[] = "alarm_code";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_Result__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_Result__FIELD_NAME__success, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__FIELD_NAME__message, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__FIELD_NAME__final_position, 14, 14},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__FIELD_NAME__final_status, 12, 12},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT16,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__FIELD_NAME__alarm_code, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT16,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_Result__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
      {sl_vcu_all__action__JackControl_Result__FIELDS, 5, 5},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_stage[] = "current_stage";
static char sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_position[] = "current_position";
static char sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_status[] = "current_status";
static char sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_alarm[] = "current_alarm";
static char sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__progress[] = "progress";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_Feedback__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_stage, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_position, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_status, 14, 14},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT16,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__current_alarm, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT16,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Feedback__FIELD_NAME__progress, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_FLOAT,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_Feedback__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
      {sl_vcu_all__action__JackControl_Feedback__FIELDS, 5, 5},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__request_message[] = "request_message";
static char sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__response_message[] = "response_message";
static char sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__event_message[] = "event_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_SendGoal__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__request_message, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__response_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal__FIELD_NAME__event_message, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME, 44, 44},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_SendGoal__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME, 44, 44},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_SendGoal__get_type_description(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_SendGoal__TYPE_NAME, 38, 38},
      {sl_vcu_all__action__JackControl_SendGoal__FIELDS, 3, 3},
    },
    {sl_vcu_all__action__JackControl_SendGoal__REFERENCED_TYPE_DESCRIPTIONS, 7, 7},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__action__JackControl_Goal__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__action__JackControl_SendGoal_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__action__JackControl_SendGoal_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[5].fields = sl_vcu_all__action__JackControl_SendGoal_Response__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[6].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_SendGoal_Request__FIELD_NAME__goal_id[] = "goal_id";
static char sl_vcu_all__action__JackControl_SendGoal_Request__FIELD_NAME__goal[] = "goal";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_SendGoal_Request__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_SendGoal_Request__FIELD_NAME__goal_id, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Request__FIELD_NAME__goal, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_SendGoal_Request__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_SendGoal_Request__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
      {sl_vcu_all__action__JackControl_SendGoal_Request__FIELDS, 2, 2},
    },
    {sl_vcu_all__action__JackControl_SendGoal_Request__REFERENCED_TYPE_DESCRIPTIONS, 2, 2},
  };
  if (!constructed) {
    description.referenced_type_descriptions.data[0].fields = sl_vcu_all__action__JackControl_Goal__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_SendGoal_Response__FIELD_NAME__accepted[] = "accepted";
static char sl_vcu_all__action__JackControl_SendGoal_Response__FIELD_NAME__stamp[] = "stamp";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_SendGoal_Response__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_SendGoal_Response__FIELD_NAME__accepted, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Response__FIELD_NAME__stamp, 5, 5},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_SendGoal_Response__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_SendGoal_Response__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
      {sl_vcu_all__action__JackControl_SendGoal_Response__FIELDS, 2, 2},
    },
    {sl_vcu_all__action__JackControl_SendGoal_Response__REFERENCED_TYPE_DESCRIPTIONS, 1, 1},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__info[] = "info";
static char sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__request[] = "request";
static char sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__response[] = "response";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_SendGoal_Event__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__info, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__request, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Event__FIELD_NAME__response, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_SendGoal_Event__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_SendGoal_Event__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME, 44, 44},
      {sl_vcu_all__action__JackControl_SendGoal_Event__FIELDS, 3, 3},
    },
    {sl_vcu_all__action__JackControl_SendGoal_Event__REFERENCED_TYPE_DESCRIPTIONS, 6, 6},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__action__JackControl_Goal__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__action__JackControl_SendGoal_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__action__JackControl_SendGoal_Response__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[5].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__request_message[] = "request_message";
static char sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__response_message[] = "response_message";
static char sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__event_message[] = "event_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_GetResult__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__request_message, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__response_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult__FIELD_NAME__event_message, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME, 45, 45},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_GetResult__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME, 45, 45},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_GetResult__get_type_description(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_GetResult__TYPE_NAME, 39, 39},
      {sl_vcu_all__action__JackControl_GetResult__FIELDS, 3, 3},
    },
    {sl_vcu_all__action__JackControl_GetResult__REFERENCED_TYPE_DESCRIPTIONS, 7, 7},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__action__JackControl_GetResult_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__action__JackControl_GetResult_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__action__JackControl_GetResult_Response__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[5].fields = sl_vcu_all__action__JackControl_Result__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[6].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_GetResult_Request__FIELD_NAME__goal_id[] = "goal_id";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_GetResult_Request__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_GetResult_Request__FIELD_NAME__goal_id, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_GetResult_Request__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_GetResult_Request__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
      {sl_vcu_all__action__JackControl_GetResult_Request__FIELDS, 1, 1},
    },
    {sl_vcu_all__action__JackControl_GetResult_Request__REFERENCED_TYPE_DESCRIPTIONS, 1, 1},
  };
  if (!constructed) {
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_GetResult_Response__FIELD_NAME__status[] = "status";
static char sl_vcu_all__action__JackControl_GetResult_Response__FIELD_NAME__result[] = "result";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_GetResult_Response__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_GetResult_Response__FIELD_NAME__status, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_INT8,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Response__FIELD_NAME__result, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_GetResult_Response__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_GetResult_Response__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
      {sl_vcu_all__action__JackControl_GetResult_Response__FIELDS, 2, 2},
    },
    {sl_vcu_all__action__JackControl_GetResult_Response__REFERENCED_TYPE_DESCRIPTIONS, 1, 1},
  };
  if (!constructed) {
    description.referenced_type_descriptions.data[0].fields = sl_vcu_all__action__JackControl_Result__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__info[] = "info";
static char sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__request[] = "request";
static char sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__response[] = "response";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_GetResult_Event__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__info, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__request, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Event__FIELD_NAME__response, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_GetResult_Event__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_GetResult_Event__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME, 45, 45},
      {sl_vcu_all__action__JackControl_GetResult_Event__FIELDS, 3, 3},
    },
    {sl_vcu_all__action__JackControl_GetResult_Event__REFERENCED_TYPE_DESCRIPTIONS, 6, 6},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__action__JackControl_GetResult_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__action__JackControl_GetResult_Response__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__action__JackControl_Result__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[5].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__action__JackControl_FeedbackMessage__FIELD_NAME__goal_id[] = "goal_id";
static char sl_vcu_all__action__JackControl_FeedbackMessage__FIELD_NAME__feedback[] = "feedback";

static rosidl_runtime_c__type_description__Field sl_vcu_all__action__JackControl_FeedbackMessage__FIELDS[] = {
  {
    {sl_vcu_all__action__JackControl_FeedbackMessage__FIELD_NAME__goal_id, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__action__JackControl_FeedbackMessage__FIELD_NAME__feedback, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__action__JackControl_FeedbackMessage__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
    {NULL, 0, 0},
  },
  {
    {unique_identifier_msgs__msg__UUID__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__action__JackControl_FeedbackMessage__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__action__JackControl_FeedbackMessage__TYPE_NAME, 45, 45},
      {sl_vcu_all__action__JackControl_FeedbackMessage__FIELDS, 2, 2},
    },
    {sl_vcu_all__action__JackControl_FeedbackMessage__REFERENCED_TYPE_DESCRIPTIONS, 2, 2},
  };
  if (!constructed) {
    description.referenced_type_descriptions.data[0].fields = sl_vcu_all__action__JackControl_Feedback__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&unique_identifier_msgs__msg__UUID__EXPECTED_HASH, unique_identifier_msgs__msg__UUID__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = unique_identifier_msgs__msg__UUID__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# Jack Control Action Definition\n"
  "# This action provides control over the lifting jack system\n"
  "\n"
  "# Goal - The requested jack operation\n"
  "string command  # One of: \"detect_base\", \"lift_up\", \"lift_down\", \"stop\", \"clear_alarm\"\n"
  "uint32 target_position  # Target position for lift operations (optional, used for lift_up/lift_down)\n"
  "uint32 speed  # Speed for lift operations (optional, RPM)\n"
  "\n"
  "---\n"
  "\n"
  "# Result - The final result of the jack operation\n"
  "bool success  # Whether the operation completed successfully\n"
  "string message  # Human-readable result message\n"
  "uint32 final_position  # Final position after operation\n"
  "uint16 final_status  # Final status word from jack controller\n"
  "uint16 alarm_code  # Any alarm codes present\n"
  "\n"
  "---\n"
  "\n"
  "# Feedback - Ongoing feedback during the operation\n"
  "string current_stage  # Current stage: \"init\", \"detecting_base\", \"base_stop\", \"lifting_up\", \"lifting_down\", \"top_stop\", \"middle_stop\"\n"
  "uint32 current_position  # Current position\n"
  "uint16 current_status  # Current status word\n"
  "uint16 current_alarm  # Current alarm code\n"
  "float32 progress  # Progress percentage (0.0 to 100.0)";

static char action_encoding[] = "action";
static char implicit_encoding[] = "implicit";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl__get_individual_type_description_source(
  const rosidl_action_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl__TYPE_NAME, 29, 29},
    {action_encoding, 6, 6},
    {toplevel_type_raw_source, 1084, 1084},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_Goal__TYPE_NAME, 34, 34},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_Result__TYPE_NAME, 36, 36},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_Feedback__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_Feedback__TYPE_NAME, 38, 38},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_SendGoal__get_individual_type_description_source(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_SendGoal__TYPE_NAME, 38, 38},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_SendGoal_Request__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_SendGoal_Request__TYPE_NAME, 46, 46},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_SendGoal_Response__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_SendGoal_Response__TYPE_NAME, 47, 47},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_SendGoal_Event__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_SendGoal_Event__TYPE_NAME, 44, 44},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_GetResult__get_individual_type_description_source(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_GetResult__TYPE_NAME, 39, 39},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_GetResult_Request__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_GetResult_Request__TYPE_NAME, 47, 47},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_GetResult_Response__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_GetResult_Response__TYPE_NAME, 48, 48},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_GetResult_Event__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_GetResult_Event__TYPE_NAME, 45, 45},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__action__JackControl_FeedbackMessage__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__action__JackControl_FeedbackMessage__TYPE_NAME, 45, 45},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl__get_type_description_sources(
  const rosidl_action_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[16];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 16, 16};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__action__JackControl_Feedback__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__action__JackControl_FeedbackMessage__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__action__JackControl_GetResult__get_individual_type_description_source(NULL);
    sources[6] = *sl_vcu_all__action__JackControl_GetResult_Event__get_individual_type_description_source(NULL);
    sources[7] = *sl_vcu_all__action__JackControl_GetResult_Request__get_individual_type_description_source(NULL);
    sources[8] = *sl_vcu_all__action__JackControl_GetResult_Response__get_individual_type_description_source(NULL);
    sources[9] = *sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(NULL);
    sources[10] = *sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(NULL);
    sources[11] = *sl_vcu_all__action__JackControl_SendGoal__get_individual_type_description_source(NULL);
    sources[12] = *sl_vcu_all__action__JackControl_SendGoal_Event__get_individual_type_description_source(NULL);
    sources[13] = *sl_vcu_all__action__JackControl_SendGoal_Request__get_individual_type_description_source(NULL);
    sources[14] = *sl_vcu_all__action__JackControl_SendGoal_Response__get_individual_type_description_source(NULL);
    sources[15] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_Goal__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_Result__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_Feedback__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_Feedback__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_SendGoal__get_type_description_sources(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[8];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 8, 8};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_SendGoal__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__action__JackControl_SendGoal_Event__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__action__JackControl_SendGoal_Request__get_individual_type_description_source(NULL);
    sources[6] = *sl_vcu_all__action__JackControl_SendGoal_Response__get_individual_type_description_source(NULL);
    sources[7] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Request__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[3];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 3, 3};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_SendGoal_Request__get_individual_type_description_source(NULL),
    sources[1] = *sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(NULL);
    sources[2] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Response__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[2];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 2, 2};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_SendGoal_Response__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Event__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[7];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 7, 7};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_SendGoal_Event__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__action__JackControl_Goal__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__action__JackControl_SendGoal_Request__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__action__JackControl_SendGoal_Response__get_individual_type_description_source(NULL);
    sources[6] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_GetResult__get_type_description_sources(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[8];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 8, 8};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_GetResult__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__action__JackControl_GetResult_Event__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__action__JackControl_GetResult_Request__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__action__JackControl_GetResult_Response__get_individual_type_description_source(NULL);
    sources[6] = *sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(NULL);
    sources[7] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_GetResult_Request__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[2];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 2, 2};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_GetResult_Request__get_individual_type_description_source(NULL),
    sources[1] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_GetResult_Response__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[2];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 2, 2};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_GetResult_Response__get_individual_type_description_source(NULL),
    sources[1] = *sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_GetResult_Event__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[7];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 7, 7};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_GetResult_Event__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__action__JackControl_GetResult_Request__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__action__JackControl_GetResult_Response__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__action__JackControl_Result__get_individual_type_description_source(NULL);
    sources[6] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__action__JackControl_FeedbackMessage__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[3];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 3, 3};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__action__JackControl_FeedbackMessage__get_individual_type_description_source(NULL),
    sources[1] = *sl_vcu_all__action__JackControl_Feedback__get_individual_type_description_source(NULL);
    sources[2] = *unique_identifier_msgs__msg__UUID__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
