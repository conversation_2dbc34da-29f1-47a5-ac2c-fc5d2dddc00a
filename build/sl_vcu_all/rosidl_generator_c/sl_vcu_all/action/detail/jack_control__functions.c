// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:action/JackControl.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/action/detail/jack_control__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `command`
#include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__action__JackControl_Goal__init(sl_vcu_all__action__JackControl_Goal * msg)
{
  if (!msg) {
    return false;
  }
  // command
  if (!rosidl_runtime_c__String__init(&msg->command)) {
    sl_vcu_all__action__JackControl_Goal__fini(msg);
    return false;
  }
  // target_position
  // speed
  return true;
}

void
sl_vcu_all__action__JackControl_Goal__fini(sl_vcu_all__action__JackControl_Goal * msg)
{
  if (!msg) {
    return;
  }
  // command
  rosidl_runtime_c__String__fini(&msg->command);
  // target_position
  // speed
}

bool
sl_vcu_all__action__JackControl_Goal__are_equal(const sl_vcu_all__action__JackControl_Goal * lhs, const sl_vcu_all__action__JackControl_Goal * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // command
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->command), &(rhs->command)))
  {
    return false;
  }
  // target_position
  if (lhs->target_position != rhs->target_position) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Goal__copy(
  const sl_vcu_all__action__JackControl_Goal * input,
  sl_vcu_all__action__JackControl_Goal * output)
{
  if (!input || !output) {
    return false;
  }
  // command
  if (!rosidl_runtime_c__String__copy(
      &(input->command), &(output->command)))
  {
    return false;
  }
  // target_position
  output->target_position = input->target_position;
  // speed
  output->speed = input->speed;
  return true;
}

sl_vcu_all__action__JackControl_Goal *
sl_vcu_all__action__JackControl_Goal__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Goal * msg = (sl_vcu_all__action__JackControl_Goal *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Goal), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_Goal));
  bool success = sl_vcu_all__action__JackControl_Goal__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_Goal__destroy(sl_vcu_all__action__JackControl_Goal * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_Goal__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_Goal__Sequence__init(sl_vcu_all__action__JackControl_Goal__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Goal * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_Goal *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_Goal), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_Goal__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_Goal__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_Goal__Sequence__fini(sl_vcu_all__action__JackControl_Goal__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_Goal__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_Goal__Sequence *
sl_vcu_all__action__JackControl_Goal__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Goal__Sequence * array = (sl_vcu_all__action__JackControl_Goal__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Goal__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_Goal__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_Goal__Sequence__destroy(sl_vcu_all__action__JackControl_Goal__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_Goal__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_Goal__Sequence__are_equal(const sl_vcu_all__action__JackControl_Goal__Sequence * lhs, const sl_vcu_all__action__JackControl_Goal__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Goal__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Goal__Sequence__copy(
  const sl_vcu_all__action__JackControl_Goal__Sequence * input,
  sl_vcu_all__action__JackControl_Goal__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_Goal);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_Goal * data =
      (sl_vcu_all__action__JackControl_Goal *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_Goal__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_Goal__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Goal__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `message`
// already included above
// #include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__action__JackControl_Result__init(sl_vcu_all__action__JackControl_Result * msg)
{
  if (!msg) {
    return false;
  }
  // success
  // message
  if (!rosidl_runtime_c__String__init(&msg->message)) {
    sl_vcu_all__action__JackControl_Result__fini(msg);
    return false;
  }
  // final_position
  // final_status
  // alarm_code
  return true;
}

void
sl_vcu_all__action__JackControl_Result__fini(sl_vcu_all__action__JackControl_Result * msg)
{
  if (!msg) {
    return;
  }
  // success
  // message
  rosidl_runtime_c__String__fini(&msg->message);
  // final_position
  // final_status
  // alarm_code
}

bool
sl_vcu_all__action__JackControl_Result__are_equal(const sl_vcu_all__action__JackControl_Result * lhs, const sl_vcu_all__action__JackControl_Result * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // success
  if (lhs->success != rhs->success) {
    return false;
  }
  // message
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->message), &(rhs->message)))
  {
    return false;
  }
  // final_position
  if (lhs->final_position != rhs->final_position) {
    return false;
  }
  // final_status
  if (lhs->final_status != rhs->final_status) {
    return false;
  }
  // alarm_code
  if (lhs->alarm_code != rhs->alarm_code) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Result__copy(
  const sl_vcu_all__action__JackControl_Result * input,
  sl_vcu_all__action__JackControl_Result * output)
{
  if (!input || !output) {
    return false;
  }
  // success
  output->success = input->success;
  // message
  if (!rosidl_runtime_c__String__copy(
      &(input->message), &(output->message)))
  {
    return false;
  }
  // final_position
  output->final_position = input->final_position;
  // final_status
  output->final_status = input->final_status;
  // alarm_code
  output->alarm_code = input->alarm_code;
  return true;
}

sl_vcu_all__action__JackControl_Result *
sl_vcu_all__action__JackControl_Result__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Result * msg = (sl_vcu_all__action__JackControl_Result *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Result), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_Result));
  bool success = sl_vcu_all__action__JackControl_Result__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_Result__destroy(sl_vcu_all__action__JackControl_Result * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_Result__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_Result__Sequence__init(sl_vcu_all__action__JackControl_Result__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Result * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_Result *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_Result), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_Result__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_Result__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_Result__Sequence__fini(sl_vcu_all__action__JackControl_Result__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_Result__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_Result__Sequence *
sl_vcu_all__action__JackControl_Result__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Result__Sequence * array = (sl_vcu_all__action__JackControl_Result__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Result__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_Result__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_Result__Sequence__destroy(sl_vcu_all__action__JackControl_Result__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_Result__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_Result__Sequence__are_equal(const sl_vcu_all__action__JackControl_Result__Sequence * lhs, const sl_vcu_all__action__JackControl_Result__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Result__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Result__Sequence__copy(
  const sl_vcu_all__action__JackControl_Result__Sequence * input,
  sl_vcu_all__action__JackControl_Result__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_Result);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_Result * data =
      (sl_vcu_all__action__JackControl_Result *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_Result__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_Result__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Result__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `current_stage`
// already included above
// #include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__action__JackControl_Feedback__init(sl_vcu_all__action__JackControl_Feedback * msg)
{
  if (!msg) {
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__init(&msg->current_stage)) {
    sl_vcu_all__action__JackControl_Feedback__fini(msg);
    return false;
  }
  // current_position
  // current_status
  // current_alarm
  // progress
  return true;
}

void
sl_vcu_all__action__JackControl_Feedback__fini(sl_vcu_all__action__JackControl_Feedback * msg)
{
  if (!msg) {
    return;
  }
  // current_stage
  rosidl_runtime_c__String__fini(&msg->current_stage);
  // current_position
  // current_status
  // current_alarm
  // progress
}

bool
sl_vcu_all__action__JackControl_Feedback__are_equal(const sl_vcu_all__action__JackControl_Feedback * lhs, const sl_vcu_all__action__JackControl_Feedback * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->current_stage), &(rhs->current_stage)))
  {
    return false;
  }
  // current_position
  if (lhs->current_position != rhs->current_position) {
    return false;
  }
  // current_status
  if (lhs->current_status != rhs->current_status) {
    return false;
  }
  // current_alarm
  if (lhs->current_alarm != rhs->current_alarm) {
    return false;
  }
  // progress
  if (lhs->progress != rhs->progress) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Feedback__copy(
  const sl_vcu_all__action__JackControl_Feedback * input,
  sl_vcu_all__action__JackControl_Feedback * output)
{
  if (!input || !output) {
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__copy(
      &(input->current_stage), &(output->current_stage)))
  {
    return false;
  }
  // current_position
  output->current_position = input->current_position;
  // current_status
  output->current_status = input->current_status;
  // current_alarm
  output->current_alarm = input->current_alarm;
  // progress
  output->progress = input->progress;
  return true;
}

sl_vcu_all__action__JackControl_Feedback *
sl_vcu_all__action__JackControl_Feedback__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Feedback * msg = (sl_vcu_all__action__JackControl_Feedback *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Feedback), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_Feedback));
  bool success = sl_vcu_all__action__JackControl_Feedback__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_Feedback__destroy(sl_vcu_all__action__JackControl_Feedback * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_Feedback__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_Feedback__Sequence__init(sl_vcu_all__action__JackControl_Feedback__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Feedback * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_Feedback *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_Feedback), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_Feedback__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_Feedback__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_Feedback__Sequence__fini(sl_vcu_all__action__JackControl_Feedback__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_Feedback__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_Feedback__Sequence *
sl_vcu_all__action__JackControl_Feedback__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_Feedback__Sequence * array = (sl_vcu_all__action__JackControl_Feedback__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_Feedback__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_Feedback__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_Feedback__Sequence__destroy(sl_vcu_all__action__JackControl_Feedback__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_Feedback__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_Feedback__Sequence__are_equal(const sl_vcu_all__action__JackControl_Feedback__Sequence * lhs, const sl_vcu_all__action__JackControl_Feedback__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Feedback__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_Feedback__Sequence__copy(
  const sl_vcu_all__action__JackControl_Feedback__Sequence * input,
  sl_vcu_all__action__JackControl_Feedback__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_Feedback);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_Feedback * data =
      (sl_vcu_all__action__JackControl_Feedback *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_Feedback__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_Feedback__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_Feedback__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `goal_id`
#include "unique_identifier_msgs/msg/detail/uuid__functions.h"
// Member `goal`
// already included above
// #include "sl_vcu_all/action/detail/jack_control__functions.h"

bool
sl_vcu_all__action__JackControl_SendGoal_Request__init(sl_vcu_all__action__JackControl_SendGoal_Request * msg)
{
  if (!msg) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__init(&msg->goal_id)) {
    sl_vcu_all__action__JackControl_SendGoal_Request__fini(msg);
    return false;
  }
  // goal
  if (!sl_vcu_all__action__JackControl_Goal__init(&msg->goal)) {
    sl_vcu_all__action__JackControl_SendGoal_Request__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Request__fini(sl_vcu_all__action__JackControl_SendGoal_Request * msg)
{
  if (!msg) {
    return;
  }
  // goal_id
  unique_identifier_msgs__msg__UUID__fini(&msg->goal_id);
  // goal
  sl_vcu_all__action__JackControl_Goal__fini(&msg->goal);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Request__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Request * lhs, const sl_vcu_all__action__JackControl_SendGoal_Request * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__are_equal(
      &(lhs->goal_id), &(rhs->goal_id)))
  {
    return false;
  }
  // goal
  if (!sl_vcu_all__action__JackControl_Goal__are_equal(
      &(lhs->goal), &(rhs->goal)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Request__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Request * input,
  sl_vcu_all__action__JackControl_SendGoal_Request * output)
{
  if (!input || !output) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__copy(
      &(input->goal_id), &(output->goal_id)))
  {
    return false;
  }
  // goal
  if (!sl_vcu_all__action__JackControl_Goal__copy(
      &(input->goal), &(output->goal)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_SendGoal_Request *
sl_vcu_all__action__JackControl_SendGoal_Request__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Request * msg = (sl_vcu_all__action__JackControl_SendGoal_Request *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Request), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_SendGoal_Request));
  bool success = sl_vcu_all__action__JackControl_SendGoal_Request__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_SendGoal_Request__destroy(sl_vcu_all__action__JackControl_SendGoal_Request * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_SendGoal_Request__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__init(sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Request * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_SendGoal_Request *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_SendGoal_Request), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_SendGoal_Request__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_SendGoal_Request__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__fini(sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_SendGoal_Request__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_SendGoal_Request__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * array = (sl_vcu_all__action__JackControl_SendGoal_Request__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Request__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__destroy(sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * lhs, const sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Request__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * input,
  sl_vcu_all__action__JackControl_SendGoal_Request__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_SendGoal_Request);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_SendGoal_Request * data =
      (sl_vcu_all__action__JackControl_SendGoal_Request *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_SendGoal_Request__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_SendGoal_Request__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Request__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `stamp`
#include "builtin_interfaces/msg/detail/time__functions.h"

bool
sl_vcu_all__action__JackControl_SendGoal_Response__init(sl_vcu_all__action__JackControl_SendGoal_Response * msg)
{
  if (!msg) {
    return false;
  }
  // accepted
  // stamp
  if (!builtin_interfaces__msg__Time__init(&msg->stamp)) {
    sl_vcu_all__action__JackControl_SendGoal_Response__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Response__fini(sl_vcu_all__action__JackControl_SendGoal_Response * msg)
{
  if (!msg) {
    return;
  }
  // accepted
  // stamp
  builtin_interfaces__msg__Time__fini(&msg->stamp);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Response__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Response * lhs, const sl_vcu_all__action__JackControl_SendGoal_Response * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // accepted
  if (lhs->accepted != rhs->accepted) {
    return false;
  }
  // stamp
  if (!builtin_interfaces__msg__Time__are_equal(
      &(lhs->stamp), &(rhs->stamp)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Response__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Response * input,
  sl_vcu_all__action__JackControl_SendGoal_Response * output)
{
  if (!input || !output) {
    return false;
  }
  // accepted
  output->accepted = input->accepted;
  // stamp
  if (!builtin_interfaces__msg__Time__copy(
      &(input->stamp), &(output->stamp)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_SendGoal_Response *
sl_vcu_all__action__JackControl_SendGoal_Response__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Response * msg = (sl_vcu_all__action__JackControl_SendGoal_Response *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Response), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_SendGoal_Response));
  bool success = sl_vcu_all__action__JackControl_SendGoal_Response__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_SendGoal_Response__destroy(sl_vcu_all__action__JackControl_SendGoal_Response * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_SendGoal_Response__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__init(sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Response * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_SendGoal_Response *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_SendGoal_Response), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_SendGoal_Response__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_SendGoal_Response__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__fini(sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_SendGoal_Response__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_SendGoal_Response__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * array = (sl_vcu_all__action__JackControl_SendGoal_Response__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Response__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__destroy(sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * lhs, const sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Response__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * input,
  sl_vcu_all__action__JackControl_SendGoal_Response__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_SendGoal_Response);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_SendGoal_Response * data =
      (sl_vcu_all__action__JackControl_SendGoal_Response *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_SendGoal_Response__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_SendGoal_Response__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Response__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `info`
#include "service_msgs/msg/detail/service_event_info__functions.h"
// Member `request`
// Member `response`
// already included above
// #include "sl_vcu_all/action/detail/jack_control__functions.h"

bool
sl_vcu_all__action__JackControl_SendGoal_Event__init(sl_vcu_all__action__JackControl_SendGoal_Event * msg)
{
  if (!msg) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__init(&msg->info)) {
    sl_vcu_all__action__JackControl_SendGoal_Event__fini(msg);
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__init(&msg->request, 0)) {
    sl_vcu_all__action__JackControl_SendGoal_Event__fini(msg);
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__init(&msg->response, 0)) {
    sl_vcu_all__action__JackControl_SendGoal_Event__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Event__fini(sl_vcu_all__action__JackControl_SendGoal_Event * msg)
{
  if (!msg) {
    return;
  }
  // info
  service_msgs__msg__ServiceEventInfo__fini(&msg->info);
  // request
  sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__fini(&msg->request);
  // response
  sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__fini(&msg->response);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Event__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Event * lhs, const sl_vcu_all__action__JackControl_SendGoal_Event * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__are_equal(
      &(lhs->info), &(rhs->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__are_equal(
      &(lhs->request), &(rhs->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__are_equal(
      &(lhs->response), &(rhs->response)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Event__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Event * input,
  sl_vcu_all__action__JackControl_SendGoal_Event * output)
{
  if (!input || !output) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__copy(
      &(input->info), &(output->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_SendGoal_Request__Sequence__copy(
      &(input->request), &(output->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_SendGoal_Response__Sequence__copy(
      &(input->response), &(output->response)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_SendGoal_Event *
sl_vcu_all__action__JackControl_SendGoal_Event__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Event * msg = (sl_vcu_all__action__JackControl_SendGoal_Event *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Event), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_SendGoal_Event));
  bool success = sl_vcu_all__action__JackControl_SendGoal_Event__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_SendGoal_Event__destroy(sl_vcu_all__action__JackControl_SendGoal_Event * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_SendGoal_Event__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__init(sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Event * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_SendGoal_Event *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_SendGoal_Event), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_SendGoal_Event__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_SendGoal_Event__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__fini(sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_SendGoal_Event__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_SendGoal_Event__Sequence *
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * array = (sl_vcu_all__action__JackControl_SendGoal_Event__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_SendGoal_Event__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__destroy(sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__are_equal(const sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * lhs, const sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Event__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_SendGoal_Event__Sequence__copy(
  const sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * input,
  sl_vcu_all__action__JackControl_SendGoal_Event__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_SendGoal_Event);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_SendGoal_Event * data =
      (sl_vcu_all__action__JackControl_SendGoal_Event *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_SendGoal_Event__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_SendGoal_Event__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_SendGoal_Event__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `goal_id`
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__functions.h"

bool
sl_vcu_all__action__JackControl_GetResult_Request__init(sl_vcu_all__action__JackControl_GetResult_Request * msg)
{
  if (!msg) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__init(&msg->goal_id)) {
    sl_vcu_all__action__JackControl_GetResult_Request__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Request__fini(sl_vcu_all__action__JackControl_GetResult_Request * msg)
{
  if (!msg) {
    return;
  }
  // goal_id
  unique_identifier_msgs__msg__UUID__fini(&msg->goal_id);
}

bool
sl_vcu_all__action__JackControl_GetResult_Request__are_equal(const sl_vcu_all__action__JackControl_GetResult_Request * lhs, const sl_vcu_all__action__JackControl_GetResult_Request * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__are_equal(
      &(lhs->goal_id), &(rhs->goal_id)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Request__copy(
  const sl_vcu_all__action__JackControl_GetResult_Request * input,
  sl_vcu_all__action__JackControl_GetResult_Request * output)
{
  if (!input || !output) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__copy(
      &(input->goal_id), &(output->goal_id)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_GetResult_Request *
sl_vcu_all__action__JackControl_GetResult_Request__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Request * msg = (sl_vcu_all__action__JackControl_GetResult_Request *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Request), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_GetResult_Request));
  bool success = sl_vcu_all__action__JackControl_GetResult_Request__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_GetResult_Request__destroy(sl_vcu_all__action__JackControl_GetResult_Request * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_GetResult_Request__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__init(sl_vcu_all__action__JackControl_GetResult_Request__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Request * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_GetResult_Request *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_GetResult_Request), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_GetResult_Request__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_GetResult_Request__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__fini(sl_vcu_all__action__JackControl_GetResult_Request__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_GetResult_Request__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_GetResult_Request__Sequence *
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Request__Sequence * array = (sl_vcu_all__action__JackControl_GetResult_Request__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Request__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_GetResult_Request__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__destroy(sl_vcu_all__action__JackControl_GetResult_Request__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_GetResult_Request__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__are_equal(const sl_vcu_all__action__JackControl_GetResult_Request__Sequence * lhs, const sl_vcu_all__action__JackControl_GetResult_Request__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Request__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Request__Sequence__copy(
  const sl_vcu_all__action__JackControl_GetResult_Request__Sequence * input,
  sl_vcu_all__action__JackControl_GetResult_Request__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_GetResult_Request);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_GetResult_Request * data =
      (sl_vcu_all__action__JackControl_GetResult_Request *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_GetResult_Request__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_GetResult_Request__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Request__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `result`
// already included above
// #include "sl_vcu_all/action/detail/jack_control__functions.h"

bool
sl_vcu_all__action__JackControl_GetResult_Response__init(sl_vcu_all__action__JackControl_GetResult_Response * msg)
{
  if (!msg) {
    return false;
  }
  // status
  // result
  if (!sl_vcu_all__action__JackControl_Result__init(&msg->result)) {
    sl_vcu_all__action__JackControl_GetResult_Response__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Response__fini(sl_vcu_all__action__JackControl_GetResult_Response * msg)
{
  if (!msg) {
    return;
  }
  // status
  // result
  sl_vcu_all__action__JackControl_Result__fini(&msg->result);
}

bool
sl_vcu_all__action__JackControl_GetResult_Response__are_equal(const sl_vcu_all__action__JackControl_GetResult_Response * lhs, const sl_vcu_all__action__JackControl_GetResult_Response * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // status
  if (lhs->status != rhs->status) {
    return false;
  }
  // result
  if (!sl_vcu_all__action__JackControl_Result__are_equal(
      &(lhs->result), &(rhs->result)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Response__copy(
  const sl_vcu_all__action__JackControl_GetResult_Response * input,
  sl_vcu_all__action__JackControl_GetResult_Response * output)
{
  if (!input || !output) {
    return false;
  }
  // status
  output->status = input->status;
  // result
  if (!sl_vcu_all__action__JackControl_Result__copy(
      &(input->result), &(output->result)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_GetResult_Response *
sl_vcu_all__action__JackControl_GetResult_Response__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Response * msg = (sl_vcu_all__action__JackControl_GetResult_Response *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Response), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_GetResult_Response));
  bool success = sl_vcu_all__action__JackControl_GetResult_Response__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_GetResult_Response__destroy(sl_vcu_all__action__JackControl_GetResult_Response * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_GetResult_Response__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__init(sl_vcu_all__action__JackControl_GetResult_Response__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Response * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_GetResult_Response *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_GetResult_Response), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_GetResult_Response__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_GetResult_Response__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__fini(sl_vcu_all__action__JackControl_GetResult_Response__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_GetResult_Response__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_GetResult_Response__Sequence *
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Response__Sequence * array = (sl_vcu_all__action__JackControl_GetResult_Response__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Response__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_GetResult_Response__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__destroy(sl_vcu_all__action__JackControl_GetResult_Response__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_GetResult_Response__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__are_equal(const sl_vcu_all__action__JackControl_GetResult_Response__Sequence * lhs, const sl_vcu_all__action__JackControl_GetResult_Response__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Response__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Response__Sequence__copy(
  const sl_vcu_all__action__JackControl_GetResult_Response__Sequence * input,
  sl_vcu_all__action__JackControl_GetResult_Response__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_GetResult_Response);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_GetResult_Response * data =
      (sl_vcu_all__action__JackControl_GetResult_Response *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_GetResult_Response__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_GetResult_Response__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Response__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `info`
// already included above
// #include "service_msgs/msg/detail/service_event_info__functions.h"
// Member `request`
// Member `response`
// already included above
// #include "sl_vcu_all/action/detail/jack_control__functions.h"

bool
sl_vcu_all__action__JackControl_GetResult_Event__init(sl_vcu_all__action__JackControl_GetResult_Event * msg)
{
  if (!msg) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__init(&msg->info)) {
    sl_vcu_all__action__JackControl_GetResult_Event__fini(msg);
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_GetResult_Request__Sequence__init(&msg->request, 0)) {
    sl_vcu_all__action__JackControl_GetResult_Event__fini(msg);
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_GetResult_Response__Sequence__init(&msg->response, 0)) {
    sl_vcu_all__action__JackControl_GetResult_Event__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Event__fini(sl_vcu_all__action__JackControl_GetResult_Event * msg)
{
  if (!msg) {
    return;
  }
  // info
  service_msgs__msg__ServiceEventInfo__fini(&msg->info);
  // request
  sl_vcu_all__action__JackControl_GetResult_Request__Sequence__fini(&msg->request);
  // response
  sl_vcu_all__action__JackControl_GetResult_Response__Sequence__fini(&msg->response);
}

bool
sl_vcu_all__action__JackControl_GetResult_Event__are_equal(const sl_vcu_all__action__JackControl_GetResult_Event * lhs, const sl_vcu_all__action__JackControl_GetResult_Event * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__are_equal(
      &(lhs->info), &(rhs->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_GetResult_Request__Sequence__are_equal(
      &(lhs->request), &(rhs->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_GetResult_Response__Sequence__are_equal(
      &(lhs->response), &(rhs->response)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Event__copy(
  const sl_vcu_all__action__JackControl_GetResult_Event * input,
  sl_vcu_all__action__JackControl_GetResult_Event * output)
{
  if (!input || !output) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__copy(
      &(input->info), &(output->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__action__JackControl_GetResult_Request__Sequence__copy(
      &(input->request), &(output->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__action__JackControl_GetResult_Response__Sequence__copy(
      &(input->response), &(output->response)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_GetResult_Event *
sl_vcu_all__action__JackControl_GetResult_Event__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Event * msg = (sl_vcu_all__action__JackControl_GetResult_Event *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Event), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_GetResult_Event));
  bool success = sl_vcu_all__action__JackControl_GetResult_Event__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_GetResult_Event__destroy(sl_vcu_all__action__JackControl_GetResult_Event * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_GetResult_Event__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__init(sl_vcu_all__action__JackControl_GetResult_Event__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Event * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_GetResult_Event *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_GetResult_Event), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_GetResult_Event__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_GetResult_Event__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__fini(sl_vcu_all__action__JackControl_GetResult_Event__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_GetResult_Event__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_GetResult_Event__Sequence *
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_GetResult_Event__Sequence * array = (sl_vcu_all__action__JackControl_GetResult_Event__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_GetResult_Event__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_GetResult_Event__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__destroy(sl_vcu_all__action__JackControl_GetResult_Event__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_GetResult_Event__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__are_equal(const sl_vcu_all__action__JackControl_GetResult_Event__Sequence * lhs, const sl_vcu_all__action__JackControl_GetResult_Event__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Event__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_GetResult_Event__Sequence__copy(
  const sl_vcu_all__action__JackControl_GetResult_Event__Sequence * input,
  sl_vcu_all__action__JackControl_GetResult_Event__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_GetResult_Event);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_GetResult_Event * data =
      (sl_vcu_all__action__JackControl_GetResult_Event *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_GetResult_Event__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_GetResult_Event__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_GetResult_Event__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `goal_id`
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__functions.h"
// Member `feedback`
// already included above
// #include "sl_vcu_all/action/detail/jack_control__functions.h"

bool
sl_vcu_all__action__JackControl_FeedbackMessage__init(sl_vcu_all__action__JackControl_FeedbackMessage * msg)
{
  if (!msg) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__init(&msg->goal_id)) {
    sl_vcu_all__action__JackControl_FeedbackMessage__fini(msg);
    return false;
  }
  // feedback
  if (!sl_vcu_all__action__JackControl_Feedback__init(&msg->feedback)) {
    sl_vcu_all__action__JackControl_FeedbackMessage__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__action__JackControl_FeedbackMessage__fini(sl_vcu_all__action__JackControl_FeedbackMessage * msg)
{
  if (!msg) {
    return;
  }
  // goal_id
  unique_identifier_msgs__msg__UUID__fini(&msg->goal_id);
  // feedback
  sl_vcu_all__action__JackControl_Feedback__fini(&msg->feedback);
}

bool
sl_vcu_all__action__JackControl_FeedbackMessage__are_equal(const sl_vcu_all__action__JackControl_FeedbackMessage * lhs, const sl_vcu_all__action__JackControl_FeedbackMessage * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__are_equal(
      &(lhs->goal_id), &(rhs->goal_id)))
  {
    return false;
  }
  // feedback
  if (!sl_vcu_all__action__JackControl_Feedback__are_equal(
      &(lhs->feedback), &(rhs->feedback)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_FeedbackMessage__copy(
  const sl_vcu_all__action__JackControl_FeedbackMessage * input,
  sl_vcu_all__action__JackControl_FeedbackMessage * output)
{
  if (!input || !output) {
    return false;
  }
  // goal_id
  if (!unique_identifier_msgs__msg__UUID__copy(
      &(input->goal_id), &(output->goal_id)))
  {
    return false;
  }
  // feedback
  if (!sl_vcu_all__action__JackControl_Feedback__copy(
      &(input->feedback), &(output->feedback)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__action__JackControl_FeedbackMessage *
sl_vcu_all__action__JackControl_FeedbackMessage__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_FeedbackMessage * msg = (sl_vcu_all__action__JackControl_FeedbackMessage *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_FeedbackMessage), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__action__JackControl_FeedbackMessage));
  bool success = sl_vcu_all__action__JackControl_FeedbackMessage__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__action__JackControl_FeedbackMessage__destroy(sl_vcu_all__action__JackControl_FeedbackMessage * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__action__JackControl_FeedbackMessage__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__init(sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_FeedbackMessage * data = NULL;

  if (size) {
    data = (sl_vcu_all__action__JackControl_FeedbackMessage *)allocator.zero_allocate(size, sizeof(sl_vcu_all__action__JackControl_FeedbackMessage), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__action__JackControl_FeedbackMessage__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__action__JackControl_FeedbackMessage__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__fini(sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__action__JackControl_FeedbackMessage__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__action__JackControl_FeedbackMessage__Sequence *
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * array = (sl_vcu_all__action__JackControl_FeedbackMessage__Sequence *)allocator.allocate(sizeof(sl_vcu_all__action__JackControl_FeedbackMessage__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__destroy(sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__are_equal(const sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * lhs, const sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__action__JackControl_FeedbackMessage__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__action__JackControl_FeedbackMessage__Sequence__copy(
  const sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * input,
  sl_vcu_all__action__JackControl_FeedbackMessage__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__action__JackControl_FeedbackMessage);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__action__JackControl_FeedbackMessage * data =
      (sl_vcu_all__action__JackControl_FeedbackMessage *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__action__JackControl_FeedbackMessage__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__action__JackControl_FeedbackMessage__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__action__JackControl_FeedbackMessage__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
