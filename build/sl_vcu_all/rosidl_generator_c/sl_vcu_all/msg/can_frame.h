// generated from rosidl_generator_c/resource/idl.h.em
// with input from sl_vcu_all:msg/CanFrame.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__MSG__CAN_FRAME_H_
#define SL_VCU_ALL__MSG__CAN_FRAME_H_

#include "sl_vcu_all/msg/detail/can_frame__struct.h"
#include "sl_vcu_all/msg/detail/can_frame__functions.h"
#include "sl_vcu_all/msg/detail/can_frame__type_support.h"

#endif  // SL_VCU_ALL__MSG__CAN_FRAME_H_
