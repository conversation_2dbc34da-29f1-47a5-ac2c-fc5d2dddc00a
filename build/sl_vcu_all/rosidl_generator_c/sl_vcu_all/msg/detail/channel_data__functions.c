// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/channel_data__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `parts`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `parts_data`
#include "sl_vcu_all/msg/detail/part_data__functions.h"

bool
sl_vcu_all__msg__ChannelData__init(sl_vcu_all__msg__ChannelData * msg)
{
  if (!msg) {
    return false;
  }
  // parts
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->parts, 0)) {
    sl_vcu_all__msg__ChannelData__fini(msg);
    return false;
  }
  // sync_parts
  // parts_data
  if (!sl_vcu_all__msg__PartData__Sequence__init(&msg->parts_data, 0)) {
    sl_vcu_all__msg__ChannelData__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__msg__ChannelData__fini(sl_vcu_all__msg__ChannelData * msg)
{
  if (!msg) {
    return;
  }
  // parts
  rosidl_runtime_c__uint8__Sequence__fini(&msg->parts);
  // sync_parts
  // parts_data
  sl_vcu_all__msg__PartData__Sequence__fini(&msg->parts_data);
}

bool
sl_vcu_all__msg__ChannelData__are_equal(const sl_vcu_all__msg__ChannelData * lhs, const sl_vcu_all__msg__ChannelData * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // parts
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->parts), &(rhs->parts)))
  {
    return false;
  }
  // sync_parts
  if (lhs->sync_parts != rhs->sync_parts) {
    return false;
  }
  // parts_data
  if (!sl_vcu_all__msg__PartData__Sequence__are_equal(
      &(lhs->parts_data), &(rhs->parts_data)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__ChannelData__copy(
  const sl_vcu_all__msg__ChannelData * input,
  sl_vcu_all__msg__ChannelData * output)
{
  if (!input || !output) {
    return false;
  }
  // parts
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->parts), &(output->parts)))
  {
    return false;
  }
  // sync_parts
  output->sync_parts = input->sync_parts;
  // parts_data
  if (!sl_vcu_all__msg__PartData__Sequence__copy(
      &(input->parts_data), &(output->parts_data)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__msg__ChannelData *
sl_vcu_all__msg__ChannelData__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__ChannelData * msg = (sl_vcu_all__msg__ChannelData *)allocator.allocate(sizeof(sl_vcu_all__msg__ChannelData), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__ChannelData));
  bool success = sl_vcu_all__msg__ChannelData__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__ChannelData__destroy(sl_vcu_all__msg__ChannelData * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__ChannelData__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__ChannelData__Sequence__init(sl_vcu_all__msg__ChannelData__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__ChannelData * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__ChannelData *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__ChannelData), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__ChannelData__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__ChannelData__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__ChannelData__Sequence__fini(sl_vcu_all__msg__ChannelData__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__ChannelData__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__ChannelData__Sequence *
sl_vcu_all__msg__ChannelData__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__ChannelData__Sequence * array = (sl_vcu_all__msg__ChannelData__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__ChannelData__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__ChannelData__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__ChannelData__Sequence__destroy(sl_vcu_all__msg__ChannelData__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__ChannelData__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__ChannelData__Sequence__are_equal(const sl_vcu_all__msg__ChannelData__Sequence * lhs, const sl_vcu_all__msg__ChannelData__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__ChannelData__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__ChannelData__Sequence__copy(
  const sl_vcu_all__msg__ChannelData__Sequence * input,
  sl_vcu_all__msg__ChannelData__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__ChannelData);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__ChannelData * data =
      (sl_vcu_all__msg__ChannelData *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__ChannelData__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__ChannelData__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__ChannelData__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
