// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/msg/detail/part_data__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__msg__PartData__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x44, 0xe0, 0x9f, 0x9f, 0x38, 0x47, 0x2e, 0x21,
      0x2f, 0xec, 0x22, 0xe7, 0x18, 0x2c, 0x21, 0x2d,
      0x97, 0xba, 0x1b, 0x65, 0x01, 0xc5, 0x0e, 0x89,
      0xfb, 0x31, 0x19, 0x9e, 0x90, 0x8f, 0x32, 0xc3,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types

// Hashes for external referenced types
#ifndef NDEBUG
#endif

static char sl_vcu_all__msg__PartData__TYPE_NAME[] = "sl_vcu_all/msg/PartData";

// Define type names, field names, and default values
static char sl_vcu_all__msg__PartData__FIELD_NAME__mode[] = "mode";
static char sl_vcu_all__msg__PartData__FIELD_NAME__green_brightness[] = "green_brightness";
static char sl_vcu_all__msg__PartData__FIELD_NAME__red_brightness[] = "red_brightness";
static char sl_vcu_all__msg__PartData__FIELD_NAME__blue_brightness[] = "blue_brightness";
static char sl_vcu_all__msg__PartData__FIELD_NAME__frequency[] = "frequency";
static char sl_vcu_all__msg__PartData__FIELD_NAME__speed[] = "speed";
static char sl_vcu_all__msg__PartData__FIELD_NAME__marquee_direction[] = "marquee_direction";
static char sl_vcu_all__msg__PartData__FIELD_NAME__on_time_duty[] = "on_time_duty";

static rosidl_runtime_c__type_description__Field sl_vcu_all__msg__PartData__FIELDS[] = {
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__mode, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__green_brightness, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__red_brightness, 14, 14},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__blue_brightness, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__frequency, 9, 9},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_FLOAT,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__speed, 5, 5},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_FLOAT,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__marquee_direction, 17, 17},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__FIELD_NAME__on_time_duty, 12, 12},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_FLOAT,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__msg__PartData__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
      {sl_vcu_all__msg__PartData__FIELDS, 8, 8},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# Part Data for LED Control\n"
  "# This message defines the control data for a single part of an LED strip\n"
  "\n"
  "# Lighting mode (required)\n"
  "string mode                    # \"off\", \"on\", \"breathing\", \"flashing\", \"marquee\"\n"
  "\n"
  "# Color brightness settings (optional, defaults applied if not set)\n"
  "uint8 green_brightness         # Green brightness (0-255)\n"
  "uint8 red_brightness           # Red brightness (0-255)\n"
  "uint8 blue_brightness          # Blue brightness (0-255)\n"
  "\n"
  "# Effect parameters (optional, defaults applied if not set)\n"
  "float32 frequency              # Frequency for breathing/flashing effects (Hz)\n"
  "float32 speed                  # Speed for marquee effect (pixels/second)\n"
  "bool marquee_direction         # Direction for marquee: true=forward, false=reverse\n"
  "float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)";

static char msg_encoding[] = "msg";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__msg__PartData__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    {msg_encoding, 3, 3},
    {toplevel_type_raw_source, 836, 836},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__msg__PartData__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__msg__PartData__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}
