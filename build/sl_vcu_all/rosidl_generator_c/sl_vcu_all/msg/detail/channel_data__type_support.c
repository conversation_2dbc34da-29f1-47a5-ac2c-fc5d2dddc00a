// generated from rosidl_generator_c/resource/idl__type_support.c.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

#include <string.h>

#include "sl_vcu_all/msg/detail/channel_data__functions.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/detail/channel_data__type_support.h"
#include "sl_vcu_all/msg/detail/channel_data__struct.h"

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef __cplusplus
}
#endif
