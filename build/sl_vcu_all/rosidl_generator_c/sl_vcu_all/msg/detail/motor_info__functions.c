// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/motor_info__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"

bool
sl_vcu_all__msg__MotorInfo__init(sl_vcu_all__msg__MotorInfo * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    sl_vcu_all__msg__MotorInfo__fini(msg);
    return false;
  }
  // left_current
  // right_current
  // left_temp
  // right_temp
  // driver_temp
  // left_pos_encoder
  // right_pos_encoder
  // alarm_code
  return true;
}

void
sl_vcu_all__msg__MotorInfo__fini(sl_vcu_all__msg__MotorInfo * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // left_current
  // right_current
  // left_temp
  // right_temp
  // driver_temp
  // left_pos_encoder
  // right_pos_encoder
  // alarm_code
}

bool
sl_vcu_all__msg__MotorInfo__are_equal(const sl_vcu_all__msg__MotorInfo * lhs, const sl_vcu_all__msg__MotorInfo * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // left_current
  if (lhs->left_current != rhs->left_current) {
    return false;
  }
  // right_current
  if (lhs->right_current != rhs->right_current) {
    return false;
  }
  // left_temp
  if (lhs->left_temp != rhs->left_temp) {
    return false;
  }
  // right_temp
  if (lhs->right_temp != rhs->right_temp) {
    return false;
  }
  // driver_temp
  if (lhs->driver_temp != rhs->driver_temp) {
    return false;
  }
  // left_pos_encoder
  if (lhs->left_pos_encoder != rhs->left_pos_encoder) {
    return false;
  }
  // right_pos_encoder
  if (lhs->right_pos_encoder != rhs->right_pos_encoder) {
    return false;
  }
  // alarm_code
  if (lhs->alarm_code != rhs->alarm_code) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__MotorInfo__copy(
  const sl_vcu_all__msg__MotorInfo * input,
  sl_vcu_all__msg__MotorInfo * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // left_current
  output->left_current = input->left_current;
  // right_current
  output->right_current = input->right_current;
  // left_temp
  output->left_temp = input->left_temp;
  // right_temp
  output->right_temp = input->right_temp;
  // driver_temp
  output->driver_temp = input->driver_temp;
  // left_pos_encoder
  output->left_pos_encoder = input->left_pos_encoder;
  // right_pos_encoder
  output->right_pos_encoder = input->right_pos_encoder;
  // alarm_code
  output->alarm_code = input->alarm_code;
  return true;
}

sl_vcu_all__msg__MotorInfo *
sl_vcu_all__msg__MotorInfo__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorInfo * msg = (sl_vcu_all__msg__MotorInfo *)allocator.allocate(sizeof(sl_vcu_all__msg__MotorInfo), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__MotorInfo));
  bool success = sl_vcu_all__msg__MotorInfo__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__MotorInfo__destroy(sl_vcu_all__msg__MotorInfo * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__MotorInfo__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__MotorInfo__Sequence__init(sl_vcu_all__msg__MotorInfo__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorInfo * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__MotorInfo *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__MotorInfo), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__MotorInfo__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__MotorInfo__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__MotorInfo__Sequence__fini(sl_vcu_all__msg__MotorInfo__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__MotorInfo__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__MotorInfo__Sequence *
sl_vcu_all__msg__MotorInfo__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorInfo__Sequence * array = (sl_vcu_all__msg__MotorInfo__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__MotorInfo__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__MotorInfo__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__MotorInfo__Sequence__destroy(sl_vcu_all__msg__MotorInfo__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__MotorInfo__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__MotorInfo__Sequence__are_equal(const sl_vcu_all__msg__MotorInfo__Sequence * lhs, const sl_vcu_all__msg__MotorInfo__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__MotorInfo__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__MotorInfo__Sequence__copy(
  const sl_vcu_all__msg__MotorInfo__Sequence * input,
  sl_vcu_all__msg__MotorInfo__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__MotorInfo);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__MotorInfo * data =
      (sl_vcu_all__msg__MotorInfo *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__MotorInfo__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__MotorInfo__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__MotorInfo__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
