// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/MotorState.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/motor_state__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"
// Member `state`
// Member `error_info`
#include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__msg__MotorState__init(sl_vcu_all__msg__MotorState * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    sl_vcu_all__msg__MotorState__fini(msg);
    return false;
  }
  // brake_release
  // emergency_stop
  // state
  if (!rosidl_runtime_c__String__init(&msg->state)) {
    sl_vcu_all__msg__MotorState__fini(msg);
    return false;
  }
  // error_code
  // error_info
  if (!rosidl_runtime_c__String__init(&msg->error_info)) {
    sl_vcu_all__msg__MotorState__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__msg__MotorState__fini(sl_vcu_all__msg__MotorState * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // brake_release
  // emergency_stop
  // state
  rosidl_runtime_c__String__fini(&msg->state);
  // error_code
  // error_info
  rosidl_runtime_c__String__fini(&msg->error_info);
}

bool
sl_vcu_all__msg__MotorState__are_equal(const sl_vcu_all__msg__MotorState * lhs, const sl_vcu_all__msg__MotorState * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // brake_release
  if (lhs->brake_release != rhs->brake_release) {
    return false;
  }
  // emergency_stop
  if (lhs->emergency_stop != rhs->emergency_stop) {
    return false;
  }
  // state
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->state), &(rhs->state)))
  {
    return false;
  }
  // error_code
  if (lhs->error_code != rhs->error_code) {
    return false;
  }
  // error_info
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->error_info), &(rhs->error_info)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__MotorState__copy(
  const sl_vcu_all__msg__MotorState * input,
  sl_vcu_all__msg__MotorState * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // brake_release
  output->brake_release = input->brake_release;
  // emergency_stop
  output->emergency_stop = input->emergency_stop;
  // state
  if (!rosidl_runtime_c__String__copy(
      &(input->state), &(output->state)))
  {
    return false;
  }
  // error_code
  output->error_code = input->error_code;
  // error_info
  if (!rosidl_runtime_c__String__copy(
      &(input->error_info), &(output->error_info)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__msg__MotorState *
sl_vcu_all__msg__MotorState__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorState * msg = (sl_vcu_all__msg__MotorState *)allocator.allocate(sizeof(sl_vcu_all__msg__MotorState), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__MotorState));
  bool success = sl_vcu_all__msg__MotorState__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__MotorState__destroy(sl_vcu_all__msg__MotorState * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__MotorState__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__MotorState__Sequence__init(sl_vcu_all__msg__MotorState__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorState * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__MotorState *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__MotorState), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__MotorState__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__MotorState__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__MotorState__Sequence__fini(sl_vcu_all__msg__MotorState__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__MotorState__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__MotorState__Sequence *
sl_vcu_all__msg__MotorState__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__MotorState__Sequence * array = (sl_vcu_all__msg__MotorState__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__MotorState__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__MotorState__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__MotorState__Sequence__destroy(sl_vcu_all__msg__MotorState__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__MotorState__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__MotorState__Sequence__are_equal(const sl_vcu_all__msg__MotorState__Sequence * lhs, const sl_vcu_all__msg__MotorState__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__MotorState__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__MotorState__Sequence__copy(
  const sl_vcu_all__msg__MotorState__Sequence * input,
  sl_vcu_all__msg__MotorState__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__MotorState);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__MotorState * data =
      (sl_vcu_all__msg__MotorState *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__MotorState__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__MotorState__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__MotorState__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
