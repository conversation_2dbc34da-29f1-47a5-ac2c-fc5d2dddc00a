// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/bumper_state.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"

/// Struct defined in msg/BumperState in the package sl_vcu_all.
typedef struct sl_vcu_all__msg__BumperState
{
  std_msgs__msg__Header header;
  bool front_bumper_triggered;
  bool back_bumper_triggered;
  /// Combined status for additional info
  uint32_t bumper_status;
} sl_vcu_all__msg__BumperState;

// Struct for a sequence of sl_vcu_all__msg__BumperState.
typedef struct sl_vcu_all__msg__BumperState__Sequence
{
  sl_vcu_all__msg__BumperState * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__BumperState__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_H_
