// generated from rosidl_generator_c/resource/idl__type_support.c.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

#include <string.h>

#include "sl_vcu_all/msg/detail/bumper_state__functions.h"
#include "sl_vcu_all/msg/detail/bumper_state__struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/detail/bumper_state__type_support.h"

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef __cplusplus
}
#endif
