// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/CanFrame.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/can_frame.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"

/// Struct defined in msg/CanFrame in the package sl_vcu_all.
/**
  * CAN Frame message definition
  * This message represents a single CAN frame
 */
typedef struct sl_vcu_all__msg__CanFrame
{
  std_msgs__msg__Header header;
  uint32_t id;
  bool is_rtr;
  bool is_extended;
  bool is_error;
  uint8_t dlc;
  uint8_t data[8];
} sl_vcu_all__msg__CanFrame;

// Struct for a sequence of sl_vcu_all__msg__CanFrame.
typedef struct sl_vcu_all__msg__CanFrame__Sequence
{
  sl_vcu_all__msg__CanFrame * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__CanFrame__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__STRUCT_H_
