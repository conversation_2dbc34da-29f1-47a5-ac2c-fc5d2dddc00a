// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/channel_data.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'parts'
#include "rosidl_runtime_c/primitives_sequence.h"
// Member 'parts_data'
#include "sl_vcu_all/msg/detail/part_data__struct.h"

/// Struct defined in msg/ChannelData in the package sl_vcu_all.
/**
  * Channel Data for LED Control
  * This message defines the control data for a single LED strip channel
 */
typedef struct sl_vcu_all__msg__ChannelData
{
  /// Part selection and synchronization
  /// Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)
  rosidl_runtime_c__uint8__Sequence parts;
  /// Whether to synchronize parts within this channel
  bool sync_parts;
  /// Part data array
  /// Data for each part (size should match parts array or 1 if sync_parts=true)
  sl_vcu_all__msg__PartData__Sequence parts_data;
} sl_vcu_all__msg__ChannelData;

// Struct for a sequence of sl_vcu_all__msg__ChannelData.
typedef struct sl_vcu_all__msg__ChannelData__Sequence
{
  sl_vcu_all__msg__ChannelData * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__ChannelData__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_H_
