// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from sl_vcu_all:msg/CanFrame.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/can_frame.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__TYPE_SUPPORT_H_
#define SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "sl_vcu_all/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  sl_vcu_all,
  msg,
  CanFrame
)(void);

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__TYPE_SUPPORT_H_
