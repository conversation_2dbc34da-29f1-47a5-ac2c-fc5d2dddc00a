// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/part_data__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `mode`
#include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__msg__PartData__init(sl_vcu_all__msg__PartData * msg)
{
  if (!msg) {
    return false;
  }
  // mode
  if (!rosidl_runtime_c__String__init(&msg->mode)) {
    sl_vcu_all__msg__PartData__fini(msg);
    return false;
  }
  // green_brightness
  // red_brightness
  // blue_brightness
  // frequency
  // speed
  // marquee_direction
  // on_time_duty
  return true;
}

void
sl_vcu_all__msg__PartData__fini(sl_vcu_all__msg__PartData * msg)
{
  if (!msg) {
    return;
  }
  // mode
  rosidl_runtime_c__String__fini(&msg->mode);
  // green_brightness
  // red_brightness
  // blue_brightness
  // frequency
  // speed
  // marquee_direction
  // on_time_duty
}

bool
sl_vcu_all__msg__PartData__are_equal(const sl_vcu_all__msg__PartData * lhs, const sl_vcu_all__msg__PartData * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // mode
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->mode), &(rhs->mode)))
  {
    return false;
  }
  // green_brightness
  if (lhs->green_brightness != rhs->green_brightness) {
    return false;
  }
  // red_brightness
  if (lhs->red_brightness != rhs->red_brightness) {
    return false;
  }
  // blue_brightness
  if (lhs->blue_brightness != rhs->blue_brightness) {
    return false;
  }
  // frequency
  if (lhs->frequency != rhs->frequency) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // marquee_direction
  if (lhs->marquee_direction != rhs->marquee_direction) {
    return false;
  }
  // on_time_duty
  if (lhs->on_time_duty != rhs->on_time_duty) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__PartData__copy(
  const sl_vcu_all__msg__PartData * input,
  sl_vcu_all__msg__PartData * output)
{
  if (!input || !output) {
    return false;
  }
  // mode
  if (!rosidl_runtime_c__String__copy(
      &(input->mode), &(output->mode)))
  {
    return false;
  }
  // green_brightness
  output->green_brightness = input->green_brightness;
  // red_brightness
  output->red_brightness = input->red_brightness;
  // blue_brightness
  output->blue_brightness = input->blue_brightness;
  // frequency
  output->frequency = input->frequency;
  // speed
  output->speed = input->speed;
  // marquee_direction
  output->marquee_direction = input->marquee_direction;
  // on_time_duty
  output->on_time_duty = input->on_time_duty;
  return true;
}

sl_vcu_all__msg__PartData *
sl_vcu_all__msg__PartData__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__PartData * msg = (sl_vcu_all__msg__PartData *)allocator.allocate(sizeof(sl_vcu_all__msg__PartData), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__PartData));
  bool success = sl_vcu_all__msg__PartData__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__PartData__destroy(sl_vcu_all__msg__PartData * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__PartData__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__PartData__Sequence__init(sl_vcu_all__msg__PartData__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__PartData * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__PartData *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__PartData), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__PartData__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__PartData__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__PartData__Sequence__fini(sl_vcu_all__msg__PartData__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__PartData__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__PartData__Sequence *
sl_vcu_all__msg__PartData__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__PartData__Sequence * array = (sl_vcu_all__msg__PartData__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__PartData__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__PartData__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__PartData__Sequence__destroy(sl_vcu_all__msg__PartData__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__PartData__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__PartData__Sequence__are_equal(const sl_vcu_all__msg__PartData__Sequence * lhs, const sl_vcu_all__msg__PartData__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__PartData__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__PartData__Sequence__copy(
  const sl_vcu_all__msg__PartData__Sequence * input,
  sl_vcu_all__msg__PartData__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__PartData);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__PartData * data =
      (sl_vcu_all__msg__PartData *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__PartData__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__PartData__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__PartData__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
