// generated from rosidl_generator_c/resource/idl__type_support.c.em
// with input from sl_vcu_all:msg/BatteryStatus.idl
// generated code does not contain a copyright notice

#include <string.h>

#include "sl_vcu_all/msg/detail/battery_status__functions.h"
#include "rosidl_typesupport_interface/macros.h"
#include "sl_vcu_all/msg/detail/battery_status__struct.h"
#include "sl_vcu_all/msg/detail/battery_status__type_support.h"

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef __cplusplus
}
#endif
