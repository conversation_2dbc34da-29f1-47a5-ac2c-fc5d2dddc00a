// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/part_data.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__PART_DATA__FUNCTIONS_H_
#define SL_VCU_ALL__MSG__DETAIL__PART_DATA__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/action_type_support_struct.h"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_runtime_c/service_type_support_struct.h"
#include "rosidl_runtime_c/type_description/type_description__struct.h"
#include "rosidl_runtime_c/type_description/type_source__struct.h"
#include "rosidl_runtime_c/type_hash.h"
#include "rosidl_runtime_c/visibility_control.h"
#include "sl_vcu_all/msg/rosidl_generator_c__visibility_control.h"

#include "sl_vcu_all/msg/detail/part_data__struct.h"

/// Initialize msg/PartData message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * sl_vcu_all__msg__PartData
 * )) before or use
 * sl_vcu_all__msg__PartData__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__init(sl_vcu_all__msg__PartData * msg);

/// Finalize msg/PartData message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
void
sl_vcu_all__msg__PartData__fini(sl_vcu_all__msg__PartData * msg);

/// Create msg/PartData message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * sl_vcu_all__msg__PartData__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
sl_vcu_all__msg__PartData *
sl_vcu_all__msg__PartData__create(void);

/// Destroy msg/PartData message.
/**
 * It calls
 * sl_vcu_all__msg__PartData__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
void
sl_vcu_all__msg__PartData__destroy(sl_vcu_all__msg__PartData * msg);

/// Check for msg/PartData message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__are_equal(const sl_vcu_all__msg__PartData * lhs, const sl_vcu_all__msg__PartData * rhs);

/// Copy a msg/PartData message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__copy(
  const sl_vcu_all__msg__PartData * input,
  sl_vcu_all__msg__PartData * output);

/// Retrieve pointer to the hash of the description of this type.
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__msg__PartData__get_type_hash(
  const rosidl_message_type_support_t * type_support);

/// Retrieve pointer to the description of this type.
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__msg__PartData__get_type_description(
  const rosidl_message_type_support_t * type_support);

/// Retrieve pointer to the single raw source text that defined this type.
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__msg__PartData__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support);

/// Retrieve pointer to the recursive raw sources that defined the description of this type.
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__msg__PartData__get_type_description_sources(
  const rosidl_message_type_support_t * type_support);

/// Initialize array of msg/PartData messages.
/**
 * It allocates the memory for the number of elements and calls
 * sl_vcu_all__msg__PartData__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__Sequence__init(sl_vcu_all__msg__PartData__Sequence * array, size_t size);

/// Finalize array of msg/PartData messages.
/**
 * It calls
 * sl_vcu_all__msg__PartData__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
void
sl_vcu_all__msg__PartData__Sequence__fini(sl_vcu_all__msg__PartData__Sequence * array);

/// Create array of msg/PartData messages.
/**
 * It allocates the memory for the array and calls
 * sl_vcu_all__msg__PartData__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
sl_vcu_all__msg__PartData__Sequence *
sl_vcu_all__msg__PartData__Sequence__create(size_t size);

/// Destroy array of msg/PartData messages.
/**
 * It calls
 * sl_vcu_all__msg__PartData__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
void
sl_vcu_all__msg__PartData__Sequence__destroy(sl_vcu_all__msg__PartData__Sequence * array);

/// Check for msg/PartData message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__Sequence__are_equal(const sl_vcu_all__msg__PartData__Sequence * lhs, const sl_vcu_all__msg__PartData__Sequence * rhs);

/// Copy an array of msg/PartData messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
bool
sl_vcu_all__msg__PartData__Sequence__copy(
  const sl_vcu_all__msg__PartData__Sequence * input,
  sl_vcu_all__msg__PartData__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__PART_DATA__FUNCTIONS_H_
