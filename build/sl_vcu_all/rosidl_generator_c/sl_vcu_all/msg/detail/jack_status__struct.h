// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/jack_status.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"
// Member 'current_stage'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/JackStatus in the package sl_vcu_all.
/**
  * Jack Status Message
  * This message contains the current status information of the jack control system
 */
typedef struct sl_vcu_all__msg__JackStatus
{
  std_msgs__msg__Header header;
  /// Current operational stage
  /// Current stage: "init", "detecting_base", "base_stop", "lifting_up", "lifting_down", "top_stop", "middle_stop"
  rosidl_runtime_c__String current_stage;
  /// Position and status information
  /// Current position in encoder counts
  int32_t current_position;
  /// Current status word from jack controller
  uint16_t current_status;
  /// Current alarm code from jack controller
  uint16_t current_alarm;
} sl_vcu_all__msg__JackStatus;

// Struct for a sequence of sl_vcu_all__msg__JackStatus.
typedef struct sl_vcu_all__msg__JackStatus__Sequence
{
  sl_vcu_all__msg__JackStatus * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__JackStatus__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__STRUCT_H_
