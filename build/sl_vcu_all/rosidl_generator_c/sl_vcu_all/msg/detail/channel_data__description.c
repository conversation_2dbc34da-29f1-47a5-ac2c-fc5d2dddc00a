// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/msg/detail/channel_data__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__msg__ChannelData__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x2d, 0xd7, 0x3e, 0xf9, 0x32, 0xed, 0x81, 0xbc,
      0xdf, 0xb0, 0xbd, 0x1a, 0x4b, 0x34, 0x91, 0x7e,
      0x63, 0x92, 0x80, 0x25, 0x82, 0x33, 0x5c, 0x1c,
      0xb5, 0xeb, 0x17, 0xab, 0x89, 0xfa, 0x30, 0x6c,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "sl_vcu_all/msg/detail/part_data__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t sl_vcu_all__msg__PartData__EXPECTED_HASH = {1, {
    0x44, 0xe0, 0x9f, 0x9f, 0x38, 0x47, 0x2e, 0x21,
    0x2f, 0xec, 0x22, 0xe7, 0x18, 0x2c, 0x21, 0x2d,
    0x97, 0xba, 0x1b, 0x65, 0x01, 0xc5, 0x0e, 0x89,
    0xfb, 0x31, 0x19, 0x9e, 0x90, 0x8f, 0x32, 0xc3,
  }};
#endif

static char sl_vcu_all__msg__ChannelData__TYPE_NAME[] = "sl_vcu_all/msg/ChannelData";
static char sl_vcu_all__msg__PartData__TYPE_NAME[] = "sl_vcu_all/msg/PartData";

// Define type names, field names, and default values
static char sl_vcu_all__msg__ChannelData__FIELD_NAME__parts[] = "parts";
static char sl_vcu_all__msg__ChannelData__FIELD_NAME__sync_parts[] = "sync_parts";
static char sl_vcu_all__msg__ChannelData__FIELD_NAME__parts_data[] = "parts_data";

static rosidl_runtime_c__type_description__Field sl_vcu_all__msg__ChannelData__FIELDS[] = {
  {
    {sl_vcu_all__msg__ChannelData__FIELD_NAME__parts, 5, 5},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8_UNBOUNDED_SEQUENCE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__ChannelData__FIELD_NAME__sync_parts, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__ChannelData__FIELD_NAME__parts_data, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_UNBOUNDED_SEQUENCE,
      0,
      0,
      {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__msg__ChannelData__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__msg__ChannelData__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
      {sl_vcu_all__msg__ChannelData__FIELDS, 3, 3},
    },
    {sl_vcu_all__msg__ChannelData__REFERENCED_TYPE_DESCRIPTIONS, 1, 1},
  };
  if (!constructed) {
    assert(0 == memcmp(&sl_vcu_all__msg__PartData__EXPECTED_HASH, sl_vcu_all__msg__PartData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = sl_vcu_all__msg__PartData__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# Channel Data for LED Control\n"
  "# This message defines the control data for a single LED strip channel\n"
  "\n"
  "# Part selection and synchronization\n"
  "uint8[] parts                  # Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)\n"
  "bool sync_parts                # Whether to synchronize parts within this channel\n"
  "\n"
  "# Part data array\n"
  "PartData[] parts_data          # Data for each part (size should match parts array or 1 if sync_parts=true)";

static char msg_encoding[] = "msg";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__msg__ChannelData__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
    {msg_encoding, 3, 3},
    {toplevel_type_raw_source, 451, 451},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__msg__ChannelData__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[2];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 2, 2};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__msg__ChannelData__get_individual_type_description_source(NULL),
    sources[1] = *sl_vcu_all__msg__PartData__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
