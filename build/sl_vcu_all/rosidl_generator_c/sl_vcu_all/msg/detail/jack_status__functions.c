// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/jack_status__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"
// Member `current_stage`
#include "rosidl_runtime_c/string_functions.h"

bool
sl_vcu_all__msg__JackStatus__init(sl_vcu_all__msg__JackStatus * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    sl_vcu_all__msg__JackStatus__fini(msg);
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__init(&msg->current_stage)) {
    sl_vcu_all__msg__JackStatus__fini(msg);
    return false;
  }
  // current_position
  // current_status
  // current_alarm
  return true;
}

void
sl_vcu_all__msg__JackStatus__fini(sl_vcu_all__msg__JackStatus * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // current_stage
  rosidl_runtime_c__String__fini(&msg->current_stage);
  // current_position
  // current_status
  // current_alarm
}

bool
sl_vcu_all__msg__JackStatus__are_equal(const sl_vcu_all__msg__JackStatus * lhs, const sl_vcu_all__msg__JackStatus * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->current_stage), &(rhs->current_stage)))
  {
    return false;
  }
  // current_position
  if (lhs->current_position != rhs->current_position) {
    return false;
  }
  // current_status
  if (lhs->current_status != rhs->current_status) {
    return false;
  }
  // current_alarm
  if (lhs->current_alarm != rhs->current_alarm) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__JackStatus__copy(
  const sl_vcu_all__msg__JackStatus * input,
  sl_vcu_all__msg__JackStatus * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // current_stage
  if (!rosidl_runtime_c__String__copy(
      &(input->current_stage), &(output->current_stage)))
  {
    return false;
  }
  // current_position
  output->current_position = input->current_position;
  // current_status
  output->current_status = input->current_status;
  // current_alarm
  output->current_alarm = input->current_alarm;
  return true;
}

sl_vcu_all__msg__JackStatus *
sl_vcu_all__msg__JackStatus__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__JackStatus * msg = (sl_vcu_all__msg__JackStatus *)allocator.allocate(sizeof(sl_vcu_all__msg__JackStatus), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__JackStatus));
  bool success = sl_vcu_all__msg__JackStatus__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__JackStatus__destroy(sl_vcu_all__msg__JackStatus * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__JackStatus__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__JackStatus__Sequence__init(sl_vcu_all__msg__JackStatus__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__JackStatus * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__JackStatus *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__JackStatus), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__JackStatus__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__JackStatus__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__JackStatus__Sequence__fini(sl_vcu_all__msg__JackStatus__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__JackStatus__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__JackStatus__Sequence *
sl_vcu_all__msg__JackStatus__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__JackStatus__Sequence * array = (sl_vcu_all__msg__JackStatus__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__JackStatus__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__JackStatus__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__JackStatus__Sequence__destroy(sl_vcu_all__msg__JackStatus__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__JackStatus__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__JackStatus__Sequence__are_equal(const sl_vcu_all__msg__JackStatus__Sequence * lhs, const sl_vcu_all__msg__JackStatus__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__JackStatus__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__JackStatus__Sequence__copy(
  const sl_vcu_all__msg__JackStatus__Sequence * input,
  sl_vcu_all__msg__JackStatus__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__JackStatus);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__JackStatus * data =
      (sl_vcu_all__msg__JackStatus *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__JackStatus__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__JackStatus__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__JackStatus__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
