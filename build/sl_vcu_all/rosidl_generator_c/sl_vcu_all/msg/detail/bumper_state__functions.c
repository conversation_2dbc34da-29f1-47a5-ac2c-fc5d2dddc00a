// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/msg/detail/bumper_state__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"

bool
sl_vcu_all__msg__BumperState__init(sl_vcu_all__msg__BumperState * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    sl_vcu_all__msg__BumperState__fini(msg);
    return false;
  }
  // front_bumper_triggered
  // back_bumper_triggered
  // bumper_status
  return true;
}

void
sl_vcu_all__msg__BumperState__fini(sl_vcu_all__msg__BumperState * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // front_bumper_triggered
  // back_bumper_triggered
  // bumper_status
}

bool
sl_vcu_all__msg__BumperState__are_equal(const sl_vcu_all__msg__BumperState * lhs, const sl_vcu_all__msg__BumperState * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // front_bumper_triggered
  if (lhs->front_bumper_triggered != rhs->front_bumper_triggered) {
    return false;
  }
  // back_bumper_triggered
  if (lhs->back_bumper_triggered != rhs->back_bumper_triggered) {
    return false;
  }
  // bumper_status
  if (lhs->bumper_status != rhs->bumper_status) {
    return false;
  }
  return true;
}

bool
sl_vcu_all__msg__BumperState__copy(
  const sl_vcu_all__msg__BumperState * input,
  sl_vcu_all__msg__BumperState * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // front_bumper_triggered
  output->front_bumper_triggered = input->front_bumper_triggered;
  // back_bumper_triggered
  output->back_bumper_triggered = input->back_bumper_triggered;
  // bumper_status
  output->bumper_status = input->bumper_status;
  return true;
}

sl_vcu_all__msg__BumperState *
sl_vcu_all__msg__BumperState__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__BumperState * msg = (sl_vcu_all__msg__BumperState *)allocator.allocate(sizeof(sl_vcu_all__msg__BumperState), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__msg__BumperState));
  bool success = sl_vcu_all__msg__BumperState__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__msg__BumperState__destroy(sl_vcu_all__msg__BumperState * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__msg__BumperState__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__msg__BumperState__Sequence__init(sl_vcu_all__msg__BumperState__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__BumperState * data = NULL;

  if (size) {
    data = (sl_vcu_all__msg__BumperState *)allocator.zero_allocate(size, sizeof(sl_vcu_all__msg__BumperState), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__msg__BumperState__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__msg__BumperState__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__msg__BumperState__Sequence__fini(sl_vcu_all__msg__BumperState__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__msg__BumperState__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__msg__BumperState__Sequence *
sl_vcu_all__msg__BumperState__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__msg__BumperState__Sequence * array = (sl_vcu_all__msg__BumperState__Sequence *)allocator.allocate(sizeof(sl_vcu_all__msg__BumperState__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__msg__BumperState__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__msg__BumperState__Sequence__destroy(sl_vcu_all__msg__BumperState__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__msg__BumperState__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__msg__BumperState__Sequence__are_equal(const sl_vcu_all__msg__BumperState__Sequence * lhs, const sl_vcu_all__msg__BumperState__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__msg__BumperState__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__msg__BumperState__Sequence__copy(
  const sl_vcu_all__msg__BumperState__Sequence * input,
  sl_vcu_all__msg__BumperState__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__msg__BumperState);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__msg__BumperState * data =
      (sl_vcu_all__msg__BumperState *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__msg__BumperState__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__msg__BumperState__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__msg__BumperState__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
