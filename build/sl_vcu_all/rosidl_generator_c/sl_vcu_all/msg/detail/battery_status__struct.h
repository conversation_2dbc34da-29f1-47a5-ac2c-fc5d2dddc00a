// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/BatteryStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/battery_status.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"

/// Struct defined in msg/BatteryStatus in the package sl_vcu_all.
/**
  * Battery status message
 */
typedef struct sl_vcu_all__msg__BatteryStatus
{
  std_msgs__msg__Header header;
  /// Battery states
  /// Whether the battery is being charged automatically
  bool is_auto_charging;
  /// Whether the battery is being charged manually
  bool is_manual_charging;
  /// Whether the battery is discharging
  bool is_discharging;
  /// Remaining battery percentage (0-100)
  float remaining_percent;
} sl_vcu_all__msg__BatteryStatus;

// Struct for a sequence of sl_vcu_all__msg__BatteryStatus.
typedef struct sl_vcu_all__msg__BatteryStatus__Sequence
{
  sl_vcu_all__msg__BatteryStatus * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__BatteryStatus__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_H_
