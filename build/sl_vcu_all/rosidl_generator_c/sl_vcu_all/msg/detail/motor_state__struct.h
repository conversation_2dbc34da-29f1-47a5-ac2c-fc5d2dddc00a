// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/MotorState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_state.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"
// Member 'state'
// Member 'error_info'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/MotorState in the package sl_vcu_all.
/**
  * Motor state message
 */
typedef struct sl_vcu_all__msg__MotorState
{
  std_msgs__msg__Header header;
  /// Brake and emergency status
  /// true if brake is released
  bool brake_release;
  /// true if emergency stop is triggered
  bool emergency_stop;
  /// Motor state
  /// "running", "error"
  rosidl_runtime_c__String state;
  /// Motor alarm/error code
  int32_t error_code;
  /// Human readable error information
  rosidl_runtime_c__String error_info;
} sl_vcu_all__msg__MotorState;

// Struct for a sequence of sl_vcu_all__msg__MotorState.
typedef struct sl_vcu_all__msg__MotorState__Sequence
{
  sl_vcu_all__msg__MotorState * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__MotorState__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_H_
