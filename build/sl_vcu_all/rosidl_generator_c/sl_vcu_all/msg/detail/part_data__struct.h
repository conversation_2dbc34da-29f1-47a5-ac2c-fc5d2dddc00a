// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/part_data.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'mode'
#include "rosidl_runtime_c/string.h"

/// Struct defined in msg/PartData in the package sl_vcu_all.
/**
  * Part Data for LED Control
  * This message defines the control data for a single part of an LED strip
 */
typedef struct sl_vcu_all__msg__PartData
{
  /// Lighting mode (required)
  /// "off", "on", "breathing", "flashing", "marquee"
  rosidl_runtime_c__String mode;
  /// Color brightness settings (optional, defaults applied if not set)
  /// Green brightness (0-255)
  uint8_t green_brightness;
  /// Red brightness (0-255)
  uint8_t red_brightness;
  /// Blue brightness (0-255)
  uint8_t blue_brightness;
  /// Effect parameters (optional, defaults applied if not set)
  /// Frequency for breathing/flashing effects (Hz)
  float frequency;
  /// Speed for marquee effect (pixels/second)
  float speed;
  /// Direction for marquee: true=forward, false=reverse
  bool marquee_direction;
  /// Duty cycle for flashing effect (0.0-1.0, default 0.5)
  float on_time_duty;
} sl_vcu_all__msg__PartData;

// Struct for a sequence of sl_vcu_all__msg__PartData.
typedef struct sl_vcu_all__msg__PartData__Sequence
{
  sl_vcu_all__msg__PartData * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__PartData__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_H_
