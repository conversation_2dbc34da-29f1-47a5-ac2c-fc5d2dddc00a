// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_info.h"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_H_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"

/// Struct defined in msg/MotorInfo in the package sl_vcu_all.
typedef struct sl_vcu_all__msg__MotorInfo
{
  std_msgs__msg__Header header;
  double left_current;
  double right_current;
  double left_temp;
  double right_temp;
  double driver_temp;
  int32_t left_pos_encoder;
  int32_t right_pos_encoder;
  uint32_t alarm_code;
} sl_vcu_all__msg__MotorInfo;

// Struct for a sequence of sl_vcu_all__msg__MotorInfo.
typedef struct sl_vcu_all__msg__MotorInfo__Sequence
{
  sl_vcu_all__msg__MotorInfo * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__msg__MotorInfo__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_H_
