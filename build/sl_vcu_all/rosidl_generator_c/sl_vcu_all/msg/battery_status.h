// generated from rosidl_generator_c/resource/idl.h.em
// with input from sl_vcu_all:msg/BatteryStatus.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__MSG__BATTERY_STATUS_H_
#define SL_VCU_ALL__MSG__BATTERY_STATUS_H_

#include "sl_vcu_all/msg/detail/battery_status__struct.h"
#include "sl_vcu_all/msg/detail/battery_status__functions.h"
#include "sl_vcu_all/msg/detail/battery_status__type_support.h"

#endif  // SL_VCU_ALL__MSG__BATTERY_STATUS_H_
