// generated from rosidl_generator_c/resource/idl.h.em
// with input from sl_vcu_all:srv/LedControl.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__SRV__LED_CONTROL_H_
#define SL_VCU_ALL__SRV__LED_CONTROL_H_

#include "sl_vcu_all/srv/detail/led_control__struct.h"
#include "sl_vcu_all/srv/detail/led_control__functions.h"
#include "sl_vcu_all/srv/detail/led_control__type_support.h"

#endif  // SL_VCU_ALL__SRV__LED_CONTROL_H_
