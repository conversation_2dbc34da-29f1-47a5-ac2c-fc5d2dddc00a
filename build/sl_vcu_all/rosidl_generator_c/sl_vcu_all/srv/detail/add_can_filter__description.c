// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:srv/AddCanFilter.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/srv/detail/add_can_filter__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__AddCanFilter__get_type_hash(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xad, 0xb1, 0x46, 0x8e, 0x50, 0x7b, 0x6f, 0x3d,
      0x8b, 0x93, 0xfa, 0x53, 0xe3, 0x02, 0xba, 0xe3,
      0x83, 0x8c, 0x95, 0x99, 0x29, 0xd1, 0x8a, 0xdd,
      0x5c, 0xe9, 0x50, 0x26, 0xb9, 0xe2, 0x66, 0x8c,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__AddCanFilter_Request__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x2a, 0xcb, 0x9a, 0xfd, 0xf6, 0x93, 0xdb, 0x9b,
      0x67, 0x22, 0x74, 0x9e, 0xb2, 0x10, 0x78, 0xaf,
      0xff, 0x72, 0x47, 0xe8, 0xc9, 0x31, 0x4f, 0x61,
      0xf0, 0x5d, 0xbc, 0x2d, 0xdb, 0x4c, 0x85, 0x59,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__AddCanFilter_Response__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x6a, 0x3e, 0x00, 0x51, 0x85, 0x64, 0x0a, 0x69,
      0x0a, 0xe7, 0x94, 0x36, 0x5c, 0x6c, 0xac, 0xeb,
      0x7f, 0x20, 0x84, 0x96, 0x1f, 0xda, 0x85, 0x4d,
      0xa9, 0x23, 0xe4, 0x6b, 0x9b, 0xaa, 0x03, 0x52,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__AddCanFilter_Event__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x17, 0x7f, 0xdc, 0x55, 0x1a, 0xc3, 0x36, 0x9c,
      0xf7, 0xe9, 0x80, 0xb6, 0x60, 0x6a, 0xb5, 0x3e,
      0x34, 0xe5, 0x92, 0x0e, 0x10, 0x96, 0x93, 0x70,
      0xf9, 0xa2, 0x01, 0x48, 0x71, 0x4d, 0x49, 0x36,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "service_msgs/msg/detail/service_event_info__functions.h"
#include "builtin_interfaces/msg/detail/time__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t builtin_interfaces__msg__Time__EXPECTED_HASH = {1, {
    0xb1, 0x06, 0x23, 0x5e, 0x25, 0xa4, 0xc5, 0xed,
    0x35, 0x09, 0x8a, 0xa0, 0xa6, 0x1a, 0x3e, 0xe9,
    0xc9, 0xb1, 0x8d, 0x19, 0x7f, 0x39, 0x8b, 0x0e,
    0x42, 0x06, 0xce, 0xa9, 0xac, 0xf9, 0xc1, 0x97,
  }};
static const rosidl_type_hash_t service_msgs__msg__ServiceEventInfo__EXPECTED_HASH = {1, {
    0x41, 0xbc, 0xbb, 0xe0, 0x7a, 0x75, 0xc9, 0xb5,
    0x2b, 0xc9, 0x6b, 0xfd, 0x5c, 0x24, 0xd7, 0xf0,
    0xfc, 0x0a, 0x08, 0xc0, 0xcb, 0x79, 0x21, 0xb3,
    0x37, 0x3c, 0x57, 0x32, 0x34, 0x5a, 0x6f, 0x45,
  }};
#endif

static char sl_vcu_all__srv__AddCanFilter__TYPE_NAME[] = "sl_vcu_all/srv/AddCanFilter";
static char builtin_interfaces__msg__Time__TYPE_NAME[] = "builtin_interfaces/msg/Time";
static char service_msgs__msg__ServiceEventInfo__TYPE_NAME[] = "service_msgs/msg/ServiceEventInfo";
static char sl_vcu_all__srv__AddCanFilter_Event__TYPE_NAME[] = "sl_vcu_all/srv/AddCanFilter_Event";
static char sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME[] = "sl_vcu_all/srv/AddCanFilter_Request";
static char sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME[] = "sl_vcu_all/srv/AddCanFilter_Response";

// Define type names, field names, and default values
static char sl_vcu_all__srv__AddCanFilter__FIELD_NAME__request_message[] = "request_message";
static char sl_vcu_all__srv__AddCanFilter__FIELD_NAME__response_message[] = "response_message";
static char sl_vcu_all__srv__AddCanFilter__FIELD_NAME__event_message[] = "event_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__AddCanFilter__FIELDS[] = {
  {
    {sl_vcu_all__srv__AddCanFilter__FIELD_NAME__request_message, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter__FIELD_NAME__response_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter__FIELD_NAME__event_message, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__AddCanFilter_Event__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__AddCanFilter__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Event__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__AddCanFilter__get_type_description(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__AddCanFilter__TYPE_NAME, 27, 27},
      {sl_vcu_all__srv__AddCanFilter__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__AddCanFilter__REFERENCED_TYPE_DESCRIPTIONS, 5, 5},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__srv__AddCanFilter_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__srv__AddCanFilter_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__srv__AddCanFilter_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__AddCanFilter_Request__FIELD_NAME__can_id[] = "can_id";
static char sl_vcu_all__srv__AddCanFilter_Request__FIELD_NAME__topic_name[] = "topic_name";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__AddCanFilter_Request__FIELDS[] = {
  {
    {sl_vcu_all__srv__AddCanFilter_Request__FIELD_NAME__can_id, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT32,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Request__FIELD_NAME__topic_name, 10, 10},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__AddCanFilter_Request__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
      {sl_vcu_all__srv__AddCanFilter_Request__FIELDS, 2, 2},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__AddCanFilter_Response__FIELD_NAME__success[] = "success";
static char sl_vcu_all__srv__AddCanFilter_Response__FIELD_NAME__message[] = "message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__AddCanFilter_Response__FIELDS[] = {
  {
    {sl_vcu_all__srv__AddCanFilter_Response__FIELD_NAME__success, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Response__FIELD_NAME__message, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__AddCanFilter_Response__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
      {sl_vcu_all__srv__AddCanFilter_Response__FIELDS, 2, 2},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__info[] = "info";
static char sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__request[] = "request";
static char sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__response[] = "response";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__AddCanFilter_Event__FIELDS[] = {
  {
    {sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__info, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__request, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Event__FIELD_NAME__response, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__AddCanFilter_Event__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__AddCanFilter_Event__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__AddCanFilter_Event__TYPE_NAME, 33, 33},
      {sl_vcu_all__srv__AddCanFilter_Event__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__AddCanFilter_Event__REFERENCED_TYPE_DESCRIPTIONS, 4, 4},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__srv__AddCanFilter_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__srv__AddCanFilter_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# Request\n"
  "uint32 can_id      # CAN ID to filter\n"
  "string topic_name  # Topic name to publish messages for this CAN ID\n"
  "---\n"
  "# Response\n"
  "bool success       # Whether the filter was added successfully\n"
  "string message     # Success or error message ";

static char srv_encoding[] = "srv";
static char implicit_encoding[] = "implicit";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__AddCanFilter__get_individual_type_description_source(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__AddCanFilter__TYPE_NAME, 27, 27},
    {srv_encoding, 3, 3},
    {toplevel_type_raw_source, 240, 240},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__AddCanFilter_Request__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__AddCanFilter_Request__TYPE_NAME, 35, 35},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__AddCanFilter_Response__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__AddCanFilter_Response__TYPE_NAME, 36, 36},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__AddCanFilter_Event__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__AddCanFilter_Event__TYPE_NAME, 33, 33},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__AddCanFilter__get_type_description_sources(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[6];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 6, 6};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__AddCanFilter__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__srv__AddCanFilter_Event__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__srv__AddCanFilter_Request__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__srv__AddCanFilter_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__AddCanFilter_Request__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__AddCanFilter_Request__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__AddCanFilter_Response__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__AddCanFilter_Response__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__AddCanFilter_Event__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[5];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 5, 5};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__AddCanFilter_Event__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__srv__AddCanFilter_Request__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__srv__AddCanFilter_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
