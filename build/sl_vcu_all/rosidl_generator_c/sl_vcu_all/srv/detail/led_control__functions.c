// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from sl_vcu_all:srv/LedControl.idl
// generated code does not contain a copyright notice
#include "sl_vcu_all/srv/detail/led_control__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"

// Include directives for member types
// Member `channels`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `channel_data`
#include "sl_vcu_all/msg/detail/channel_data__functions.h"

bool
sl_vcu_all__srv__LedControl_Request__init(sl_vcu_all__srv__LedControl_Request * msg)
{
  if (!msg) {
    return false;
  }
  // channels
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->channels, 0)) {
    sl_vcu_all__srv__LedControl_Request__fini(msg);
    return false;
  }
  // sync_channels
  // channel_data
  if (!sl_vcu_all__msg__ChannelData__Sequence__init(&msg->channel_data, 0)) {
    sl_vcu_all__srv__LedControl_Request__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__srv__LedControl_Request__fini(sl_vcu_all__srv__LedControl_Request * msg)
{
  if (!msg) {
    return;
  }
  // channels
  rosidl_runtime_c__uint8__Sequence__fini(&msg->channels);
  // sync_channels
  // channel_data
  sl_vcu_all__msg__ChannelData__Sequence__fini(&msg->channel_data);
}

bool
sl_vcu_all__srv__LedControl_Request__are_equal(const sl_vcu_all__srv__LedControl_Request * lhs, const sl_vcu_all__srv__LedControl_Request * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // channels
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->channels), &(rhs->channels)))
  {
    return false;
  }
  // sync_channels
  if (lhs->sync_channels != rhs->sync_channels) {
    return false;
  }
  // channel_data
  if (!sl_vcu_all__msg__ChannelData__Sequence__are_equal(
      &(lhs->channel_data), &(rhs->channel_data)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Request__copy(
  const sl_vcu_all__srv__LedControl_Request * input,
  sl_vcu_all__srv__LedControl_Request * output)
{
  if (!input || !output) {
    return false;
  }
  // channels
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->channels), &(output->channels)))
  {
    return false;
  }
  // sync_channels
  output->sync_channels = input->sync_channels;
  // channel_data
  if (!sl_vcu_all__msg__ChannelData__Sequence__copy(
      &(input->channel_data), &(output->channel_data)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__srv__LedControl_Request *
sl_vcu_all__srv__LedControl_Request__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Request * msg = (sl_vcu_all__srv__LedControl_Request *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Request), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__srv__LedControl_Request));
  bool success = sl_vcu_all__srv__LedControl_Request__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__srv__LedControl_Request__destroy(sl_vcu_all__srv__LedControl_Request * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__srv__LedControl_Request__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__srv__LedControl_Request__Sequence__init(sl_vcu_all__srv__LedControl_Request__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Request * data = NULL;

  if (size) {
    data = (sl_vcu_all__srv__LedControl_Request *)allocator.zero_allocate(size, sizeof(sl_vcu_all__srv__LedControl_Request), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__srv__LedControl_Request__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__srv__LedControl_Request__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__srv__LedControl_Request__Sequence__fini(sl_vcu_all__srv__LedControl_Request__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__srv__LedControl_Request__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__srv__LedControl_Request__Sequence *
sl_vcu_all__srv__LedControl_Request__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Request__Sequence * array = (sl_vcu_all__srv__LedControl_Request__Sequence *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Request__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__srv__LedControl_Request__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__srv__LedControl_Request__Sequence__destroy(sl_vcu_all__srv__LedControl_Request__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__srv__LedControl_Request__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__srv__LedControl_Request__Sequence__are_equal(const sl_vcu_all__srv__LedControl_Request__Sequence * lhs, const sl_vcu_all__srv__LedControl_Request__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Request__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Request__Sequence__copy(
  const sl_vcu_all__srv__LedControl_Request__Sequence * input,
  sl_vcu_all__srv__LedControl_Request__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__srv__LedControl_Request);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__srv__LedControl_Request * data =
      (sl_vcu_all__srv__LedControl_Request *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__srv__LedControl_Request__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__srv__LedControl_Request__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Request__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `message`
#include "rosidl_runtime_c/string_functions.h"
// Member `affected_channels`
// already included above
// #include "rosidl_runtime_c/primitives_sequence_functions.h"

bool
sl_vcu_all__srv__LedControl_Response__init(sl_vcu_all__srv__LedControl_Response * msg)
{
  if (!msg) {
    return false;
  }
  // success
  // message
  if (!rosidl_runtime_c__String__init(&msg->message)) {
    sl_vcu_all__srv__LedControl_Response__fini(msg);
    return false;
  }
  // affected_channels
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->affected_channels, 0)) {
    sl_vcu_all__srv__LedControl_Response__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__srv__LedControl_Response__fini(sl_vcu_all__srv__LedControl_Response * msg)
{
  if (!msg) {
    return;
  }
  // success
  // message
  rosidl_runtime_c__String__fini(&msg->message);
  // affected_channels
  rosidl_runtime_c__uint8__Sequence__fini(&msg->affected_channels);
}

bool
sl_vcu_all__srv__LedControl_Response__are_equal(const sl_vcu_all__srv__LedControl_Response * lhs, const sl_vcu_all__srv__LedControl_Response * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // success
  if (lhs->success != rhs->success) {
    return false;
  }
  // message
  if (!rosidl_runtime_c__String__are_equal(
      &(lhs->message), &(rhs->message)))
  {
    return false;
  }
  // affected_channels
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->affected_channels), &(rhs->affected_channels)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Response__copy(
  const sl_vcu_all__srv__LedControl_Response * input,
  sl_vcu_all__srv__LedControl_Response * output)
{
  if (!input || !output) {
    return false;
  }
  // success
  output->success = input->success;
  // message
  if (!rosidl_runtime_c__String__copy(
      &(input->message), &(output->message)))
  {
    return false;
  }
  // affected_channels
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->affected_channels), &(output->affected_channels)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__srv__LedControl_Response *
sl_vcu_all__srv__LedControl_Response__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Response * msg = (sl_vcu_all__srv__LedControl_Response *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Response), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__srv__LedControl_Response));
  bool success = sl_vcu_all__srv__LedControl_Response__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__srv__LedControl_Response__destroy(sl_vcu_all__srv__LedControl_Response * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__srv__LedControl_Response__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__srv__LedControl_Response__Sequence__init(sl_vcu_all__srv__LedControl_Response__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Response * data = NULL;

  if (size) {
    data = (sl_vcu_all__srv__LedControl_Response *)allocator.zero_allocate(size, sizeof(sl_vcu_all__srv__LedControl_Response), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__srv__LedControl_Response__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__srv__LedControl_Response__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__srv__LedControl_Response__Sequence__fini(sl_vcu_all__srv__LedControl_Response__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__srv__LedControl_Response__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__srv__LedControl_Response__Sequence *
sl_vcu_all__srv__LedControl_Response__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Response__Sequence * array = (sl_vcu_all__srv__LedControl_Response__Sequence *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Response__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__srv__LedControl_Response__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__srv__LedControl_Response__Sequence__destroy(sl_vcu_all__srv__LedControl_Response__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__srv__LedControl_Response__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__srv__LedControl_Response__Sequence__are_equal(const sl_vcu_all__srv__LedControl_Response__Sequence * lhs, const sl_vcu_all__srv__LedControl_Response__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Response__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Response__Sequence__copy(
  const sl_vcu_all__srv__LedControl_Response__Sequence * input,
  sl_vcu_all__srv__LedControl_Response__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__srv__LedControl_Response);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__srv__LedControl_Response * data =
      (sl_vcu_all__srv__LedControl_Response *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__srv__LedControl_Response__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__srv__LedControl_Response__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Response__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}


// Include directives for member types
// Member `info`
#include "service_msgs/msg/detail/service_event_info__functions.h"
// Member `request`
// Member `response`
// already included above
// #include "sl_vcu_all/srv/detail/led_control__functions.h"

bool
sl_vcu_all__srv__LedControl_Event__init(sl_vcu_all__srv__LedControl_Event * msg)
{
  if (!msg) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__init(&msg->info)) {
    sl_vcu_all__srv__LedControl_Event__fini(msg);
    return false;
  }
  // request
  if (!sl_vcu_all__srv__LedControl_Request__Sequence__init(&msg->request, 0)) {
    sl_vcu_all__srv__LedControl_Event__fini(msg);
    return false;
  }
  // response
  if (!sl_vcu_all__srv__LedControl_Response__Sequence__init(&msg->response, 0)) {
    sl_vcu_all__srv__LedControl_Event__fini(msg);
    return false;
  }
  return true;
}

void
sl_vcu_all__srv__LedControl_Event__fini(sl_vcu_all__srv__LedControl_Event * msg)
{
  if (!msg) {
    return;
  }
  // info
  service_msgs__msg__ServiceEventInfo__fini(&msg->info);
  // request
  sl_vcu_all__srv__LedControl_Request__Sequence__fini(&msg->request);
  // response
  sl_vcu_all__srv__LedControl_Response__Sequence__fini(&msg->response);
}

bool
sl_vcu_all__srv__LedControl_Event__are_equal(const sl_vcu_all__srv__LedControl_Event * lhs, const sl_vcu_all__srv__LedControl_Event * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__are_equal(
      &(lhs->info), &(rhs->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__srv__LedControl_Request__Sequence__are_equal(
      &(lhs->request), &(rhs->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__srv__LedControl_Response__Sequence__are_equal(
      &(lhs->response), &(rhs->response)))
  {
    return false;
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Event__copy(
  const sl_vcu_all__srv__LedControl_Event * input,
  sl_vcu_all__srv__LedControl_Event * output)
{
  if (!input || !output) {
    return false;
  }
  // info
  if (!service_msgs__msg__ServiceEventInfo__copy(
      &(input->info), &(output->info)))
  {
    return false;
  }
  // request
  if (!sl_vcu_all__srv__LedControl_Request__Sequence__copy(
      &(input->request), &(output->request)))
  {
    return false;
  }
  // response
  if (!sl_vcu_all__srv__LedControl_Response__Sequence__copy(
      &(input->response), &(output->response)))
  {
    return false;
  }
  return true;
}

sl_vcu_all__srv__LedControl_Event *
sl_vcu_all__srv__LedControl_Event__create(void)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Event * msg = (sl_vcu_all__srv__LedControl_Event *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Event), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(sl_vcu_all__srv__LedControl_Event));
  bool success = sl_vcu_all__srv__LedControl_Event__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
sl_vcu_all__srv__LedControl_Event__destroy(sl_vcu_all__srv__LedControl_Event * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    sl_vcu_all__srv__LedControl_Event__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
sl_vcu_all__srv__LedControl_Event__Sequence__init(sl_vcu_all__srv__LedControl_Event__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Event * data = NULL;

  if (size) {
    data = (sl_vcu_all__srv__LedControl_Event *)allocator.zero_allocate(size, sizeof(sl_vcu_all__srv__LedControl_Event), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = sl_vcu_all__srv__LedControl_Event__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        sl_vcu_all__srv__LedControl_Event__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
sl_vcu_all__srv__LedControl_Event__Sequence__fini(sl_vcu_all__srv__LedControl_Event__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      sl_vcu_all__srv__LedControl_Event__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

sl_vcu_all__srv__LedControl_Event__Sequence *
sl_vcu_all__srv__LedControl_Event__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  sl_vcu_all__srv__LedControl_Event__Sequence * array = (sl_vcu_all__srv__LedControl_Event__Sequence *)allocator.allocate(sizeof(sl_vcu_all__srv__LedControl_Event__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = sl_vcu_all__srv__LedControl_Event__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
sl_vcu_all__srv__LedControl_Event__Sequence__destroy(sl_vcu_all__srv__LedControl_Event__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    sl_vcu_all__srv__LedControl_Event__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
sl_vcu_all__srv__LedControl_Event__Sequence__are_equal(const sl_vcu_all__srv__LedControl_Event__Sequence * lhs, const sl_vcu_all__srv__LedControl_Event__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Event__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
sl_vcu_all__srv__LedControl_Event__Sequence__copy(
  const sl_vcu_all__srv__LedControl_Event__Sequence * input,
  sl_vcu_all__srv__LedControl_Event__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(sl_vcu_all__srv__LedControl_Event);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    sl_vcu_all__srv__LedControl_Event * data =
      (sl_vcu_all__srv__LedControl_Event *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!sl_vcu_all__srv__LedControl_Event__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          sl_vcu_all__srv__LedControl_Event__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!sl_vcu_all__srv__LedControl_Event__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
