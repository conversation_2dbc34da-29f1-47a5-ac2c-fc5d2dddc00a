// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:srv/LedControl.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/srv/detail/led_control__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__LedControl__get_type_hash(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x20, 0x8e, 0xd4, 0x6c, 0x49, 0xd5, 0xb2, 0xfa,
      0x97, 0xe8, 0x25, 0xd8, 0xc8, 0xa8, 0x46, 0xcb,
      0xd6, 0x4e, 0x98, 0x6e, 0x3b, 0x9d, 0xd5, 0x1d,
      0x8e, 0xa0, 0x8a, 0x5e, 0xa4, 0xc9, 0xbe, 0x6d,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__LedControl_Request__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x33, 0x80, 0x48, 0x85, 0x1e, 0xb6, 0x0a, 0x16,
      0xcf, 0xe4, 0x6b, 0x2f, 0x77, 0x59, 0xf4, 0x63,
      0x60, 0x59, 0xef, 0xf7, 0x02, 0xb6, 0xd7, 0xf0,
      0x0f, 0xda, 0x57, 0x6a, 0xda, 0xa3, 0xdd, 0x8e,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__LedControl_Response__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x24, 0xcf, 0x66, 0x1d, 0xea, 0x2f, 0x8a, 0x99,
      0x1a, 0xdd, 0xcb, 0x53, 0xaa, 0x3c, 0x15, 0x3a,
      0xcc, 0x62, 0x8c, 0xfb, 0x34, 0x63, 0x6f, 0x60,
      0x05, 0x50, 0xb6, 0xf1, 0x4d, 0x0e, 0x6c, 0xfc,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__LedControl_Event__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x59, 0x88, 0x75, 0xd1, 0xbe, 0x09, 0x6a, 0x0a,
      0xeb, 0x1d, 0x67, 0x1d, 0x4a, 0xf9, 0xce, 0x57,
      0x5c, 0x6b, 0x0e, 0xd2, 0xcd, 0xe1, 0x6e, 0x1a,
      0xf6, 0xa5, 0xc2, 0xce, 0x9f, 0xd2, 0x4f, 0x5e,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "service_msgs/msg/detail/service_event_info__functions.h"
#include "sl_vcu_all/msg/detail/channel_data__functions.h"
#include "builtin_interfaces/msg/detail/time__functions.h"
#include "sl_vcu_all/msg/detail/part_data__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t builtin_interfaces__msg__Time__EXPECTED_HASH = {1, {
    0xb1, 0x06, 0x23, 0x5e, 0x25, 0xa4, 0xc5, 0xed,
    0x35, 0x09, 0x8a, 0xa0, 0xa6, 0x1a, 0x3e, 0xe9,
    0xc9, 0xb1, 0x8d, 0x19, 0x7f, 0x39, 0x8b, 0x0e,
    0x42, 0x06, 0xce, 0xa9, 0xac, 0xf9, 0xc1, 0x97,
  }};
static const rosidl_type_hash_t service_msgs__msg__ServiceEventInfo__EXPECTED_HASH = {1, {
    0x41, 0xbc, 0xbb, 0xe0, 0x7a, 0x75, 0xc9, 0xb5,
    0x2b, 0xc9, 0x6b, 0xfd, 0x5c, 0x24, 0xd7, 0xf0,
    0xfc, 0x0a, 0x08, 0xc0, 0xcb, 0x79, 0x21, 0xb3,
    0x37, 0x3c, 0x57, 0x32, 0x34, 0x5a, 0x6f, 0x45,
  }};
static const rosidl_type_hash_t sl_vcu_all__msg__ChannelData__EXPECTED_HASH = {1, {
    0x2d, 0xd7, 0x3e, 0xf9, 0x32, 0xed, 0x81, 0xbc,
    0xdf, 0xb0, 0xbd, 0x1a, 0x4b, 0x34, 0x91, 0x7e,
    0x63, 0x92, 0x80, 0x25, 0x82, 0x33, 0x5c, 0x1c,
    0xb5, 0xeb, 0x17, 0xab, 0x89, 0xfa, 0x30, 0x6c,
  }};
static const rosidl_type_hash_t sl_vcu_all__msg__PartData__EXPECTED_HASH = {1, {
    0x44, 0xe0, 0x9f, 0x9f, 0x38, 0x47, 0x2e, 0x21,
    0x2f, 0xec, 0x22, 0xe7, 0x18, 0x2c, 0x21, 0x2d,
    0x97, 0xba, 0x1b, 0x65, 0x01, 0xc5, 0x0e, 0x89,
    0xfb, 0x31, 0x19, 0x9e, 0x90, 0x8f, 0x32, 0xc3,
  }};
#endif

static char sl_vcu_all__srv__LedControl__TYPE_NAME[] = "sl_vcu_all/srv/LedControl";
static char builtin_interfaces__msg__Time__TYPE_NAME[] = "builtin_interfaces/msg/Time";
static char service_msgs__msg__ServiceEventInfo__TYPE_NAME[] = "service_msgs/msg/ServiceEventInfo";
static char sl_vcu_all__msg__ChannelData__TYPE_NAME[] = "sl_vcu_all/msg/ChannelData";
static char sl_vcu_all__msg__PartData__TYPE_NAME[] = "sl_vcu_all/msg/PartData";
static char sl_vcu_all__srv__LedControl_Event__TYPE_NAME[] = "sl_vcu_all/srv/LedControl_Event";
static char sl_vcu_all__srv__LedControl_Request__TYPE_NAME[] = "sl_vcu_all/srv/LedControl_Request";
static char sl_vcu_all__srv__LedControl_Response__TYPE_NAME[] = "sl_vcu_all/srv/LedControl_Response";

// Define type names, field names, and default values
static char sl_vcu_all__srv__LedControl__FIELD_NAME__request_message[] = "request_message";
static char sl_vcu_all__srv__LedControl__FIELD_NAME__response_message[] = "response_message";
static char sl_vcu_all__srv__LedControl__FIELD_NAME__event_message[] = "event_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__LedControl__FIELDS[] = {
  {
    {sl_vcu_all__srv__LedControl__FIELD_NAME__request_message, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl__FIELD_NAME__response_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl__FIELD_NAME__event_message, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__LedControl_Event__TYPE_NAME, 31, 31},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__LedControl__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Event__TYPE_NAME, 31, 31},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__LedControl__get_type_description(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__LedControl__TYPE_NAME, 25, 25},
      {sl_vcu_all__srv__LedControl__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__LedControl__REFERENCED_TYPE_DESCRIPTIONS, 7, 7},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&sl_vcu_all__msg__ChannelData__EXPECTED_HASH, sl_vcu_all__msg__ChannelData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__msg__ChannelData__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&sl_vcu_all__msg__PartData__EXPECTED_HASH, sl_vcu_all__msg__PartData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__msg__PartData__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__srv__LedControl_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[5].fields = sl_vcu_all__srv__LedControl_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[6].fields = sl_vcu_all__srv__LedControl_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__LedControl_Request__FIELD_NAME__channels[] = "channels";
static char sl_vcu_all__srv__LedControl_Request__FIELD_NAME__sync_channels[] = "sync_channels";
static char sl_vcu_all__srv__LedControl_Request__FIELD_NAME__channel_data[] = "channel_data";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__LedControl_Request__FIELDS[] = {
  {
    {sl_vcu_all__srv__LedControl_Request__FIELD_NAME__channels, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8_UNBOUNDED_SEQUENCE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Request__FIELD_NAME__sync_channels, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Request__FIELD_NAME__channel_data, 12, 12},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_UNBOUNDED_SEQUENCE,
      0,
      0,
      {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__LedControl_Request__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__LedControl_Request__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
      {sl_vcu_all__srv__LedControl_Request__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__LedControl_Request__REFERENCED_TYPE_DESCRIPTIONS, 2, 2},
  };
  if (!constructed) {
    assert(0 == memcmp(&sl_vcu_all__msg__ChannelData__EXPECTED_HASH, sl_vcu_all__msg__ChannelData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = sl_vcu_all__msg__ChannelData__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&sl_vcu_all__msg__PartData__EXPECTED_HASH, sl_vcu_all__msg__PartData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = sl_vcu_all__msg__PartData__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__LedControl_Response__FIELD_NAME__success[] = "success";
static char sl_vcu_all__srv__LedControl_Response__FIELD_NAME__message[] = "message";
static char sl_vcu_all__srv__LedControl_Response__FIELD_NAME__affected_channels[] = "affected_channels";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__LedControl_Response__FIELDS[] = {
  {
    {sl_vcu_all__srv__LedControl_Response__FIELD_NAME__success, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Response__FIELD_NAME__message, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Response__FIELD_NAME__affected_channels, 17, 17},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8_UNBOUNDED_SEQUENCE,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__LedControl_Response__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
      {sl_vcu_all__srv__LedControl_Response__FIELDS, 3, 3},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__LedControl_Event__FIELD_NAME__info[] = "info";
static char sl_vcu_all__srv__LedControl_Event__FIELD_NAME__request[] = "request";
static char sl_vcu_all__srv__LedControl_Event__FIELD_NAME__response[] = "response";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__LedControl_Event__FIELDS[] = {
  {
    {sl_vcu_all__srv__LedControl_Event__FIELD_NAME__info, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Event__FIELD_NAME__request, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Event__FIELD_NAME__response, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__LedControl_Event__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__ChannelData__TYPE_NAME, 26, 26},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__msg__PartData__TYPE_NAME, 23, 23},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__LedControl_Event__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__LedControl_Event__TYPE_NAME, 31, 31},
      {sl_vcu_all__srv__LedControl_Event__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__LedControl_Event__REFERENCED_TYPE_DESCRIPTIONS, 6, 6},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&sl_vcu_all__msg__ChannelData__EXPECTED_HASH, sl_vcu_all__msg__ChannelData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__msg__ChannelData__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&sl_vcu_all__msg__PartData__EXPECTED_HASH, sl_vcu_all__msg__PartData__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__msg__PartData__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__srv__LedControl_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[5].fields = sl_vcu_all__srv__LedControl_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# LED Display Control Service Definition\n"
  "# Request\n"
  "\n"
  "# Channel selection - can specify multiple channels,\n"
  "# channels: IDs (0-based indexing), so far up to 2 channels are supported,\n"
  "# sync_channels: If true, all channels only use the first channel_data,\n"
  "#                then sends to hardware simultaneously\n"
  "\n"
  "# ChannelData structure, refer to ChannelData.msg for details\n"
  "# each channel_data has the following fields\n"
  "# ##############\n"
  "#    # Part selection and synchronization\n"
  "#    uint8[] parts                  # Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)\n"
  "#    bool sync_parts                # Whether to synchronize parts within this channel\n"
  "\n"
  "#    # Part data array\n"
  "#    PartData[] parts_data          # Data for each part (size should match parts array or 1 if sync_parts=true)\n"
  "# ###############\n"
  "\n"
  "\n"
  "# PartData structure, refer to PartData.msg for details\n"
  "# ##############\n"
  "#    # Lighting mode (required)\n"
  "#    string mode                    # \"off\", \"on\", \"breathing\", \"flashing\", \"marquee\"\n"
  "\n"
  "#    # Color brightness settings (optional, defaults applied if not set)\n"
  "#    uint8 green_brightness         # Green brightness (0-255)\n"
  "#    uint8 red_brightness           # Red brightness (0-255)\n"
  "#    uint8 blue_brightness          # Blue brightness (0-255)\n"
  "\n"
  "#    # Effect parameters (optional, defaults applied if not set)\n"
  "#    float32 frequency              # Frequency for breathing/flashing effects (Hz)\n"
  "#    float32 speed                  # Speed for marquee effect (pixels/second)\n"
  "#    bool marquee_direction         # Direction for marquee: true=forward, false=reverse\n"
  "#    float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)\n"
  "# ##############\n"
  "\n"
  "\n"
  "uint8[] channels               # Array of channel IDs (0-based indexing)\n"
  "bool sync_channels             # Whether to synchronize all specified channels\n"
  "ChannelData[] channel_data     # Array of channel data, each containing part data\n"
  "\n"
  "---\n"
  "# Response\n"
  "bool success                   # Whether the command was executed successfully\n"
  "string message                 # Success or error message\n"
  "uint8[] affected_channels      # Channels that were actually affected";

static char srv_encoding[] = "srv";
static char implicit_encoding[] = "implicit";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__LedControl__get_individual_type_description_source(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__LedControl__TYPE_NAME, 25, 25},
    {srv_encoding, 3, 3},
    {toplevel_type_raw_source, 2162, 2162},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__LedControl_Request__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__LedControl_Request__TYPE_NAME, 33, 33},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__LedControl_Response__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__LedControl_Response__TYPE_NAME, 34, 34},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__LedControl_Event__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__LedControl_Event__TYPE_NAME, 31, 31},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__LedControl__get_type_description_sources(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[8];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 8, 8};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__LedControl__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__msg__ChannelData__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__msg__PartData__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__srv__LedControl_Event__get_individual_type_description_source(NULL);
    sources[6] = *sl_vcu_all__srv__LedControl_Request__get_individual_type_description_source(NULL);
    sources[7] = *sl_vcu_all__srv__LedControl_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__LedControl_Request__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[3];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 3, 3};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__LedControl_Request__get_individual_type_description_source(NULL),
    sources[1] = *sl_vcu_all__msg__ChannelData__get_individual_type_description_source(NULL);
    sources[2] = *sl_vcu_all__msg__PartData__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__LedControl_Response__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__LedControl_Response__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__LedControl_Event__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[7];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 7, 7};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__LedControl_Event__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__msg__ChannelData__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__msg__PartData__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__srv__LedControl_Request__get_individual_type_description_source(NULL);
    sources[6] = *sl_vcu_all__srv__LedControl_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
