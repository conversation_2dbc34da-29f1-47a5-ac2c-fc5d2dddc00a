// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:srv/LedControl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/srv/led_control.h"


#ifndef SL_VCU_ALL__SRV__DETAIL__LED_CONTROL__STRUCT_H_
#define SL_VCU_ALL__SRV__DETAIL__LED_CONTROL__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'channels'
#include "rosidl_runtime_c/primitives_sequence.h"
// Member 'channel_data'
#include "sl_vcu_all/msg/detail/channel_data__struct.h"

/// Struct defined in srv/LedControl in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__LedControl_Request
{
  /// Channel selection - can specify multiple channels,
  /// channels: IDs (0-based indexing), so far up to 2 channels are supported,
  /// sync_channels: If true, all channels only use the first channel_data,
  ///                then sends to hardware simultaneously
  /// ChannelData structure, refer to ChannelData.msg for details
  /// each channel_data has the following fields
  /// ##############
  ///    # Part selection and synchronization
  ///    uint8[] parts                  # Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)
  ///    bool sync_parts                # Whether to synchronize parts within this channel
  ///    # Part data array
  ///    PartData[] parts_data          # Data for each part (size should match parts array or 1 if sync_parts=true)
  /// ###############
  /// PartData structure, refer to PartData.msg for details
  /// ##############
  ///    # Lighting mode (required)
  ///    string mode                    # "off", "on", "breathing", "flashing", "marquee"
  ///    # Color brightness settings (optional, defaults applied if not set)
  ///    uint8 green_brightness         # Green brightness (0-255)
  ///    uint8 red_brightness           # Red brightness (0-255)
  ///    uint8 blue_brightness          # Blue brightness (0-255)
  ///    # Effect parameters (optional, defaults applied if not set)
  ///    float32 frequency              # Frequency for breathing/flashing effects (Hz)
  ///    float32 speed                  # Speed for marquee effect (pixels/second)
  ///    bool marquee_direction         # Direction for marquee: true=forward, false=reverse
  ///    float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)
  /// ##############
  /// Array of channel IDs (0-based indexing)
  rosidl_runtime_c__uint8__Sequence channels;
  /// Whether to synchronize all specified channels
  bool sync_channels;
  /// Array of channel data, each containing part data
  sl_vcu_all__msg__ChannelData__Sequence channel_data;
} sl_vcu_all__srv__LedControl_Request;

// Struct for a sequence of sl_vcu_all__srv__LedControl_Request.
typedef struct sl_vcu_all__srv__LedControl_Request__Sequence
{
  sl_vcu_all__srv__LedControl_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__LedControl_Request__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'message'
#include "rosidl_runtime_c/string.h"
// Member 'affected_channels'
// already included above
// #include "rosidl_runtime_c/primitives_sequence.h"

/// Struct defined in srv/LedControl in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__LedControl_Response
{
  /// Whether the command was executed successfully
  bool success;
  /// Success or error message
  rosidl_runtime_c__String message;
  /// Channels that were actually affected
  rosidl_runtime_c__uint8__Sequence affected_channels;
} sl_vcu_all__srv__LedControl_Response;

// Struct for a sequence of sl_vcu_all__srv__LedControl_Response.
typedef struct sl_vcu_all__srv__LedControl_Response__Sequence
{
  sl_vcu_all__srv__LedControl_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__LedControl_Response__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'info'
#include "service_msgs/msg/detail/service_event_info__struct.h"

// constants for array fields with an upper bound
// request
enum
{
  sl_vcu_all__srv__LedControl_Event__request__MAX_SIZE = 1
};
// response
enum
{
  sl_vcu_all__srv__LedControl_Event__response__MAX_SIZE = 1
};

/// Struct defined in srv/LedControl in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__LedControl_Event
{
  service_msgs__msg__ServiceEventInfo info;
  sl_vcu_all__srv__LedControl_Request__Sequence request;
  sl_vcu_all__srv__LedControl_Response__Sequence response;
} sl_vcu_all__srv__LedControl_Event;

// Struct for a sequence of sl_vcu_all__srv__LedControl_Event.
typedef struct sl_vcu_all__srv__LedControl_Event__Sequence
{
  sl_vcu_all__srv__LedControl_Event * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__LedControl_Event__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__SRV__DETAIL__LED_CONTROL__STRUCT_H_
