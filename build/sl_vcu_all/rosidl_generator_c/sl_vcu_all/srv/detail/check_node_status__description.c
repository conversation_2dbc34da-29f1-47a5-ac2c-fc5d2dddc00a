// generated from rosidl_generator_c/resource/idl__description.c.em
// with input from sl_vcu_all:srv/CheckNodeStatus.idl
// generated code does not contain a copyright notice

#include "sl_vcu_all/srv/detail/check_node_status__functions.h"

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__CheckNodeStatus__get_type_hash(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0xc4, 0x8c, 0x32, 0x26, 0xef, 0xd7, 0xb9, 0x1d,
      0x40, 0xd6, 0x5d, 0x66, 0x4b, 0xec, 0x07, 0xb0,
      0xaf, 0x71, 0x7c, 0x0d, 0xc8, 0x13, 0xca, 0xf7,
      0xa7, 0x35, 0x61, 0x46, 0xa6, 0x57, 0xc9, 0x21,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__CheckNodeStatus_Request__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x1c, 0xe7, 0x6d, 0xbb, 0xba, 0x9d, 0xa3, 0x57,
      0x92, 0x23, 0x5a, 0x5d, 0x57, 0xf4, 0x1d, 0x11,
      0x5f, 0x20, 0x37, 0x6f, 0xcb, 0x3e, 0x47, 0x81,
      0x2c, 0xf9, 0xc3, 0x16, 0x9f, 0x68, 0xea, 0x26,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__CheckNodeStatus_Response__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x63, 0x25, 0x05, 0x61, 0x8f, 0x26, 0xb3, 0x9a,
      0x92, 0x2e, 0xd5, 0x56, 0x20, 0x19, 0x7c, 0x1b,
      0xd1, 0x2a, 0x24, 0x24, 0x77, 0x04, 0x1c, 0x73,
      0x5c, 0xdd, 0x04, 0xe3, 0xac, 0xe4, 0x3d, 0xbf,
    }};
  return &hash;
}

ROSIDL_GENERATOR_C_PUBLIC_sl_vcu_all
const rosidl_type_hash_t *
sl_vcu_all__srv__CheckNodeStatus_Event__get_type_hash(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_type_hash_t hash = {1, {
      0x32, 0x40, 0xdf, 0xe2, 0x16, 0x6b, 0x67, 0x79,
      0x66, 0xcf, 0xb9, 0xb7, 0xc1, 0x1c, 0xfb, 0x3b,
      0x23, 0x6b, 0x8e, 0x99, 0x4b, 0xb8, 0x2c, 0x34,
      0x96, 0xcf, 0xb1, 0x21, 0x49, 0x32, 0xb9, 0x4e,
    }};
  return &hash;
}

#include <assert.h>
#include <string.h>

// Include directives for referenced types
#include "service_msgs/msg/detail/service_event_info__functions.h"
#include "builtin_interfaces/msg/detail/time__functions.h"

// Hashes for external referenced types
#ifndef NDEBUG
static const rosidl_type_hash_t builtin_interfaces__msg__Time__EXPECTED_HASH = {1, {
    0xb1, 0x06, 0x23, 0x5e, 0x25, 0xa4, 0xc5, 0xed,
    0x35, 0x09, 0x8a, 0xa0, 0xa6, 0x1a, 0x3e, 0xe9,
    0xc9, 0xb1, 0x8d, 0x19, 0x7f, 0x39, 0x8b, 0x0e,
    0x42, 0x06, 0xce, 0xa9, 0xac, 0xf9, 0xc1, 0x97,
  }};
static const rosidl_type_hash_t service_msgs__msg__ServiceEventInfo__EXPECTED_HASH = {1, {
    0x41, 0xbc, 0xbb, 0xe0, 0x7a, 0x75, 0xc9, 0xb5,
    0x2b, 0xc9, 0x6b, 0xfd, 0x5c, 0x24, 0xd7, 0xf0,
    0xfc, 0x0a, 0x08, 0xc0, 0xcb, 0x79, 0x21, 0xb3,
    0x37, 0x3c, 0x57, 0x32, 0x34, 0x5a, 0x6f, 0x45,
  }};
#endif

static char sl_vcu_all__srv__CheckNodeStatus__TYPE_NAME[] = "sl_vcu_all/srv/CheckNodeStatus";
static char builtin_interfaces__msg__Time__TYPE_NAME[] = "builtin_interfaces/msg/Time";
static char service_msgs__msg__ServiceEventInfo__TYPE_NAME[] = "service_msgs/msg/ServiceEventInfo";
static char sl_vcu_all__srv__CheckNodeStatus_Event__TYPE_NAME[] = "sl_vcu_all/srv/CheckNodeStatus_Event";
static char sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME[] = "sl_vcu_all/srv/CheckNodeStatus_Request";
static char sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME[] = "sl_vcu_all/srv/CheckNodeStatus_Response";

// Define type names, field names, and default values
static char sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__request_message[] = "request_message";
static char sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__response_message[] = "response_message";
static char sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__event_message[] = "event_message";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__CheckNodeStatus__FIELDS[] = {
  {
    {sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__request_message, 15, 15},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__response_message, 16, 16},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus__FIELD_NAME__event_message, 13, 13},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {sl_vcu_all__srv__CheckNodeStatus_Event__TYPE_NAME, 36, 36},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__CheckNodeStatus__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Event__TYPE_NAME, 36, 36},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__CheckNodeStatus__get_type_description(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__CheckNodeStatus__TYPE_NAME, 30, 30},
      {sl_vcu_all__srv__CheckNodeStatus__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__CheckNodeStatus__REFERENCED_TYPE_DESCRIPTIONS, 5, 5},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__srv__CheckNodeStatus_Event__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__srv__CheckNodeStatus_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[4].fields = sl_vcu_all__srv__CheckNodeStatus_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__CheckNodeStatus_Request__FIELD_NAME__structure_needs_at_least_one_member[] = "structure_needs_at_least_one_member";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__CheckNodeStatus_Request__FIELDS[] = {
  {
    {sl_vcu_all__srv__CheckNodeStatus_Request__FIELD_NAME__structure_needs_at_least_one_member, 35, 35},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_UINT8,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__CheckNodeStatus_Request__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
      {sl_vcu_all__srv__CheckNodeStatus_Request__FIELDS, 1, 1},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__CheckNodeStatus_Response__FIELD_NAME__is_ready[] = "is_ready";
static char sl_vcu_all__srv__CheckNodeStatus_Response__FIELD_NAME__status[] = "status";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__CheckNodeStatus_Response__FIELDS[] = {
  {
    {sl_vcu_all__srv__CheckNodeStatus_Response__FIELD_NAME__is_ready, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_BOOLEAN,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Response__FIELD_NAME__status, 6, 6},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_STRING,
      0,
      0,
      {NULL, 0, 0},
    },
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__CheckNodeStatus_Response__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
      {sl_vcu_all__srv__CheckNodeStatus_Response__FIELDS, 2, 2},
    },
    {NULL, 0, 0},
  };
  if (!constructed) {
    constructed = true;
  }
  return &description;
}
// Define type names, field names, and default values
static char sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__info[] = "info";
static char sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__request[] = "request";
static char sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__response[] = "response";

static rosidl_runtime_c__type_description__Field sl_vcu_all__srv__CheckNodeStatus_Event__FIELDS[] = {
  {
    {sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__info, 4, 4},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE,
      0,
      0,
      {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__request, 7, 7},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
    },
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Event__FIELD_NAME__response, 8, 8},
    {
      rosidl_runtime_c__type_description__FieldType__FIELD_TYPE_NESTED_TYPE_BOUNDED_SEQUENCE,
      1,
      0,
      {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
    },
    {NULL, 0, 0},
  },
};

static rosidl_runtime_c__type_description__IndividualTypeDescription sl_vcu_all__srv__CheckNodeStatus_Event__REFERENCED_TYPE_DESCRIPTIONS[] = {
  {
    {builtin_interfaces__msg__Time__TYPE_NAME, 27, 27},
    {NULL, 0, 0},
  },
  {
    {service_msgs__msg__ServiceEventInfo__TYPE_NAME, 33, 33},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
    {NULL, 0, 0},
  },
  {
    {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
    {NULL, 0, 0},
  },
};

const rosidl_runtime_c__type_description__TypeDescription *
sl_vcu_all__srv__CheckNodeStatus_Event__get_type_description(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static bool constructed = false;
  static const rosidl_runtime_c__type_description__TypeDescription description = {
    {
      {sl_vcu_all__srv__CheckNodeStatus_Event__TYPE_NAME, 36, 36},
      {sl_vcu_all__srv__CheckNodeStatus_Event__FIELDS, 3, 3},
    },
    {sl_vcu_all__srv__CheckNodeStatus_Event__REFERENCED_TYPE_DESCRIPTIONS, 4, 4},
  };
  if (!constructed) {
    assert(0 == memcmp(&builtin_interfaces__msg__Time__EXPECTED_HASH, builtin_interfaces__msg__Time__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[0].fields = builtin_interfaces__msg__Time__get_type_description(NULL)->type_description.fields;
    assert(0 == memcmp(&service_msgs__msg__ServiceEventInfo__EXPECTED_HASH, service_msgs__msg__ServiceEventInfo__get_type_hash(NULL), sizeof(rosidl_type_hash_t)));
    description.referenced_type_descriptions.data[1].fields = service_msgs__msg__ServiceEventInfo__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[2].fields = sl_vcu_all__srv__CheckNodeStatus_Request__get_type_description(NULL)->type_description.fields;
    description.referenced_type_descriptions.data[3].fields = sl_vcu_all__srv__CheckNodeStatus_Response__get_type_description(NULL)->type_description.fields;
    constructed = true;
  }
  return &description;
}

static char toplevel_type_raw_source[] =
  "# Request - empty\n"
  "---\n"
  "# Response\n"
  "bool is_ready      # Whether the node is ready to process messages\n"
  "string status      # Status message or error description ";

static char srv_encoding[] = "srv";
static char implicit_encoding[] = "implicit";

// Define all individual source functions

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__CheckNodeStatus__get_individual_type_description_source(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__CheckNodeStatus__TYPE_NAME, 30, 30},
    {srv_encoding, 3, 3},
    {toplevel_type_raw_source, 157, 157},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__CheckNodeStatus_Request__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__CheckNodeStatus_Request__TYPE_NAME, 38, 38},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__CheckNodeStatus_Response__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__CheckNodeStatus_Response__TYPE_NAME, 39, 39},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource *
sl_vcu_all__srv__CheckNodeStatus_Event__get_individual_type_description_source(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static const rosidl_runtime_c__type_description__TypeSource source = {
    {sl_vcu_all__srv__CheckNodeStatus_Event__TYPE_NAME, 36, 36},
    {implicit_encoding, 8, 8},
    {NULL, 0, 0},
  };
  return &source;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__CheckNodeStatus__get_type_description_sources(
  const rosidl_service_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[6];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 6, 6};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__CheckNodeStatus__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__srv__CheckNodeStatus_Event__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__srv__CheckNodeStatus_Request__get_individual_type_description_source(NULL);
    sources[5] = *sl_vcu_all__srv__CheckNodeStatus_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__CheckNodeStatus_Request__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__CheckNodeStatus_Request__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__CheckNodeStatus_Response__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[1];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 1, 1};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__CheckNodeStatus_Response__get_individual_type_description_source(NULL),
    constructed = true;
  }
  return &source_sequence;
}

const rosidl_runtime_c__type_description__TypeSource__Sequence *
sl_vcu_all__srv__CheckNodeStatus_Event__get_type_description_sources(
  const rosidl_message_type_support_t * type_support)
{
  (void)type_support;
  static rosidl_runtime_c__type_description__TypeSource sources[5];
  static const rosidl_runtime_c__type_description__TypeSource__Sequence source_sequence = {sources, 5, 5};
  static bool constructed = false;
  if (!constructed) {
    sources[0] = *sl_vcu_all__srv__CheckNodeStatus_Event__get_individual_type_description_source(NULL),
    sources[1] = *builtin_interfaces__msg__Time__get_individual_type_description_source(NULL);
    sources[2] = *service_msgs__msg__ServiceEventInfo__get_individual_type_description_source(NULL);
    sources[3] = *sl_vcu_all__srv__CheckNodeStatus_Request__get_individual_type_description_source(NULL);
    sources[4] = *sl_vcu_all__srv__CheckNodeStatus_Response__get_individual_type_description_source(NULL);
    constructed = true;
  }
  return &source_sequence;
}
