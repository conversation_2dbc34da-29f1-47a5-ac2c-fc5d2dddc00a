// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:srv/CheckNodeStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/srv/check_node_status.h"


#ifndef SL_VCU_ALL__SRV__DETAIL__CHECK_NODE_STATUS__STRUCT_H_
#define SL_VCU_ALL__SRV__DETAIL__CHECK_NODE_STATUS__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in srv/CheckNodeStatus in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Request
{
  uint8_t structure_needs_at_least_one_member;
} sl_vcu_all__srv__CheckNodeStatus_Request;

// Struct for a sequence of sl_vcu_all__srv__CheckNodeStatus_Request.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Request__Sequence
{
  sl_vcu_all__srv__CheckNodeStatus_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__CheckNodeStatus_Request__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'status'
#include "rosidl_runtime_c/string.h"

/// Struct defined in srv/CheckNodeStatus in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Response
{
  /// Whether the node is ready to process messages
  bool is_ready;
  /// Status message or error description
  rosidl_runtime_c__String status;
} sl_vcu_all__srv__CheckNodeStatus_Response;

// Struct for a sequence of sl_vcu_all__srv__CheckNodeStatus_Response.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Response__Sequence
{
  sl_vcu_all__srv__CheckNodeStatus_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__CheckNodeStatus_Response__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'info'
#include "service_msgs/msg/detail/service_event_info__struct.h"

// constants for array fields with an upper bound
// request
enum
{
  sl_vcu_all__srv__CheckNodeStatus_Event__request__MAX_SIZE = 1
};
// response
enum
{
  sl_vcu_all__srv__CheckNodeStatus_Event__response__MAX_SIZE = 1
};

/// Struct defined in srv/CheckNodeStatus in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Event
{
  service_msgs__msg__ServiceEventInfo info;
  sl_vcu_all__srv__CheckNodeStatus_Request__Sequence request;
  sl_vcu_all__srv__CheckNodeStatus_Response__Sequence response;
} sl_vcu_all__srv__CheckNodeStatus_Event;

// Struct for a sequence of sl_vcu_all__srv__CheckNodeStatus_Event.
typedef struct sl_vcu_all__srv__CheckNodeStatus_Event__Sequence
{
  sl_vcu_all__srv__CheckNodeStatus_Event * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__CheckNodeStatus_Event__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__SRV__DETAIL__CHECK_NODE_STATUS__STRUCT_H_
