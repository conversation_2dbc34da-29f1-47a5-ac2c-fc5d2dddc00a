// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from sl_vcu_all:srv/AddCanFilter.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/srv/add_can_filter.h"


#ifndef SL_VCU_ALL__SRV__DETAIL__ADD_CAN_FILTER__STRUCT_H_
#define SL_VCU_ALL__SRV__DETAIL__ADD_CAN_FILTER__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'topic_name'
#include "rosidl_runtime_c/string.h"

/// Struct defined in srv/AddCanFilter in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__AddCanFilter_Request
{
  /// CAN ID to filter
  uint32_t can_id;
  /// Topic name to publish messages for this CAN ID
  rosidl_runtime_c__String topic_name;
} sl_vcu_all__srv__AddCanFilter_Request;

// Struct for a sequence of sl_vcu_all__srv__AddCanFilter_Request.
typedef struct sl_vcu_all__srv__AddCanFilter_Request__Sequence
{
  sl_vcu_all__srv__AddCanFilter_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__AddCanFilter_Request__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'message'
// already included above
// #include "rosidl_runtime_c/string.h"

/// Struct defined in srv/AddCanFilter in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__AddCanFilter_Response
{
  /// Whether the filter was added successfully
  bool success;
  /// Success or error message
  rosidl_runtime_c__String message;
} sl_vcu_all__srv__AddCanFilter_Response;

// Struct for a sequence of sl_vcu_all__srv__AddCanFilter_Response.
typedef struct sl_vcu_all__srv__AddCanFilter_Response__Sequence
{
  sl_vcu_all__srv__AddCanFilter_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__AddCanFilter_Response__Sequence;

// Constants defined in the message

// Include directives for member types
// Member 'info'
#include "service_msgs/msg/detail/service_event_info__struct.h"

// constants for array fields with an upper bound
// request
enum
{
  sl_vcu_all__srv__AddCanFilter_Event__request__MAX_SIZE = 1
};
// response
enum
{
  sl_vcu_all__srv__AddCanFilter_Event__response__MAX_SIZE = 1
};

/// Struct defined in srv/AddCanFilter in the package sl_vcu_all.
typedef struct sl_vcu_all__srv__AddCanFilter_Event
{
  service_msgs__msg__ServiceEventInfo info;
  sl_vcu_all__srv__AddCanFilter_Request__Sequence request;
  sl_vcu_all__srv__AddCanFilter_Response__Sequence response;
} sl_vcu_all__srv__AddCanFilter_Event;

// Struct for a sequence of sl_vcu_all__srv__AddCanFilter_Event.
typedef struct sl_vcu_all__srv__AddCanFilter_Event__Sequence
{
  sl_vcu_all__srv__AddCanFilter_Event * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} sl_vcu_all__srv__AddCanFilter_Event__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__SRV__DETAIL__ADD_CAN_FILTER__STRUCT_H_
