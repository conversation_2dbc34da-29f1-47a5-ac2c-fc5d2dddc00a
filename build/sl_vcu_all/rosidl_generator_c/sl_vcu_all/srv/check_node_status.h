// generated from rosidl_generator_c/resource/idl.h.em
// with input from sl_vcu_all:srv/CheckNodeStatus.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__SRV__CHECK_NODE_STATUS_H_
#define SL_VCU_ALL__SRV__CHECK_NODE_STATUS_H_

#include "sl_vcu_all/srv/detail/check_node_status__struct.h"
#include "sl_vcu_all/srv/detail/check_node_status__functions.h"
#include "sl_vcu_all/srv/detail/check_node_status__type_support.h"

#endif  // SL_VCU_ALL__SRV__CHECK_NODE_STATUS_H_
