# generated from rosidl_generator_py/resource/_idl.py.em
# with input from sl_vcu_all:msg/PartData.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_PartData(type):
    """Metaclass of message 'PartData'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('sl_vcu_all')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'sl_vcu_all.msg.PartData')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__part_data
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__part_data
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__part_data
            cls._TYPE_SUPPORT = module.type_support_msg__msg__part_data
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__part_data

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class PartData(metaclass=Metaclass_PartData):
    """Message class 'PartData'."""

    __slots__ = [
        '_mode',
        '_green_brightness',
        '_red_brightness',
        '_blue_brightness',
        '_frequency',
        '_speed',
        '_marquee_direction',
        '_on_time_duty',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'mode': 'string',
        'green_brightness': 'uint8',
        'red_brightness': 'uint8',
        'blue_brightness': 'uint8',
        'frequency': 'float',
        'speed': 'float',
        'marquee_direction': 'boolean',
        'on_time_duty': 'float',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.BasicType('boolean'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.mode = kwargs.get('mode', str())
        self.green_brightness = kwargs.get('green_brightness', int())
        self.red_brightness = kwargs.get('red_brightness', int())
        self.blue_brightness = kwargs.get('blue_brightness', int())
        self.frequency = kwargs.get('frequency', float())
        self.speed = kwargs.get('speed', float())
        self.marquee_direction = kwargs.get('marquee_direction', bool())
        self.on_time_duty = kwargs.get('on_time_duty', float())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.mode != other.mode:
            return False
        if self.green_brightness != other.green_brightness:
            return False
        if self.red_brightness != other.red_brightness:
            return False
        if self.blue_brightness != other.blue_brightness:
            return False
        if self.frequency != other.frequency:
            return False
        if self.speed != other.speed:
            return False
        if self.marquee_direction != other.marquee_direction:
            return False
        if self.on_time_duty != other.on_time_duty:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def mode(self):
        """Message field 'mode'."""
        return self._mode

    @mode.setter
    def mode(self, value):
        if self._check_fields:
            assert \
                isinstance(value, str), \
                "The 'mode' field must be of type 'str'"
        self._mode = value

    @builtins.property
    def green_brightness(self):
        """Message field 'green_brightness'."""
        return self._green_brightness

    @green_brightness.setter
    def green_brightness(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'green_brightness' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'green_brightness' field must be an unsigned integer in [0, 255]"
        self._green_brightness = value

    @builtins.property
    def red_brightness(self):
        """Message field 'red_brightness'."""
        return self._red_brightness

    @red_brightness.setter
    def red_brightness(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'red_brightness' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'red_brightness' field must be an unsigned integer in [0, 255]"
        self._red_brightness = value

    @builtins.property
    def blue_brightness(self):
        """Message field 'blue_brightness'."""
        return self._blue_brightness

    @blue_brightness.setter
    def blue_brightness(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'blue_brightness' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'blue_brightness' field must be an unsigned integer in [0, 255]"
        self._blue_brightness = value

    @builtins.property
    def frequency(self):
        """Message field 'frequency'."""
        return self._frequency

    @frequency.setter
    def frequency(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'frequency' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'frequency' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._frequency = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'speed' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'speed' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._speed = value

    @builtins.property
    def marquee_direction(self):
        """Message field 'marquee_direction'."""
        return self._marquee_direction

    @marquee_direction.setter
    def marquee_direction(self, value):
        if self._check_fields:
            assert \
                isinstance(value, bool), \
                "The 'marquee_direction' field must be of type 'bool'"
        self._marquee_direction = value

    @builtins.property
    def on_time_duty(self):
        """Message field 'on_time_duty'."""
        return self._on_time_duty

    @on_time_duty.setter
    def on_time_duty(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'on_time_duty' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'on_time_duty' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._on_time_duty = value
