# generated from rosidl_generator_py/resource/_idl.py.em
# with input from sl_vcu_all:msg/MotorInfo.idl
# generated code does not contain a copyright notice

# This is being done at the module level and not on the instance level to avoid looking
# for the same variable multiple times on each instance. This variable is not supposed to
# change during runtime so it makes sense to only look for it once.
from os import getenv

ros_python_check_fields = getenv('ROS_PYTHON_CHECK_FIELDS', default='')


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_MotorInfo(type):
    """Metaclass of message 'MotorInfo'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('sl_vcu_all')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'sl_vcu_all.msg.MotorInfo')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__motor_info
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__motor_info
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__motor_info
            cls._TYPE_SUPPORT = module.type_support_msg__msg__motor_info
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__motor_info

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class MotorInfo(metaclass=Metaclass_MotorInfo):
    """Message class 'MotorInfo'."""

    __slots__ = [
        '_header',
        '_left_current',
        '_right_current',
        '_left_temp',
        '_right_temp',
        '_driver_temp',
        '_left_pos_encoder',
        '_right_pos_encoder',
        '_alarm_code',
        '_check_fields',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'left_current': 'double',
        'right_current': 'double',
        'left_temp': 'double',
        'right_temp': 'double',
        'driver_temp': 'double',
        'left_pos_encoder': 'int32',
        'right_pos_encoder': 'int32',
        'alarm_code': 'uint32',
    }

    # This attribute is used to store an rosidl_parser.definition variable
    # related to the data type of each of the components the message.
    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        if 'check_fields' in kwargs:
            self._check_fields = kwargs['check_fields']
        else:
            self._check_fields = ros_python_check_fields == '1'
        if self._check_fields:
            assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
                'Invalid arguments passed to constructor: %s' % \
                ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.left_current = kwargs.get('left_current', float())
        self.right_current = kwargs.get('right_current', float())
        self.left_temp = kwargs.get('left_temp', float())
        self.right_temp = kwargs.get('right_temp', float())
        self.driver_temp = kwargs.get('driver_temp', float())
        self.left_pos_encoder = kwargs.get('left_pos_encoder', int())
        self.right_pos_encoder = kwargs.get('right_pos_encoder', int())
        self.alarm_code = kwargs.get('alarm_code', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.get_fields_and_field_types().keys(), self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    if self._check_fields:
                        assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.left_current != other.left_current:
            return False
        if self.right_current != other.right_current:
            return False
        if self.left_temp != other.left_temp:
            return False
        if self.right_temp != other.right_temp:
            return False
        if self.driver_temp != other.driver_temp:
            return False
        if self.left_pos_encoder != other.left_pos_encoder:
            return False
        if self.right_pos_encoder != other.right_pos_encoder:
            return False
        if self.alarm_code != other.alarm_code:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if self._check_fields:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def left_current(self):
        """Message field 'left_current'."""
        return self._left_current

    @left_current.setter
    def left_current(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'left_current' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'left_current' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._left_current = value

    @builtins.property
    def right_current(self):
        """Message field 'right_current'."""
        return self._right_current

    @right_current.setter
    def right_current(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'right_current' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'right_current' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._right_current = value

    @builtins.property
    def left_temp(self):
        """Message field 'left_temp'."""
        return self._left_temp

    @left_temp.setter
    def left_temp(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'left_temp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'left_temp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._left_temp = value

    @builtins.property
    def right_temp(self):
        """Message field 'right_temp'."""
        return self._right_temp

    @right_temp.setter
    def right_temp(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'right_temp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'right_temp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._right_temp = value

    @builtins.property
    def driver_temp(self):
        """Message field 'driver_temp'."""
        return self._driver_temp

    @driver_temp.setter
    def driver_temp(self, value):
        if self._check_fields:
            assert \
                isinstance(value, float), \
                "The 'driver_temp' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'driver_temp' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._driver_temp = value

    @builtins.property
    def left_pos_encoder(self):
        """Message field 'left_pos_encoder'."""
        return self._left_pos_encoder

    @left_pos_encoder.setter
    def left_pos_encoder(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'left_pos_encoder' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'left_pos_encoder' field must be an integer in [-2147483648, 2147483647]"
        self._left_pos_encoder = value

    @builtins.property
    def right_pos_encoder(self):
        """Message field 'right_pos_encoder'."""
        return self._right_pos_encoder

    @right_pos_encoder.setter
    def right_pos_encoder(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'right_pos_encoder' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'right_pos_encoder' field must be an integer in [-2147483648, 2147483647]"
        self._right_pos_encoder = value

    @builtins.property
    def alarm_code(self):
        """Message field 'alarm_code'."""
        return self._alarm_code

    @alarm_code.setter
    def alarm_code(self, value):
        if self._check_fields:
            assert \
                isinstance(value, int), \
                "The 'alarm_code' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'alarm_code' field must be an unsigned integer in [0, 4294967295]"
        self._alarm_code = value
