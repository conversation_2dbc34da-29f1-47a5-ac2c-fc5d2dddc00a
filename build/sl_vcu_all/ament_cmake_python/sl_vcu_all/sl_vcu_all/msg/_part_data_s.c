// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "sl_vcu_all/msg/detail/part_data__struct.h"
#include "sl_vcu_all/msg/detail/part_data__functions.h"

#include "rosidl_runtime_c/string.h"
#include "rosidl_runtime_c/string_functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool sl_vcu_all__msg__part_data__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[35];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("sl_vcu_all.msg._part_data.PartData", full_classname_dest, 34) == 0);
  }
  sl_vcu_all__msg__PartData * ros_message = _ros_message;
  {  // mode
    PyObject * field = PyObject_GetAttrString(_pymsg, "mode");
    if (!field) {
      return false;
    }
    assert(PyUnicode_Check(field));
    PyObject * encoded_field = PyUnicode_AsUTF8String(field);
    if (!encoded_field) {
      Py_DECREF(field);
      return false;
    }
    rosidl_runtime_c__String__assign(&ros_message->mode, PyBytes_AS_STRING(encoded_field));
    Py_DECREF(encoded_field);
    Py_DECREF(field);
  }
  {  // green_brightness
    PyObject * field = PyObject_GetAttrString(_pymsg, "green_brightness");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->green_brightness = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // red_brightness
    PyObject * field = PyObject_GetAttrString(_pymsg, "red_brightness");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->red_brightness = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // blue_brightness
    PyObject * field = PyObject_GetAttrString(_pymsg, "blue_brightness");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->blue_brightness = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // frequency
    PyObject * field = PyObject_GetAttrString(_pymsg, "frequency");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->frequency = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->speed = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // marquee_direction
    PyObject * field = PyObject_GetAttrString(_pymsg, "marquee_direction");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->marquee_direction = (Py_True == field);
    Py_DECREF(field);
  }
  {  // on_time_duty
    PyObject * field = PyObject_GetAttrString(_pymsg, "on_time_duty");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->on_time_duty = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * sl_vcu_all__msg__part_data__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of PartData */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("sl_vcu_all.msg._part_data");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "PartData");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  sl_vcu_all__msg__PartData * ros_message = (sl_vcu_all__msg__PartData *)raw_ros_message;
  {  // mode
    PyObject * field = NULL;
    field = PyUnicode_DecodeUTF8(
      ros_message->mode.data,
      strlen(ros_message->mode.data),
      "replace");
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "mode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // green_brightness
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->green_brightness);
    {
      int rc = PyObject_SetAttrString(_pymessage, "green_brightness", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // red_brightness
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->red_brightness);
    {
      int rc = PyObject_SetAttrString(_pymessage, "red_brightness", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // blue_brightness
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->blue_brightness);
    {
      int rc = PyObject_SetAttrString(_pymessage, "blue_brightness", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // frequency
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->frequency);
    {
      int rc = PyObject_SetAttrString(_pymessage, "frequency", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // marquee_direction
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->marquee_direction ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "marquee_direction", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // on_time_duty
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->on_time_duty);
    {
      int rc = PyObject_SetAttrString(_pymessage, "on_time_duty", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
