// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "sl_vcu_all/msg/detail/motor_info__struct.h"
#include "sl_vcu_all/msg/detail/motor_info__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool sl_vcu_all__msg__motor_info__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[37];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("sl_vcu_all.msg._motor_info.MotorInfo", full_classname_dest, 36) == 0);
  }
  sl_vcu_all__msg__MotorInfo * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // left_current
    PyObject * field = PyObject_GetAttrString(_pymsg, "left_current");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->left_current = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // right_current
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_current");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->right_current = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // left_temp
    PyObject * field = PyObject_GetAttrString(_pymsg, "left_temp");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->left_temp = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // right_temp
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_temp");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->right_temp = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // driver_temp
    PyObject * field = PyObject_GetAttrString(_pymsg, "driver_temp");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->driver_temp = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // left_pos_encoder
    PyObject * field = PyObject_GetAttrString(_pymsg, "left_pos_encoder");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->left_pos_encoder = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // right_pos_encoder
    PyObject * field = PyObject_GetAttrString(_pymsg, "right_pos_encoder");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->right_pos_encoder = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // alarm_code
    PyObject * field = PyObject_GetAttrString(_pymsg, "alarm_code");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->alarm_code = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * sl_vcu_all__msg__motor_info__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of MotorInfo */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("sl_vcu_all.msg._motor_info");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "MotorInfo");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  sl_vcu_all__msg__MotorInfo * ros_message = (sl_vcu_all__msg__MotorInfo *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // left_current
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->left_current);
    {
      int rc = PyObject_SetAttrString(_pymessage, "left_current", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_current
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->right_current);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_current", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // left_temp
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->left_temp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "left_temp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_temp
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->right_temp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_temp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // driver_temp
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->driver_temp);
    {
      int rc = PyObject_SetAttrString(_pymessage, "driver_temp", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // left_pos_encoder
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->left_pos_encoder);
    {
      int rc = PyObject_SetAttrString(_pymessage, "left_pos_encoder", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // right_pos_encoder
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->right_pos_encoder);
    {
      int rc = PyObject_SetAttrString(_pymessage, "right_pos_encoder", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // alarm_code
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->alarm_code);
    {
      int rc = PyObject_SetAttrString(_pymessage, "alarm_code", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
