{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-bbba720938b35a2cae99.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}, {"build": "sl_vcu_all__py", "jsonFile": "directory-sl_vcu_all__py-073fb6b357fa7b617102.json", "minimumCMakeVersion": {"string": "3.20"}, "parentIndex": 0, "projectIndex": 0, "source": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/sl_vcu_all__py", "targetIndexes": [10]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "sl_vcu_all", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_sl_vcu_all_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_sl_vcu_all_egg-0979b15e5b34e2d8a6c2.json", "name": "ament_cmake_python_build_sl_vcu_all_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_copy_sl_vcu_all::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_copy_sl_vcu_all-ff41cc6f2a5845806cf3.json", "name": "ament_cmake_python_copy_sl_vcu_all", "projectIndex": 0}, {"directoryIndex": 0, "id": "battery_monitor_node::@6890427a1f51a3e7e1df", "jsonFile": "target-battery_monitor_node-545b215a34d40b23ba01.json", "name": "battery_monitor_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "bumper_sensor_node::@6890427a1f51a3e7e1df", "jsonFile": "target-bumper_sensor_node-4a729abe1f2367e627e3.json", "name": "bumper_sensor_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "can_frame_dispatcher_node::@6890427a1f51a3e7e1df", "jsonFile": "target-can_frame_dispatcher_node-1f5a54d734d801daed9d.json", "name": "can_frame_dispatcher_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "imu_sensor_node::@6890427a1f51a3e7e1df", "jsonFile": "target-imu_sensor_node-0b8362b1393228b677ba.json", "name": "imu_sensor_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "jack_control_node::@6890427a1f51a3e7e1df", "jsonFile": "target-jack_control_node-a9327e5d83398e278c49.json", "name": "jack_control_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "led_display_control_node::@6890427a1f51a3e7e1df", "jsonFile": "target-led_display_control_node-0483165d42d58ed23ced.json", "name": "led_display_control_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all-4e9dbaf7e945191b234a.json", "name": "sl_vcu_all", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__cpp-f799da8d62ed2dcc7134.json", "name": "sl_vcu_all__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "sl_vcu_all__py::@1af264af66a505798f78", "jsonFile": "target-sl_vcu_all__py-1e7b42afb0d7d89dc275.json", "name": "sl_vcu_all__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_generator_c-6d2b7de6a7a1696c87ce.json", "name": "sl_vcu_all__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_generator_py-937db6a4af461b6d5847.json", "name": "sl_vcu_all__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_generator_type_description::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_generator_type_description-e38bbec9c40483d05f03.json", "name": "sl_vcu_all__rosidl_generator_type_description", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_c-9c13b1092e7035aad38b.json", "name": "sl_vcu_all__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_cpp-49a846e35c018c5a4e93.json", "name": "sl_vcu_all__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_fastrtps_c-9cd7be2314d092e3e0d0.json", "name": "sl_vcu_all__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_fastrtps_cpp-9a23fa32ae4a6fd7c070.json", "name": "sl_vcu_all__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_introspection_c-556055599c260e505f49.json", "name": "sl_vcu_all__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all__rosidl_typesupport_introspection_cpp-f8b8d7dc709fc15cced9.json", "name": "sl_vcu_all__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all_s__rosidl_typesupport_c-56d5d898c5f9fda9a904.json", "name": "sl_vcu_all_s__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all_s__rosidl_typesupport_fastrtps_c-edbf9efd561e8ed5a61a.json", "name": "sl_vcu_all_s__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all_s__rosidl_typesupport_introspection_c-b6748ec6b606a8614125.json", "name": "sl_vcu_all_s__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "sl_vcu_all_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-sl_vcu_all_uninstall-15df9681788639c356cd.json", "name": "sl_vcu_all_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "teleop_key::@6890427a1f51a3e7e1df", "jsonFile": "target-teleop_key-f45bbed1c178b25d8b2a.json", "name": "teleop_key", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-9cb1a629fe46e47362e6.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "zl_motor_controller_node::@6890427a1f51a3e7e1df", "jsonFile": "target-zl_motor_controller_node-35f2dd131ec6babf21d9.json", "name": "zl_motor_controller_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "zl_motor_modbus_controller_node::@6890427a1f51a3e7e1df", "jsonFile": "target-zl_motor_modbus_controller_node-a359d5053bec984df078.json", "name": "zl_motor_modbus_controller_node", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all", "source": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all"}, "version": {"major": 2, "minor": 6}}