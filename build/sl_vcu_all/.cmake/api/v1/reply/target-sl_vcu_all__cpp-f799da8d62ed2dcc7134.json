{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "add_dependencies"], "files": ["/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 38, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 130, "parent": 4}, {"command": 4, "file": 0, "line": 135, "parent": 4}]}, "dependencies": [{"backtrace": 6, "id": "sl_vcu_all__rosidl_generator_type_description::@6890427a1f51a3e7e1df"}], "id": "sl_vcu_all__cpp::@6890427a1f51a3e7e1df", "name": "sl_vcu_all__cpp", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/CMakeFiles/sl_vcu_all__cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/CMakeFiles/sl_vcu_all__cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_cpp/sl_vcu_all/srv/add_can_filter.hpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}