{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/sl_vcu_all__py/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}]}, "dependencies": [{"id": "sl_vcu_all::@6890427a1f51a3e7e1df"}], "id": "sl_vcu_all__py::@1af264af66a505798f78", "name": "sl_vcu_all__py", "paths": {"build": "sl_vcu_all__py", "source": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/sl_vcu_all__py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/sl_vcu_all__py/CMakeFiles/sl_vcu_all__py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/sl_vcu_all__py/CMakeFiles/sl_vcu_all__py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}