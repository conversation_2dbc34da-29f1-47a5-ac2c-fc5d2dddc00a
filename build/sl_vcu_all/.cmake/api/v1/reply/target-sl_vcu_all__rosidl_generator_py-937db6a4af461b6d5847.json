{"artifacts": [{"path": "libsl_vcu_all__rosidl_generator_py.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["/opt/ros/jazzy/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 38, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 140, "parent": 4}, {"command": 4, "file": 0, "line": 240, "parent": 4}, {"command": 5, "file": 0, "line": 166, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 7, "file": 3, "line": 11, "parent": 0}, {"file": 6, "parent": 14}, {"command": 1, "file": 6, "line": 41, "parent": 15}, {"file": 5, "parent": 16}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 4, "parent": 18}, {"command": 6, "file": 4, "line": 61, "parent": 19}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 7, "parent": 21}, {"command": 6, "file": 7, "line": 61, "parent": 22}, {"command": 7, "file": 3, "line": 10, "parent": 0}, {"file": 16, "parent": 24}, {"command": 1, "file": 16, "line": 41, "parent": 25}, {"file": 15, "parent": 26}, {"command": 7, "file": 15, "line": 21, "parent": 27}, {"file": 14, "parent": 28}, {"command": 1, "file": 14, "line": 41, "parent": 29}, {"file": 13, "parent": 30}, {"command": 7, "file": 13, "line": 21, "parent": 31}, {"file": 12, "parent": 32}, {"command": 1, "file": 12, "line": 41, "parent": 33}, {"file": 11, "parent": 34}, {"command": 7, "file": 11, "line": 21, "parent": 35}, {"file": 10, "parent": 36}, {"command": 1, "file": 10, "line": 41, "parent": 37}, {"file": 9, "parent": 38}, {"command": 1, "file": 9, "line": 9, "parent": 39}, {"file": 8, "parent": 40}, {"command": 6, "file": 8, "line": 61, "parent": 41}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 17, "parent": 43}, {"command": 6, "file": 17, "line": 61, "parent": 44}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 18, "parent": 46}, {"command": 6, "file": 18, "line": 61, "parent": 47}, {"command": 5, "file": 0, "line": 149, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 19, "parent": 50}, {"command": 5, "file": 19, "line": 170, "parent": 51}, {"command": 7, "file": 13, "line": 21, "parent": 31}, {"file": 24, "parent": 53}, {"command": 1, "file": 24, "line": 41, "parent": 54}, {"file": 23, "parent": 55}, {"command": 7, "file": 23, "line": 21, "parent": 56}, {"file": 22, "parent": 57}, {"command": 1, "file": 22, "line": 41, "parent": 58}, {"file": 21, "parent": 59}, {"command": 1, "file": 21, "line": 9, "parent": 60}, {"file": 20, "parent": 61}, {"command": 6, "file": 20, "line": 61, "parent": 62}, {"command": 8, "file": 0, "line": 143, "parent": 4}, {"command": 6, "file": 0, "line": 237, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 27, "parent": 66}, {"command": 7, "file": 27, "line": 21, "parent": 67}, {"file": 26, "parent": 68}, {"command": 1, "file": 26, "line": 41, "parent": 69}, {"file": 25, "parent": 70}, {"command": 9, "file": 25, "line": 25, "parent": 71}, {"command": 10, "file": 0, "line": 154, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 65, "fragment": "-Wall"}, {"backtrace": 65, "fragment": "-Wextra"}], "defines": [{"backtrace": 8, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 72, "define": "ROS_PACKAGE_NAME=\"sl_vcu_all\""}, {"define": "sl_vcu_all__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 73, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_c"}, {"backtrace": 73, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/action_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/jazzy/include/unique_identifier_msgs"}, {"backtrace": 49, "isSystem": true, "path": "/usr/lib/python3/dist-packages/numpy/core/include"}, {"backtrace": 49, "isSystem": true, "path": "/usr/include/python3.12"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "dependencies": [{"backtrace": 11, "id": "sl_vcu_all__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 64, "id": "sl_vcu_all__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 64, "id": "sl_vcu_all__py::@1af264af66a505798f78"}], "id": "sl_vcu_all__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "libsl_vcu_all__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "libsl_vcu_all__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.12.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 63, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "C"}, "name": "sl_vcu_all__rosidl_generator_py", "nameOnDisk": "libsl_vcu_all__rosidl_generator_py.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/srv/_add_can_filter_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/srv/_check_node_status_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/srv/_led_control_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_motor_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_motor_state_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_bumper_state_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_can_frame_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_battery_status_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_part_data_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_channel_data_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/msg/_jack_status_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_py/sl_vcu_all/action/_jack_control_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}