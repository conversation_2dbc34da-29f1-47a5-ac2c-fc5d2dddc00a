{"artifacts": [{"path": "jack_control_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "ament_execute_extensions", "rosidl_generate_interfaces", "add_dependencies", "add_compile_options", "add_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetools_exportExport.cmake", "/opt/ros/jazzy/share/tracetools/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetoolsConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclExport.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclConfig.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfacesConfig.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/export_action_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/action_msgsConfig.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/export_action_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/action_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/action_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_cExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/action_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/action_msgs/cmake/export_action_msgs__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 184, "parent": 0}, {"command": 1, "file": 0, "line": 217, "parent": 0}, {"command": 3, "file": 0, "line": 189, "parent": 0}, {"command": 2, "file": 1, "line": 146, "parent": 3}, {"command": 2, "file": 0, "line": 197, "parent": 0}, {"command": 6, "file": 0, "line": 10, "parent": 0}, {"file": 4, "parent": 6}, {"command": 5, "file": 4, "line": 41, "parent": 7}, {"file": 3, "parent": 8}, {"command": 5, "file": 3, "line": 9, "parent": 9}, {"file": 2, "parent": 10}, {"command": 4, "file": 2, "line": 61, "parent": 11}, {"command": 6, "file": 0, "line": 16, "parent": 0}, {"file": 9, "parent": 13}, {"command": 5, "file": 9, "line": 41, "parent": 14}, {"file": 8, "parent": 15}, {"command": 6, "file": 8, "line": 21, "parent": 16}, {"file": 7, "parent": 17}, {"command": 5, "file": 7, "line": 41, "parent": 18}, {"file": 6, "parent": 19}, {"command": 5, "file": 6, "line": 9, "parent": 20}, {"file": 5, "parent": 21}, {"command": 4, "file": 5, "line": 61, "parent": 22}, {"command": 5, "file": 4, "line": 41, "parent": 7}, {"file": 13, "parent": 24}, {"command": 6, "file": 13, "line": 21, "parent": 25}, {"file": 12, "parent": 26}, {"command": 5, "file": 12, "line": 41, "parent": 27}, {"file": 11, "parent": 28}, {"command": 5, "file": 11, "line": 9, "parent": 29}, {"file": 10, "parent": 30}, {"command": 4, "file": 10, "line": 61, "parent": 31}, {"command": 6, "file": 13, "line": 21, "parent": 25}, {"file": 18, "parent": 33}, {"command": 5, "file": 18, "line": 41, "parent": 34}, {"file": 17, "parent": 35}, {"command": 6, "file": 17, "line": 21, "parent": 36}, {"file": 16, "parent": 37}, {"command": 5, "file": 16, "line": 41, "parent": 38}, {"file": 15, "parent": 39}, {"command": 5, "file": 15, "line": 9, "parent": 40}, {"file": 14, "parent": 41}, {"command": 4, "file": 14, "line": 61, "parent": 42}, {"command": 5, "file": 16, "line": 41, "parent": 38}, {"file": 22, "parent": 44}, {"command": 6, "file": 22, "line": 21, "parent": 45}, {"file": 21, "parent": 46}, {"command": 5, "file": 21, "line": 41, "parent": 47}, {"file": 20, "parent": 48}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 19, "parent": 50}, {"command": 4, "file": 19, "line": 61, "parent": 51}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 23, "parent": 53}, {"command": 4, "file": 23, "line": 61, "parent": 54}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 24, "parent": 56}, {"command": 4, "file": 24, "line": 61, "parent": 57}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 25, "parent": 59}, {"command": 4, "file": 25, "line": 61, "parent": 60}, {"command": 5, "file": 7, "line": 41, "parent": 18}, {"file": 29, "parent": 62}, {"command": 6, "file": 29, "line": 21, "parent": 63}, {"file": 28, "parent": 64}, {"command": 5, "file": 28, "line": 41, "parent": 65}, {"file": 27, "parent": 66}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 26, "parent": 68}, {"command": 4, "file": 26, "line": 61, "parent": 69}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 30, "parent": 71}, {"command": 4, "file": 30, "line": 61, "parent": 72}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 31, "parent": 74}, {"command": 4, "file": 31, "line": 61, "parent": 75}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 32, "parent": 77}, {"command": 4, "file": 32, "line": 61, "parent": 78}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 33, "parent": 80}, {"command": 4, "file": 33, "line": 61, "parent": 81}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 34, "parent": 83}, {"command": 4, "file": 34, "line": 61, "parent": 84}, {"command": 6, "file": 0, "line": 11, "parent": 0}, {"file": 37, "parent": 86}, {"command": 5, "file": 37, "line": 41, "parent": 87}, {"file": 36, "parent": 88}, {"command": 5, "file": 36, "line": 9, "parent": 89}, {"file": 35, "parent": 90}, {"command": 4, "file": 35, "line": 61, "parent": 91}, {"command": 5, "file": 36, "line": 9, "parent": 89}, {"file": 38, "parent": 93}, {"command": 4, "file": 38, "line": 61, "parent": 94}, {"command": 5, "file": 36, "line": 9, "parent": 89}, {"file": 39, "parent": 96}, {"command": 4, "file": 39, "line": 61, "parent": 97}, {"command": 8, "file": 0, "line": 38, "parent": 0}, {"command": 7, "file": 42, "line": 280, "parent": 99}, {"command": 5, "file": 41, "line": 48, "parent": 100}, {"file": 40, "parent": 101}, {"command": 2, "file": 40, "line": 134, "parent": 102}, {"command": 2, "file": 40, "line": 146, "parent": 102}, {"command": 5, "file": 20, "line": 9, "parent": 49}, {"file": 43, "parent": 105}, {"command": 4, "file": 43, "line": 61, "parent": 106}, {"command": 2, "file": 40, "line": 146, "parent": 102}, {"command": 5, "file": 27, "line": 9, "parent": 67}, {"file": 44, "parent": 109}, {"command": 4, "file": 44, "line": 61, "parent": 110}, {"command": 6, "file": 13, "line": 21, "parent": 25}, {"file": 49, "parent": 112}, {"command": 5, "file": 49, "line": 41, "parent": 113}, {"file": 48, "parent": 114}, {"command": 6, "file": 48, "line": 21, "parent": 115}, {"file": 47, "parent": 116}, {"command": 5, "file": 47, "line": 41, "parent": 117}, {"file": 46, "parent": 118}, {"command": 5, "file": 46, "line": 9, "parent": 119}, {"file": 45, "parent": 120}, {"command": 4, "file": 45, "line": 61, "parent": 121}, {"command": 6, "file": 48, "line": 21, "parent": 115}, {"file": 54, "parent": 123}, {"command": 5, "file": 54, "line": 41, "parent": 124}, {"file": 53, "parent": 125}, {"command": 6, "file": 53, "line": 21, "parent": 126}, {"file": 52, "parent": 127}, {"command": 5, "file": 52, "line": 41, "parent": 128}, {"file": 51, "parent": 129}, {"command": 5, "file": 51, "line": 9, "parent": 130}, {"file": 50, "parent": 131}, {"command": 4, "file": 50, "line": 61, "parent": 132}, {"command": 5, "file": 41, "line": 48, "parent": 100}, {"file": 55, "parent": 134}, {"command": 9, "file": 55, "line": 164, "parent": 135}, {"command": 10, "file": 0, "line": 5, "parent": 0}, {"command": 5, "file": 41, "line": 48, "parent": 100}, {"file": 58, "parent": 138}, {"command": 6, "file": 58, "line": 21, "parent": 139}, {"file": 57, "parent": 140}, {"command": 5, "file": 57, "line": 41, "parent": 141}, {"file": 56, "parent": 142}, {"command": 11, "file": 56, "line": 25, "parent": 143}, {"command": 12, "file": 0, "line": 185, "parent": 0}, {"command": 12, "file": 1, "line": 142, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 137, "fragment": "-Wall"}, {"backtrace": 137, "fragment": "-Wextra"}, {"backtrace": 137, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 144, "define": "ROS_PACKAGE_NAME=\"sl_vcu_all\""}], "includes": [{"backtrace": 145, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all/include"}, {"backtrace": 5, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_c"}, {"backtrace": 5, "path": "/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all/rosidl_generator_cpp"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_action"}, {"backtrace": 146, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/type_description_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_action"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 5, "id": "sl_vcu_all__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 136, "id": "sl_vcu_all__cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "sl_vcu_all__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df"}], "id": "jack_control_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/sl_vcu_all"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/ros/jazzy/lib:/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "libsl_vcu_all__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 32, "fragment": "-llttng-ust", "role": "libraries"}, {"backtrace": 32, "fragment": "-llttng-ust-common", "role": "libraries"}, {"backtrace": 32, "fragment": "-rdynamic", "role": "libraries"}, {"backtrace": 32, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 79, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 98, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 103, "fragment": "libsl_vcu_all__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 104, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 107, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 122, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 133, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "CXX"}, "name": "jack_control_node", "nameOnDisk": "jack_control_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/jack_control_node.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}