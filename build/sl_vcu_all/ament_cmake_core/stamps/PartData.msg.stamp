# Part Data for LED Control
# This message defines the control data for a single part of an LED strip

# Lighting mode (required)
string mode                    # "off", "on", "breathing", "flashing", "marquee"

# Color brightness settings (optional, defaults applied if not set)
uint8 green_brightness         # Green brightness (0-255)
uint8 red_brightness           # Red brightness (0-255)
uint8 blue_brightness          # Blue brightness (0-255)

# Effect parameters (optional, defaults applied if not set)
float32 frequency              # Frequency for breathing/flashing effects (Hz)
float32 speed                  # Speed for marquee effect (pixels/second)
bool marquee_direction         # Direction for marquee: true=forward, false=reverse
float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)
