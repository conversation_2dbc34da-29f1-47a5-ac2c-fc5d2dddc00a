// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:action/JackControl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/action/jack_control.hpp"


#ifndef SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__BUILDER_HPP_
#define SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/action/detail/jack_control__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_Goal_speed
{
public:
  explicit Init_JackControl_Goal_speed(::sl_vcu_all::action::JackControl_Goal & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_Goal speed(::sl_vcu_all::action::JackControl_Goal::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Goal msg_;
};

class Init_JackControl_Goal_target_position
{
public:
  explicit Init_JackControl_Goal_target_position(::sl_vcu_all::action::JackControl_Goal & msg)
  : msg_(msg)
  {}
  Init_JackControl_Goal_speed target_position(::sl_vcu_all::action::JackControl_Goal::_target_position_type arg)
  {
    msg_.target_position = std::move(arg);
    return Init_JackControl_Goal_speed(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Goal msg_;
};

class Init_JackControl_Goal_command
{
public:
  Init_JackControl_Goal_command()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_Goal_target_position command(::sl_vcu_all::action::JackControl_Goal::_command_type arg)
  {
    msg_.command = std::move(arg);
    return Init_JackControl_Goal_target_position(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Goal msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_Goal>()
{
  return sl_vcu_all::action::builder::Init_JackControl_Goal_command();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_Result_alarm_code
{
public:
  explicit Init_JackControl_Result_alarm_code(::sl_vcu_all::action::JackControl_Result & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_Result alarm_code(::sl_vcu_all::action::JackControl_Result::_alarm_code_type arg)
  {
    msg_.alarm_code = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Result msg_;
};

class Init_JackControl_Result_final_status
{
public:
  explicit Init_JackControl_Result_final_status(::sl_vcu_all::action::JackControl_Result & msg)
  : msg_(msg)
  {}
  Init_JackControl_Result_alarm_code final_status(::sl_vcu_all::action::JackControl_Result::_final_status_type arg)
  {
    msg_.final_status = std::move(arg);
    return Init_JackControl_Result_alarm_code(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Result msg_;
};

class Init_JackControl_Result_final_position
{
public:
  explicit Init_JackControl_Result_final_position(::sl_vcu_all::action::JackControl_Result & msg)
  : msg_(msg)
  {}
  Init_JackControl_Result_final_status final_position(::sl_vcu_all::action::JackControl_Result::_final_position_type arg)
  {
    msg_.final_position = std::move(arg);
    return Init_JackControl_Result_final_status(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Result msg_;
};

class Init_JackControl_Result_message
{
public:
  explicit Init_JackControl_Result_message(::sl_vcu_all::action::JackControl_Result & msg)
  : msg_(msg)
  {}
  Init_JackControl_Result_final_position message(::sl_vcu_all::action::JackControl_Result::_message_type arg)
  {
    msg_.message = std::move(arg);
    return Init_JackControl_Result_final_position(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Result msg_;
};

class Init_JackControl_Result_success
{
public:
  Init_JackControl_Result_success()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_Result_message success(::sl_vcu_all::action::JackControl_Result::_success_type arg)
  {
    msg_.success = std::move(arg);
    return Init_JackControl_Result_message(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Result msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_Result>()
{
  return sl_vcu_all::action::builder::Init_JackControl_Result_success();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_Feedback_progress
{
public:
  explicit Init_JackControl_Feedback_progress(::sl_vcu_all::action::JackControl_Feedback & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_Feedback progress(::sl_vcu_all::action::JackControl_Feedback::_progress_type arg)
  {
    msg_.progress = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Feedback msg_;
};

class Init_JackControl_Feedback_current_alarm
{
public:
  explicit Init_JackControl_Feedback_current_alarm(::sl_vcu_all::action::JackControl_Feedback & msg)
  : msg_(msg)
  {}
  Init_JackControl_Feedback_progress current_alarm(::sl_vcu_all::action::JackControl_Feedback::_current_alarm_type arg)
  {
    msg_.current_alarm = std::move(arg);
    return Init_JackControl_Feedback_progress(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Feedback msg_;
};

class Init_JackControl_Feedback_current_status
{
public:
  explicit Init_JackControl_Feedback_current_status(::sl_vcu_all::action::JackControl_Feedback & msg)
  : msg_(msg)
  {}
  Init_JackControl_Feedback_current_alarm current_status(::sl_vcu_all::action::JackControl_Feedback::_current_status_type arg)
  {
    msg_.current_status = std::move(arg);
    return Init_JackControl_Feedback_current_alarm(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Feedback msg_;
};

class Init_JackControl_Feedback_current_position
{
public:
  explicit Init_JackControl_Feedback_current_position(::sl_vcu_all::action::JackControl_Feedback & msg)
  : msg_(msg)
  {}
  Init_JackControl_Feedback_current_status current_position(::sl_vcu_all::action::JackControl_Feedback::_current_position_type arg)
  {
    msg_.current_position = std::move(arg);
    return Init_JackControl_Feedback_current_status(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Feedback msg_;
};

class Init_JackControl_Feedback_current_stage
{
public:
  Init_JackControl_Feedback_current_stage()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_Feedback_current_position current_stage(::sl_vcu_all::action::JackControl_Feedback::_current_stage_type arg)
  {
    msg_.current_stage = std::move(arg);
    return Init_JackControl_Feedback_current_position(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_Feedback msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_Feedback>()
{
  return sl_vcu_all::action::builder::Init_JackControl_Feedback_current_stage();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_SendGoal_Request_goal
{
public:
  explicit Init_JackControl_SendGoal_Request_goal(::sl_vcu_all::action::JackControl_SendGoal_Request & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_SendGoal_Request goal(::sl_vcu_all::action::JackControl_SendGoal_Request::_goal_type arg)
  {
    msg_.goal = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Request msg_;
};

class Init_JackControl_SendGoal_Request_goal_id
{
public:
  Init_JackControl_SendGoal_Request_goal_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_SendGoal_Request_goal goal_id(::sl_vcu_all::action::JackControl_SendGoal_Request::_goal_id_type arg)
  {
    msg_.goal_id = std::move(arg);
    return Init_JackControl_SendGoal_Request_goal(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Request msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_SendGoal_Request>()
{
  return sl_vcu_all::action::builder::Init_JackControl_SendGoal_Request_goal_id();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_SendGoal_Response_stamp
{
public:
  explicit Init_JackControl_SendGoal_Response_stamp(::sl_vcu_all::action::JackControl_SendGoal_Response & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_SendGoal_Response stamp(::sl_vcu_all::action::JackControl_SendGoal_Response::_stamp_type arg)
  {
    msg_.stamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Response msg_;
};

class Init_JackControl_SendGoal_Response_accepted
{
public:
  Init_JackControl_SendGoal_Response_accepted()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_SendGoal_Response_stamp accepted(::sl_vcu_all::action::JackControl_SendGoal_Response::_accepted_type arg)
  {
    msg_.accepted = std::move(arg);
    return Init_JackControl_SendGoal_Response_stamp(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Response msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_SendGoal_Response>()
{
  return sl_vcu_all::action::builder::Init_JackControl_SendGoal_Response_accepted();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_SendGoal_Event_response
{
public:
  explicit Init_JackControl_SendGoal_Event_response(::sl_vcu_all::action::JackControl_SendGoal_Event & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_SendGoal_Event response(::sl_vcu_all::action::JackControl_SendGoal_Event::_response_type arg)
  {
    msg_.response = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Event msg_;
};

class Init_JackControl_SendGoal_Event_request
{
public:
  explicit Init_JackControl_SendGoal_Event_request(::sl_vcu_all::action::JackControl_SendGoal_Event & msg)
  : msg_(msg)
  {}
  Init_JackControl_SendGoal_Event_response request(::sl_vcu_all::action::JackControl_SendGoal_Event::_request_type arg)
  {
    msg_.request = std::move(arg);
    return Init_JackControl_SendGoal_Event_response(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Event msg_;
};

class Init_JackControl_SendGoal_Event_info
{
public:
  Init_JackControl_SendGoal_Event_info()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_SendGoal_Event_request info(::sl_vcu_all::action::JackControl_SendGoal_Event::_info_type arg)
  {
    msg_.info = std::move(arg);
    return Init_JackControl_SendGoal_Event_request(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_SendGoal_Event msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_SendGoal_Event>()
{
  return sl_vcu_all::action::builder::Init_JackControl_SendGoal_Event_info();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_GetResult_Request_goal_id
{
public:
  Init_JackControl_GetResult_Request_goal_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::sl_vcu_all::action::JackControl_GetResult_Request goal_id(::sl_vcu_all::action::JackControl_GetResult_Request::_goal_id_type arg)
  {
    msg_.goal_id = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Request msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_GetResult_Request>()
{
  return sl_vcu_all::action::builder::Init_JackControl_GetResult_Request_goal_id();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_GetResult_Response_result
{
public:
  explicit Init_JackControl_GetResult_Response_result(::sl_vcu_all::action::JackControl_GetResult_Response & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_GetResult_Response result(::sl_vcu_all::action::JackControl_GetResult_Response::_result_type arg)
  {
    msg_.result = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Response msg_;
};

class Init_JackControl_GetResult_Response_status
{
public:
  Init_JackControl_GetResult_Response_status()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_GetResult_Response_result status(::sl_vcu_all::action::JackControl_GetResult_Response::_status_type arg)
  {
    msg_.status = std::move(arg);
    return Init_JackControl_GetResult_Response_result(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Response msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_GetResult_Response>()
{
  return sl_vcu_all::action::builder::Init_JackControl_GetResult_Response_status();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_GetResult_Event_response
{
public:
  explicit Init_JackControl_GetResult_Event_response(::sl_vcu_all::action::JackControl_GetResult_Event & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_GetResult_Event response(::sl_vcu_all::action::JackControl_GetResult_Event::_response_type arg)
  {
    msg_.response = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Event msg_;
};

class Init_JackControl_GetResult_Event_request
{
public:
  explicit Init_JackControl_GetResult_Event_request(::sl_vcu_all::action::JackControl_GetResult_Event & msg)
  : msg_(msg)
  {}
  Init_JackControl_GetResult_Event_response request(::sl_vcu_all::action::JackControl_GetResult_Event::_request_type arg)
  {
    msg_.request = std::move(arg);
    return Init_JackControl_GetResult_Event_response(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Event msg_;
};

class Init_JackControl_GetResult_Event_info
{
public:
  Init_JackControl_GetResult_Event_info()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_GetResult_Event_request info(::sl_vcu_all::action::JackControl_GetResult_Event::_info_type arg)
  {
    msg_.info = std::move(arg);
    return Init_JackControl_GetResult_Event_request(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_GetResult_Event msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_GetResult_Event>()
{
  return sl_vcu_all::action::builder::Init_JackControl_GetResult_Event_info();
}

}  // namespace sl_vcu_all


namespace sl_vcu_all
{

namespace action
{

namespace builder
{

class Init_JackControl_FeedbackMessage_feedback
{
public:
  explicit Init_JackControl_FeedbackMessage_feedback(::sl_vcu_all::action::JackControl_FeedbackMessage & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::action::JackControl_FeedbackMessage feedback(::sl_vcu_all::action::JackControl_FeedbackMessage::_feedback_type arg)
  {
    msg_.feedback = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_FeedbackMessage msg_;
};

class Init_JackControl_FeedbackMessage_goal_id
{
public:
  Init_JackControl_FeedbackMessage_goal_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackControl_FeedbackMessage_feedback goal_id(::sl_vcu_all::action::JackControl_FeedbackMessage::_goal_id_type arg)
  {
    msg_.goal_id = std::move(arg);
    return Init_JackControl_FeedbackMessage_feedback(msg_);
  }

private:
  ::sl_vcu_all::action::JackControl_FeedbackMessage msg_;
};

}  // namespace builder

}  // namespace action

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::action::JackControl_FeedbackMessage>()
{
  return sl_vcu_all::action::builder::Init_JackControl_FeedbackMessage_goal_id();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__BUILDER_HPP_
