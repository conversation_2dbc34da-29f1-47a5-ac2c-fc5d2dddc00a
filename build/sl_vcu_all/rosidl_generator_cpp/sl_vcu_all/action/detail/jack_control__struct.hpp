// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:action/JackControl.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/action/jack_control.hpp"


#ifndef SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_HPP_
#define SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_Goal __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_Goal __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_Goal_
{
  using Type = JackControl_Goal_<ContainerAllocator>;

  explicit JackControl_Goal_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->command = "";
      this->target_position = 0ul;
      this->speed = 0ul;
    }
  }

  explicit JackControl_Goal_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : command(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->command = "";
      this->target_position = 0ul;
      this->speed = 0ul;
    }
  }

  // field types and members
  using _command_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _command_type command;
  using _target_position_type =
    uint32_t;
  _target_position_type target_position;
  using _speed_type =
    uint32_t;
  _speed_type speed;

  // setters for named parameter idiom
  Type & set__command(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->command = _arg;
    return *this;
  }
  Type & set__target_position(
    const uint32_t & _arg)
  {
    this->target_position = _arg;
    return *this;
  }
  Type & set__speed(
    const uint32_t & _arg)
  {
    this->speed = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Goal
    std::shared_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Goal
    std::shared_ptr<sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_Goal_ & other) const
  {
    if (this->command != other.command) {
      return false;
    }
    if (this->target_position != other.target_position) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_Goal_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_Goal_

// alias to use template instance with default allocator
using JackControl_Goal =
  sl_vcu_all::action::JackControl_Goal_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_Result __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_Result __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_Result_
{
  using Type = JackControl_Result_<ContainerAllocator>;

  explicit JackControl_Result_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->success = false;
      this->message = "";
      this->final_position = 0ul;
      this->final_status = 0;
      this->alarm_code = 0;
    }
  }

  explicit JackControl_Result_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : message(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->success = false;
      this->message = "";
      this->final_position = 0ul;
      this->final_status = 0;
      this->alarm_code = 0;
    }
  }

  // field types and members
  using _success_type =
    bool;
  _success_type success;
  using _message_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _message_type message;
  using _final_position_type =
    uint32_t;
  _final_position_type final_position;
  using _final_status_type =
    uint16_t;
  _final_status_type final_status;
  using _alarm_code_type =
    uint16_t;
  _alarm_code_type alarm_code;

  // setters for named parameter idiom
  Type & set__success(
    const bool & _arg)
  {
    this->success = _arg;
    return *this;
  }
  Type & set__message(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->message = _arg;
    return *this;
  }
  Type & set__final_position(
    const uint32_t & _arg)
  {
    this->final_position = _arg;
    return *this;
  }
  Type & set__final_status(
    const uint16_t & _arg)
  {
    this->final_status = _arg;
    return *this;
  }
  Type & set__alarm_code(
    const uint16_t & _arg)
  {
    this->alarm_code = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_Result_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_Result_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Result_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Result_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Result
    std::shared_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Result
    std::shared_ptr<sl_vcu_all::action::JackControl_Result_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_Result_ & other) const
  {
    if (this->success != other.success) {
      return false;
    }
    if (this->message != other.message) {
      return false;
    }
    if (this->final_position != other.final_position) {
      return false;
    }
    if (this->final_status != other.final_status) {
      return false;
    }
    if (this->alarm_code != other.alarm_code) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_Result_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_Result_

// alias to use template instance with default allocator
using JackControl_Result =
  sl_vcu_all::action::JackControl_Result_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_Feedback __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_Feedback __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_Feedback_
{
  using Type = JackControl_Feedback_<ContainerAllocator>;

  explicit JackControl_Feedback_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->current_stage = "";
      this->current_position = 0ul;
      this->current_status = 0;
      this->current_alarm = 0;
      this->progress = 0.0f;
    }
  }

  explicit JackControl_Feedback_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : current_stage(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->current_stage = "";
      this->current_position = 0ul;
      this->current_status = 0;
      this->current_alarm = 0;
      this->progress = 0.0f;
    }
  }

  // field types and members
  using _current_stage_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _current_stage_type current_stage;
  using _current_position_type =
    uint32_t;
  _current_position_type current_position;
  using _current_status_type =
    uint16_t;
  _current_status_type current_status;
  using _current_alarm_type =
    uint16_t;
  _current_alarm_type current_alarm;
  using _progress_type =
    float;
  _progress_type progress;

  // setters for named parameter idiom
  Type & set__current_stage(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->current_stage = _arg;
    return *this;
  }
  Type & set__current_position(
    const uint32_t & _arg)
  {
    this->current_position = _arg;
    return *this;
  }
  Type & set__current_status(
    const uint16_t & _arg)
  {
    this->current_status = _arg;
    return *this;
  }
  Type & set__current_alarm(
    const uint16_t & _arg)
  {
    this->current_alarm = _arg;
    return *this;
  }
  Type & set__progress(
    const float & _arg)
  {
    this->progress = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Feedback
    std::shared_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_Feedback
    std::shared_ptr<sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_Feedback_ & other) const
  {
    if (this->current_stage != other.current_stage) {
      return false;
    }
    if (this->current_position != other.current_position) {
      return false;
    }
    if (this->current_status != other.current_status) {
      return false;
    }
    if (this->current_alarm != other.current_alarm) {
      return false;
    }
    if (this->progress != other.progress) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_Feedback_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_Feedback_

// alias to use template instance with default allocator
using JackControl_Feedback =
  sl_vcu_all::action::JackControl_Feedback_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'goal_id'
#include "unique_identifier_msgs/msg/detail/uuid__struct.hpp"
// Member 'goal'
#include "sl_vcu_all/action/detail/jack_control__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Request __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Request __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_SendGoal_Request_
{
  using Type = JackControl_SendGoal_Request_<ContainerAllocator>;

  explicit JackControl_SendGoal_Request_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_init),
    goal(_init)
  {
    (void)_init;
  }

  explicit JackControl_SendGoal_Request_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_alloc, _init),
    goal(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _goal_id_type =
    unique_identifier_msgs::msg::UUID_<ContainerAllocator>;
  _goal_id_type goal_id;
  using _goal_type =
    sl_vcu_all::action::JackControl_Goal_<ContainerAllocator>;
  _goal_type goal;

  // setters for named parameter idiom
  Type & set__goal_id(
    const unique_identifier_msgs::msg::UUID_<ContainerAllocator> & _arg)
  {
    this->goal_id = _arg;
    return *this;
  }
  Type & set__goal(
    const sl_vcu_all::action::JackControl_Goal_<ContainerAllocator> & _arg)
  {
    this->goal = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Request
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Request
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_SendGoal_Request_ & other) const
  {
    if (this->goal_id != other.goal_id) {
      return false;
    }
    if (this->goal != other.goal) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_SendGoal_Request_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_SendGoal_Request_

// alias to use template instance with default allocator
using JackControl_SendGoal_Request =
  sl_vcu_all::action::JackControl_SendGoal_Request_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'stamp'
#include "builtin_interfaces/msg/detail/time__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Response __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Response __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_SendGoal_Response_
{
  using Type = JackControl_SendGoal_Response_<ContainerAllocator>;

  explicit JackControl_SendGoal_Response_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : stamp(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->accepted = false;
    }
  }

  explicit JackControl_SendGoal_Response_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : stamp(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->accepted = false;
    }
  }

  // field types and members
  using _accepted_type =
    bool;
  _accepted_type accepted;
  using _stamp_type =
    builtin_interfaces::msg::Time_<ContainerAllocator>;
  _stamp_type stamp;

  // setters for named parameter idiom
  Type & set__accepted(
    const bool & _arg)
  {
    this->accepted = _arg;
    return *this;
  }
  Type & set__stamp(
    const builtin_interfaces::msg::Time_<ContainerAllocator> & _arg)
  {
    this->stamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Response
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Response
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_SendGoal_Response_ & other) const
  {
    if (this->accepted != other.accepted) {
      return false;
    }
    if (this->stamp != other.stamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_SendGoal_Response_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_SendGoal_Response_

// alias to use template instance with default allocator
using JackControl_SendGoal_Response =
  sl_vcu_all::action::JackControl_SendGoal_Response_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'info'
#include "service_msgs/msg/detail/service_event_info__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Event __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Event __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_SendGoal_Event_
{
  using Type = JackControl_SendGoal_Event_<ContainerAllocator>;

  explicit JackControl_SendGoal_Event_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : info(_init)
  {
    (void)_init;
  }

  explicit JackControl_SendGoal_Event_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : info(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _info_type =
    service_msgs::msg::ServiceEventInfo_<ContainerAllocator>;
  _info_type info;
  using _request_type =
    rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>>;
  _request_type request;
  using _response_type =
    rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>>;
  _response_type response;

  // setters for named parameter idiom
  Type & set__info(
    const service_msgs::msg::ServiceEventInfo_<ContainerAllocator> & _arg)
  {
    this->info = _arg;
    return *this;
  }
  Type & set__request(
    const rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_SendGoal_Request_<ContainerAllocator>>> & _arg)
  {
    this->request = _arg;
    return *this;
  }
  Type & set__response(
    const rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_SendGoal_Response_<ContainerAllocator>>> & _arg)
  {
    this->response = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Event
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_SendGoal_Event
    std::shared_ptr<sl_vcu_all::action::JackControl_SendGoal_Event_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_SendGoal_Event_ & other) const
  {
    if (this->info != other.info) {
      return false;
    }
    if (this->request != other.request) {
      return false;
    }
    if (this->response != other.response) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_SendGoal_Event_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_SendGoal_Event_

// alias to use template instance with default allocator
using JackControl_SendGoal_Event =
  sl_vcu_all::action::JackControl_SendGoal_Event_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all

namespace sl_vcu_all
{

namespace action
{

struct JackControl_SendGoal
{
  using Request = sl_vcu_all::action::JackControl_SendGoal_Request;
  using Response = sl_vcu_all::action::JackControl_SendGoal_Response;
  using Event = sl_vcu_all::action::JackControl_SendGoal_Event;
};

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Request __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Request __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_GetResult_Request_
{
  using Type = JackControl_GetResult_Request_<ContainerAllocator>;

  explicit JackControl_GetResult_Request_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_init)
  {
    (void)_init;
  }

  explicit JackControl_GetResult_Request_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _goal_id_type =
    unique_identifier_msgs::msg::UUID_<ContainerAllocator>;
  _goal_id_type goal_id;

  // setters for named parameter idiom
  Type & set__goal_id(
    const unique_identifier_msgs::msg::UUID_<ContainerAllocator> & _arg)
  {
    this->goal_id = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Request
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Request
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_GetResult_Request_ & other) const
  {
    if (this->goal_id != other.goal_id) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_GetResult_Request_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_GetResult_Request_

// alias to use template instance with default allocator
using JackControl_GetResult_Request =
  sl_vcu_all::action::JackControl_GetResult_Request_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'result'
// already included above
// #include "sl_vcu_all/action/detail/jack_control__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Response __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Response __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_GetResult_Response_
{
  using Type = JackControl_GetResult_Response_<ContainerAllocator>;

  explicit JackControl_GetResult_Response_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : result(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->status = 0;
    }
  }

  explicit JackControl_GetResult_Response_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : result(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->status = 0;
    }
  }

  // field types and members
  using _status_type =
    int8_t;
  _status_type status;
  using _result_type =
    sl_vcu_all::action::JackControl_Result_<ContainerAllocator>;
  _result_type result;

  // setters for named parameter idiom
  Type & set__status(
    const int8_t & _arg)
  {
    this->status = _arg;
    return *this;
  }
  Type & set__result(
    const sl_vcu_all::action::JackControl_Result_<ContainerAllocator> & _arg)
  {
    this->result = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Response
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Response
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_GetResult_Response_ & other) const
  {
    if (this->status != other.status) {
      return false;
    }
    if (this->result != other.result) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_GetResult_Response_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_GetResult_Response_

// alias to use template instance with default allocator
using JackControl_GetResult_Response =
  sl_vcu_all::action::JackControl_GetResult_Response_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'info'
// already included above
// #include "service_msgs/msg/detail/service_event_info__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Event __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Event __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_GetResult_Event_
{
  using Type = JackControl_GetResult_Event_<ContainerAllocator>;

  explicit JackControl_GetResult_Event_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : info(_init)
  {
    (void)_init;
  }

  explicit JackControl_GetResult_Event_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : info(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _info_type =
    service_msgs::msg::ServiceEventInfo_<ContainerAllocator>;
  _info_type info;
  using _request_type =
    rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>>;
  _request_type request;
  using _response_type =
    rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>>;
  _response_type response;

  // setters for named parameter idiom
  Type & set__info(
    const service_msgs::msg::ServiceEventInfo_<ContainerAllocator> & _arg)
  {
    this->info = _arg;
    return *this;
  }
  Type & set__request(
    const rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_GetResult_Request_<ContainerAllocator>>> & _arg)
  {
    this->request = _arg;
    return *this;
  }
  Type & set__response(
    const rosidl_runtime_cpp::BoundedVector<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>, 1, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::action::JackControl_GetResult_Response_<ContainerAllocator>>> & _arg)
  {
    this->response = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Event
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_GetResult_Event
    std::shared_ptr<sl_vcu_all::action::JackControl_GetResult_Event_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_GetResult_Event_ & other) const
  {
    if (this->info != other.info) {
      return false;
    }
    if (this->request != other.request) {
      return false;
    }
    if (this->response != other.response) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_GetResult_Event_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_GetResult_Event_

// alias to use template instance with default allocator
using JackControl_GetResult_Event =
  sl_vcu_all::action::JackControl_GetResult_Event_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all

namespace sl_vcu_all
{

namespace action
{

struct JackControl_GetResult
{
  using Request = sl_vcu_all::action::JackControl_GetResult_Request;
  using Response = sl_vcu_all::action::JackControl_GetResult_Response;
  using Event = sl_vcu_all::action::JackControl_GetResult_Event;
};

}  // namespace action

}  // namespace sl_vcu_all


// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__struct.hpp"
// Member 'feedback'
// already included above
// #include "sl_vcu_all/action/detail/jack_control__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__action__JackControl_FeedbackMessage __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__action__JackControl_FeedbackMessage __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace action
{

// message struct
template<class ContainerAllocator>
struct JackControl_FeedbackMessage_
{
  using Type = JackControl_FeedbackMessage_<ContainerAllocator>;

  explicit JackControl_FeedbackMessage_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_init),
    feedback(_init)
  {
    (void)_init;
  }

  explicit JackControl_FeedbackMessage_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : goal_id(_alloc, _init),
    feedback(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _goal_id_type =
    unique_identifier_msgs::msg::UUID_<ContainerAllocator>;
  _goal_id_type goal_id;
  using _feedback_type =
    sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator>;
  _feedback_type feedback;

  // setters for named parameter idiom
  Type & set__goal_id(
    const unique_identifier_msgs::msg::UUID_<ContainerAllocator> & _arg)
  {
    this->goal_id = _arg;
    return *this;
  }
  Type & set__feedback(
    const sl_vcu_all::action::JackControl_Feedback_<ContainerAllocator> & _arg)
  {
    this->feedback = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__action__JackControl_FeedbackMessage
    std::shared_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__action__JackControl_FeedbackMessage
    std::shared_ptr<sl_vcu_all::action::JackControl_FeedbackMessage_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const JackControl_FeedbackMessage_ & other) const
  {
    if (this->goal_id != other.goal_id) {
      return false;
    }
    if (this->feedback != other.feedback) {
      return false;
    }
    return true;
  }
  bool operator!=(const JackControl_FeedbackMessage_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct JackControl_FeedbackMessage_

// alias to use template instance with default allocator
using JackControl_FeedbackMessage =
  sl_vcu_all::action::JackControl_FeedbackMessage_<std::allocator<void>>;

// constant definitions

}  // namespace action

}  // namespace sl_vcu_all

#include "action_msgs/srv/cancel_goal.hpp"
#include "action_msgs/msg/goal_info.hpp"
#include "action_msgs/msg/goal_status_array.hpp"

namespace sl_vcu_all
{

namespace action
{

struct JackControl
{
  /// The goal message defined in the action definition.
  using Goal = sl_vcu_all::action::JackControl_Goal;
  /// The result message defined in the action definition.
  using Result = sl_vcu_all::action::JackControl_Result;
  /// The feedback message defined in the action definition.
  using Feedback = sl_vcu_all::action::JackControl_Feedback;

  struct Impl
  {
    /// The send_goal service using a wrapped version of the goal message as a request.
    using SendGoalService = sl_vcu_all::action::JackControl_SendGoal;
    /// The get_result service using a wrapped version of the result message as a response.
    using GetResultService = sl_vcu_all::action::JackControl_GetResult;
    /// The feedback message with generic fields which wraps the feedback message.
    using FeedbackMessage = sl_vcu_all::action::JackControl_FeedbackMessage;

    /// The generic service to cancel a goal.
    using CancelGoalService = action_msgs::srv::CancelGoal;
    /// The generic message for the status of a goal.
    using GoalStatusMessage = action_msgs::msg::GoalStatusArray;
  };
};

typedef struct JackControl JackControl;

}  // namespace action

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__ACTION__DETAIL__JACK_CONTROL__STRUCT_HPP_
