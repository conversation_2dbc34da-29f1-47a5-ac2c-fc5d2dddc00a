// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/BatteryStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/battery_status.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__BatteryStatus __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__BatteryStatus __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct BatteryStatus_
{
  using Type = BatteryStatus_<ContainerAllocator>;

  explicit BatteryStatus_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->is_auto_charging = false;
      this->is_manual_charging = false;
      this->is_discharging = false;
      this->remaining_percent = 0.0f;
    }
  }

  explicit BatteryStatus_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->is_auto_charging = false;
      this->is_manual_charging = false;
      this->is_discharging = false;
      this->remaining_percent = 0.0f;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _is_auto_charging_type =
    bool;
  _is_auto_charging_type is_auto_charging;
  using _is_manual_charging_type =
    bool;
  _is_manual_charging_type is_manual_charging;
  using _is_discharging_type =
    bool;
  _is_discharging_type is_discharging;
  using _remaining_percent_type =
    float;
  _remaining_percent_type remaining_percent;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__is_auto_charging(
    const bool & _arg)
  {
    this->is_auto_charging = _arg;
    return *this;
  }
  Type & set__is_manual_charging(
    const bool & _arg)
  {
    this->is_manual_charging = _arg;
    return *this;
  }
  Type & set__is_discharging(
    const bool & _arg)
  {
    this->is_discharging = _arg;
    return *this;
  }
  Type & set__remaining_percent(
    const float & _arg)
  {
    this->remaining_percent = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__BatteryStatus
    std::shared_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__BatteryStatus
    std::shared_ptr<sl_vcu_all::msg::BatteryStatus_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const BatteryStatus_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->is_auto_charging != other.is_auto_charging) {
      return false;
    }
    if (this->is_manual_charging != other.is_manual_charging) {
      return false;
    }
    if (this->is_discharging != other.is_discharging) {
      return false;
    }
    if (this->remaining_percent != other.remaining_percent) {
      return false;
    }
    return true;
  }
  bool operator!=(const BatteryStatus_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct BatteryStatus_

// alias to use template instance with default allocator
using BatteryStatus =
  sl_vcu_all::msg::BatteryStatus_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__STRUCT_HPP_
