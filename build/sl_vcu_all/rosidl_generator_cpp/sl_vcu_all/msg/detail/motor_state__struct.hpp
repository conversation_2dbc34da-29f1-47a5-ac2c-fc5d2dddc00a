// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/MotorState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_state.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__MotorState __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__MotorState __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct MotorState_
{
  using Type = MotorState_<ContainerAllocator>;

  explicit MotorState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->brake_release = false;
      this->emergency_stop = false;
      this->state = "";
      this->error_code = 0l;
      this->error_info = "";
    }
  }

  explicit MotorState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init),
    state(_alloc),
    error_info(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->brake_release = false;
      this->emergency_stop = false;
      this->state = "";
      this->error_code = 0l;
      this->error_info = "";
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _brake_release_type =
    bool;
  _brake_release_type brake_release;
  using _emergency_stop_type =
    bool;
  _emergency_stop_type emergency_stop;
  using _state_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _state_type state;
  using _error_code_type =
    int32_t;
  _error_code_type error_code;
  using _error_info_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _error_info_type error_info;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__brake_release(
    const bool & _arg)
  {
    this->brake_release = _arg;
    return *this;
  }
  Type & set__emergency_stop(
    const bool & _arg)
  {
    this->emergency_stop = _arg;
    return *this;
  }
  Type & set__state(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->state = _arg;
    return *this;
  }
  Type & set__error_code(
    const int32_t & _arg)
  {
    this->error_code = _arg;
    return *this;
  }
  Type & set__error_info(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->error_info = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::MotorState_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::MotorState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::MotorState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::MotorState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__MotorState
    std::shared_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__MotorState
    std::shared_ptr<sl_vcu_all::msg::MotorState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const MotorState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->brake_release != other.brake_release) {
      return false;
    }
    if (this->emergency_stop != other.emergency_stop) {
      return false;
    }
    if (this->state != other.state) {
      return false;
    }
    if (this->error_code != other.error_code) {
      return false;
    }
    if (this->error_info != other.error_info) {
      return false;
    }
    return true;
  }
  bool operator!=(const MotorState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct MotorState_

// alias to use template instance with default allocator
using MotorState =
  sl_vcu_all::msg::MotorState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__STRUCT_HPP_
