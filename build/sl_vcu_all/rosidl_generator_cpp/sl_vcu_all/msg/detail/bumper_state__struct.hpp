// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/bumper_state.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__BumperState __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__BumperState __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct BumperState_
{
  using Type = BumperState_<ContainerAllocator>;

  explicit BumperState_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->front_bumper_triggered = false;
      this->back_bumper_triggered = false;
      this->bumper_status = 0ul;
    }
  }

  explicit BumperState_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->front_bumper_triggered = false;
      this->back_bumper_triggered = false;
      this->bumper_status = 0ul;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _front_bumper_triggered_type =
    bool;
  _front_bumper_triggered_type front_bumper_triggered;
  using _back_bumper_triggered_type =
    bool;
  _back_bumper_triggered_type back_bumper_triggered;
  using _bumper_status_type =
    uint32_t;
  _bumper_status_type bumper_status;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__front_bumper_triggered(
    const bool & _arg)
  {
    this->front_bumper_triggered = _arg;
    return *this;
  }
  Type & set__back_bumper_triggered(
    const bool & _arg)
  {
    this->back_bumper_triggered = _arg;
    return *this;
  }
  Type & set__bumper_status(
    const uint32_t & _arg)
  {
    this->bumper_status = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::BumperState_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::BumperState_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::BumperState_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::BumperState_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__BumperState
    std::shared_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__BumperState
    std::shared_ptr<sl_vcu_all::msg::BumperState_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const BumperState_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->front_bumper_triggered != other.front_bumper_triggered) {
      return false;
    }
    if (this->back_bumper_triggered != other.back_bumper_triggered) {
      return false;
    }
    if (this->bumper_status != other.bumper_status) {
      return false;
    }
    return true;
  }
  bool operator!=(const BumperState_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct BumperState_

// alias to use template instance with default allocator
using BumperState =
  sl_vcu_all::msg::BumperState_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__STRUCT_HPP_
