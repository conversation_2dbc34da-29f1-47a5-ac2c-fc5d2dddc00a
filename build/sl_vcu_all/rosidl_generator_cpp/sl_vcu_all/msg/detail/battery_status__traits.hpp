// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from sl_vcu_all:msg/BatteryStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/battery_status.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__TRAITS_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "sl_vcu_all/msg/detail/battery_status__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace sl_vcu_all
{

namespace msg
{

inline void to_flow_style_yaml(
  const BatteryStatus & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: is_auto_charging
  {
    out << "is_auto_charging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_auto_charging, out);
    out << ", ";
  }

  // member: is_manual_charging
  {
    out << "is_manual_charging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_manual_charging, out);
    out << ", ";
  }

  // member: is_discharging
  {
    out << "is_discharging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_discharging, out);
    out << ", ";
  }

  // member: remaining_percent
  {
    out << "remaining_percent: ";
    rosidl_generator_traits::value_to_yaml(msg.remaining_percent, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const BatteryStatus & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: is_auto_charging
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "is_auto_charging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_auto_charging, out);
    out << "\n";
  }

  // member: is_manual_charging
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "is_manual_charging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_manual_charging, out);
    out << "\n";
  }

  // member: is_discharging
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "is_discharging: ";
    rosidl_generator_traits::value_to_yaml(msg.is_discharging, out);
    out << "\n";
  }

  // member: remaining_percent
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "remaining_percent: ";
    rosidl_generator_traits::value_to_yaml(msg.remaining_percent, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const BatteryStatus & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace sl_vcu_all

namespace rosidl_generator_traits
{

[[deprecated("use sl_vcu_all::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const sl_vcu_all::msg::BatteryStatus & msg,
  std::ostream & out, size_t indentation = 0)
{
  sl_vcu_all::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use sl_vcu_all::msg::to_yaml() instead")]]
inline std::string to_yaml(const sl_vcu_all::msg::BatteryStatus & msg)
{
  return sl_vcu_all::msg::to_yaml(msg);
}

template<>
inline const char * data_type<sl_vcu_all::msg::BatteryStatus>()
{
  return "sl_vcu_all::msg::BatteryStatus";
}

template<>
inline const char * name<sl_vcu_all::msg::BatteryStatus>()
{
  return "sl_vcu_all/msg/BatteryStatus";
}

template<>
struct has_fixed_size<sl_vcu_all::msg::BatteryStatus>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<sl_vcu_all::msg::BatteryStatus>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<sl_vcu_all::msg::BatteryStatus>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // SL_VCU_ALL__MSG__DETAIL__BATTERY_STATUS__TRAITS_HPP_
