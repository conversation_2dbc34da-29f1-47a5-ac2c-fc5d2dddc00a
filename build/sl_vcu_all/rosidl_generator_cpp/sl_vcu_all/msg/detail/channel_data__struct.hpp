// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/channel_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'parts_data'
#include "sl_vcu_all/msg/detail/part_data__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__ChannelData __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__ChannelData __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct ChannelData_
{
  using Type = ChannelData_<ContainerAllocator>;

  explicit ChannelData_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->sync_parts = false;
    }
  }

  explicit ChannelData_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->sync_parts = false;
    }
  }

  // field types and members
  using _parts_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _parts_type parts;
  using _sync_parts_type =
    bool;
  _sync_parts_type sync_parts;
  using _parts_data_type =
    std::vector<sl_vcu_all::msg::PartData_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::msg::PartData_<ContainerAllocator>>>;
  _parts_data_type parts_data;

  // setters for named parameter idiom
  Type & set__parts(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->parts = _arg;
    return *this;
  }
  Type & set__sync_parts(
    const bool & _arg)
  {
    this->sync_parts = _arg;
    return *this;
  }
  Type & set__parts_data(
    const std::vector<sl_vcu_all::msg::PartData_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<sl_vcu_all::msg::PartData_<ContainerAllocator>>> & _arg)
  {
    this->parts_data = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::ChannelData_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::ChannelData_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::ChannelData_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::ChannelData_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__ChannelData
    std::shared_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__ChannelData
    std::shared_ptr<sl_vcu_all::msg::ChannelData_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const ChannelData_ & other) const
  {
    if (this->parts != other.parts) {
      return false;
    }
    if (this->sync_parts != other.sync_parts) {
      return false;
    }
    if (this->parts_data != other.parts_data) {
      return false;
    }
    return true;
  }
  bool operator!=(const ChannelData_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct ChannelData_

// alias to use template instance with default allocator
using ChannelData =
  sl_vcu_all::msg::ChannelData_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__STRUCT_HPP_
