// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_info.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/motor_info__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_MotorInfo_alarm_code
{
public:
  explicit Init_MotorInfo_alarm_code(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::MotorInfo alarm_code(::sl_vcu_all::msg::MotorInfo::_alarm_code_type arg)
  {
    msg_.alarm_code = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_right_pos_encoder
{
public:
  explicit Init_MotorInfo_right_pos_encoder(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_alarm_code right_pos_encoder(::sl_vcu_all::msg::MotorInfo::_right_pos_encoder_type arg)
  {
    msg_.right_pos_encoder = std::move(arg);
    return Init_MotorInfo_alarm_code(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_left_pos_encoder
{
public:
  explicit Init_MotorInfo_left_pos_encoder(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_right_pos_encoder left_pos_encoder(::sl_vcu_all::msg::MotorInfo::_left_pos_encoder_type arg)
  {
    msg_.left_pos_encoder = std::move(arg);
    return Init_MotorInfo_right_pos_encoder(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_driver_temp
{
public:
  explicit Init_MotorInfo_driver_temp(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_left_pos_encoder driver_temp(::sl_vcu_all::msg::MotorInfo::_driver_temp_type arg)
  {
    msg_.driver_temp = std::move(arg);
    return Init_MotorInfo_left_pos_encoder(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_right_temp
{
public:
  explicit Init_MotorInfo_right_temp(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_driver_temp right_temp(::sl_vcu_all::msg::MotorInfo::_right_temp_type arg)
  {
    msg_.right_temp = std::move(arg);
    return Init_MotorInfo_driver_temp(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_left_temp
{
public:
  explicit Init_MotorInfo_left_temp(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_right_temp left_temp(::sl_vcu_all::msg::MotorInfo::_left_temp_type arg)
  {
    msg_.left_temp = std::move(arg);
    return Init_MotorInfo_right_temp(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_right_current
{
public:
  explicit Init_MotorInfo_right_current(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_left_temp right_current(::sl_vcu_all::msg::MotorInfo::_right_current_type arg)
  {
    msg_.right_current = std::move(arg);
    return Init_MotorInfo_left_temp(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_left_current
{
public:
  explicit Init_MotorInfo_left_current(::sl_vcu_all::msg::MotorInfo & msg)
  : msg_(msg)
  {}
  Init_MotorInfo_right_current left_current(::sl_vcu_all::msg::MotorInfo::_left_current_type arg)
  {
    msg_.left_current = std::move(arg);
    return Init_MotorInfo_right_current(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

class Init_MotorInfo_header
{
public:
  Init_MotorInfo_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_MotorInfo_left_current header(::sl_vcu_all::msg::MotorInfo::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_MotorInfo_left_current(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorInfo msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::MotorInfo>()
{
  return sl_vcu_all::msg::builder::Init_MotorInfo_header();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__BUILDER_HPP_
