// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/bumper_state.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/bumper_state__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_BumperState_bumper_status
{
public:
  explicit Init_BumperState_bumper_status(::sl_vcu_all::msg::BumperState & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::BumperState bumper_status(::sl_vcu_all::msg::BumperState::_bumper_status_type arg)
  {
    msg_.bumper_status = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::BumperState msg_;
};

class Init_BumperState_back_bumper_triggered
{
public:
  explicit Init_BumperState_back_bumper_triggered(::sl_vcu_all::msg::BumperState & msg)
  : msg_(msg)
  {}
  Init_BumperState_bumper_status back_bumper_triggered(::sl_vcu_all::msg::BumperState::_back_bumper_triggered_type arg)
  {
    msg_.back_bumper_triggered = std::move(arg);
    return Init_BumperState_bumper_status(msg_);
  }

private:
  ::sl_vcu_all::msg::BumperState msg_;
};

class Init_BumperState_front_bumper_triggered
{
public:
  explicit Init_BumperState_front_bumper_triggered(::sl_vcu_all::msg::BumperState & msg)
  : msg_(msg)
  {}
  Init_BumperState_back_bumper_triggered front_bumper_triggered(::sl_vcu_all::msg::BumperState::_front_bumper_triggered_type arg)
  {
    msg_.front_bumper_triggered = std::move(arg);
    return Init_BumperState_back_bumper_triggered(msg_);
  }

private:
  ::sl_vcu_all::msg::BumperState msg_;
};

class Init_BumperState_header
{
public:
  Init_BumperState_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_BumperState_front_bumper_triggered header(::sl_vcu_all::msg::BumperState::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_BumperState_front_bumper_triggered(msg_);
  }

private:
  ::sl_vcu_all::msg::BumperState msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::BumperState>()
{
  return sl_vcu_all::msg::builder::Init_BumperState_header();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__BUILDER_HPP_
