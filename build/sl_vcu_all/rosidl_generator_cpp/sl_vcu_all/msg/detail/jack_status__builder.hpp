// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/JackStatus.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/jack_status.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/jack_status__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_JackStatus_current_alarm
{
public:
  explicit Init_JackStatus_current_alarm(::sl_vcu_all::msg::JackStatus & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::JackStatus current_alarm(::sl_vcu_all::msg::JackStatus::_current_alarm_type arg)
  {
    msg_.current_alarm = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::JackStatus msg_;
};

class Init_JackStatus_current_status
{
public:
  explicit Init_JackStatus_current_status(::sl_vcu_all::msg::JackStatus & msg)
  : msg_(msg)
  {}
  Init_JackStatus_current_alarm current_status(::sl_vcu_all::msg::JackStatus::_current_status_type arg)
  {
    msg_.current_status = std::move(arg);
    return Init_JackStatus_current_alarm(msg_);
  }

private:
  ::sl_vcu_all::msg::JackStatus msg_;
};

class Init_JackStatus_current_position
{
public:
  explicit Init_JackStatus_current_position(::sl_vcu_all::msg::JackStatus & msg)
  : msg_(msg)
  {}
  Init_JackStatus_current_status current_position(::sl_vcu_all::msg::JackStatus::_current_position_type arg)
  {
    msg_.current_position = std::move(arg);
    return Init_JackStatus_current_status(msg_);
  }

private:
  ::sl_vcu_all::msg::JackStatus msg_;
};

class Init_JackStatus_current_stage
{
public:
  explicit Init_JackStatus_current_stage(::sl_vcu_all::msg::JackStatus & msg)
  : msg_(msg)
  {}
  Init_JackStatus_current_position current_stage(::sl_vcu_all::msg::JackStatus::_current_stage_type arg)
  {
    msg_.current_stage = std::move(arg);
    return Init_JackStatus_current_position(msg_);
  }

private:
  ::sl_vcu_all::msg::JackStatus msg_;
};

class Init_JackStatus_header
{
public:
  Init_JackStatus_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_JackStatus_current_stage header(::sl_vcu_all::msg::JackStatus::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_JackStatus_current_stage(msg_);
  }

private:
  ::sl_vcu_all::msg::JackStatus msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::JackStatus>()
{
  return sl_vcu_all::msg::builder::Init_JackStatus_header();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__JACK_STATUS__BUILDER_HPP_
