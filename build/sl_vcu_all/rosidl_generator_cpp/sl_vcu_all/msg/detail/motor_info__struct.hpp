// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_info.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__MotorInfo __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__MotorInfo __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct MotorInfo_
{
  using Type = MotorInfo_<ContainerAllocator>;

  explicit MotorInfo_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->left_current = 0.0;
      this->right_current = 0.0;
      this->left_temp = 0.0;
      this->right_temp = 0.0;
      this->driver_temp = 0.0;
      this->left_pos_encoder = 0l;
      this->right_pos_encoder = 0l;
      this->alarm_code = 0ul;
    }
  }

  explicit MotorInfo_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->left_current = 0.0;
      this->right_current = 0.0;
      this->left_temp = 0.0;
      this->right_temp = 0.0;
      this->driver_temp = 0.0;
      this->left_pos_encoder = 0l;
      this->right_pos_encoder = 0l;
      this->alarm_code = 0ul;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _left_current_type =
    double;
  _left_current_type left_current;
  using _right_current_type =
    double;
  _right_current_type right_current;
  using _left_temp_type =
    double;
  _left_temp_type left_temp;
  using _right_temp_type =
    double;
  _right_temp_type right_temp;
  using _driver_temp_type =
    double;
  _driver_temp_type driver_temp;
  using _left_pos_encoder_type =
    int32_t;
  _left_pos_encoder_type left_pos_encoder;
  using _right_pos_encoder_type =
    int32_t;
  _right_pos_encoder_type right_pos_encoder;
  using _alarm_code_type =
    uint32_t;
  _alarm_code_type alarm_code;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__left_current(
    const double & _arg)
  {
    this->left_current = _arg;
    return *this;
  }
  Type & set__right_current(
    const double & _arg)
  {
    this->right_current = _arg;
    return *this;
  }
  Type & set__left_temp(
    const double & _arg)
  {
    this->left_temp = _arg;
    return *this;
  }
  Type & set__right_temp(
    const double & _arg)
  {
    this->right_temp = _arg;
    return *this;
  }
  Type & set__driver_temp(
    const double & _arg)
  {
    this->driver_temp = _arg;
    return *this;
  }
  Type & set__left_pos_encoder(
    const int32_t & _arg)
  {
    this->left_pos_encoder = _arg;
    return *this;
  }
  Type & set__right_pos_encoder(
    const int32_t & _arg)
  {
    this->right_pos_encoder = _arg;
    return *this;
  }
  Type & set__alarm_code(
    const uint32_t & _arg)
  {
    this->alarm_code = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::MotorInfo_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::MotorInfo_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::MotorInfo_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::MotorInfo_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__MotorInfo
    std::shared_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__MotorInfo
    std::shared_ptr<sl_vcu_all::msg::MotorInfo_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const MotorInfo_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->left_current != other.left_current) {
      return false;
    }
    if (this->right_current != other.right_current) {
      return false;
    }
    if (this->left_temp != other.left_temp) {
      return false;
    }
    if (this->right_temp != other.right_temp) {
      return false;
    }
    if (this->driver_temp != other.driver_temp) {
      return false;
    }
    if (this->left_pos_encoder != other.left_pos_encoder) {
      return false;
    }
    if (this->right_pos_encoder != other.right_pos_encoder) {
      return false;
    }
    if (this->alarm_code != other.alarm_code) {
      return false;
    }
    return true;
  }
  bool operator!=(const MotorInfo_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct MotorInfo_

// alias to use template instance with default allocator
using MotorInfo =
  sl_vcu_all::msg::MotorInfo_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__STRUCT_HPP_
