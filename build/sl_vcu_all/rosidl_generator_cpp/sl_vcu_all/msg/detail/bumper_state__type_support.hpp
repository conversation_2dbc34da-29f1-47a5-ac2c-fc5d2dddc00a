// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

#ifndef SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TYPE_SUPPORT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_sl_vcu_all
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  sl_vcu_all,
  msg,
  BumperState
)();
#ifdef __cplusplus
}
#endif

#endif  // SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TYPE_SUPPORT_HPP_
