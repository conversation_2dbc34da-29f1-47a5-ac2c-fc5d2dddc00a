// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/CanFrame.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/can_frame.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/can_frame__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_CanFrame_data
{
public:
  explicit Init_CanFrame_data(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::CanFrame data(::sl_vcu_all::msg::CanFrame::_data_type arg)
  {
    msg_.data = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_dlc
{
public:
  explicit Init_CanFrame_dlc(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  Init_CanFrame_data dlc(::sl_vcu_all::msg::CanFrame::_dlc_type arg)
  {
    msg_.dlc = std::move(arg);
    return Init_CanFrame_data(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_is_error
{
public:
  explicit Init_CanFrame_is_error(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  Init_CanFrame_dlc is_error(::sl_vcu_all::msg::CanFrame::_is_error_type arg)
  {
    msg_.is_error = std::move(arg);
    return Init_CanFrame_dlc(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_is_extended
{
public:
  explicit Init_CanFrame_is_extended(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  Init_CanFrame_is_error is_extended(::sl_vcu_all::msg::CanFrame::_is_extended_type arg)
  {
    msg_.is_extended = std::move(arg);
    return Init_CanFrame_is_error(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_is_rtr
{
public:
  explicit Init_CanFrame_is_rtr(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  Init_CanFrame_is_extended is_rtr(::sl_vcu_all::msg::CanFrame::_is_rtr_type arg)
  {
    msg_.is_rtr = std::move(arg);
    return Init_CanFrame_is_extended(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_id
{
public:
  explicit Init_CanFrame_id(::sl_vcu_all::msg::CanFrame & msg)
  : msg_(msg)
  {}
  Init_CanFrame_is_rtr id(::sl_vcu_all::msg::CanFrame::_id_type arg)
  {
    msg_.id = std::move(arg);
    return Init_CanFrame_is_rtr(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

class Init_CanFrame_header
{
public:
  Init_CanFrame_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_CanFrame_id header(::sl_vcu_all::msg::CanFrame::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_CanFrame_id(msg_);
  }

private:
  ::sl_vcu_all::msg::CanFrame msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::CanFrame>()
{
  return sl_vcu_all::msg::builder::Init_CanFrame_header();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__CAN_FRAME__BUILDER_HPP_
