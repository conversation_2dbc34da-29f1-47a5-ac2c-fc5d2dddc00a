// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from sl_vcu_all:msg/MotorInfo.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_info.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__TRAITS_HPP_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "sl_vcu_all/msg/detail/motor_info__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace sl_vcu_all
{

namespace msg
{

inline void to_flow_style_yaml(
  const MotorInfo & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: left_current
  {
    out << "left_current: ";
    rosidl_generator_traits::value_to_yaml(msg.left_current, out);
    out << ", ";
  }

  // member: right_current
  {
    out << "right_current: ";
    rosidl_generator_traits::value_to_yaml(msg.right_current, out);
    out << ", ";
  }

  // member: left_temp
  {
    out << "left_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.left_temp, out);
    out << ", ";
  }

  // member: right_temp
  {
    out << "right_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.right_temp, out);
    out << ", ";
  }

  // member: driver_temp
  {
    out << "driver_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.driver_temp, out);
    out << ", ";
  }

  // member: left_pos_encoder
  {
    out << "left_pos_encoder: ";
    rosidl_generator_traits::value_to_yaml(msg.left_pos_encoder, out);
    out << ", ";
  }

  // member: right_pos_encoder
  {
    out << "right_pos_encoder: ";
    rosidl_generator_traits::value_to_yaml(msg.right_pos_encoder, out);
    out << ", ";
  }

  // member: alarm_code
  {
    out << "alarm_code: ";
    rosidl_generator_traits::value_to_yaml(msg.alarm_code, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const MotorInfo & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: left_current
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "left_current: ";
    rosidl_generator_traits::value_to_yaml(msg.left_current, out);
    out << "\n";
  }

  // member: right_current
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_current: ";
    rosidl_generator_traits::value_to_yaml(msg.right_current, out);
    out << "\n";
  }

  // member: left_temp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "left_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.left_temp, out);
    out << "\n";
  }

  // member: right_temp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.right_temp, out);
    out << "\n";
  }

  // member: driver_temp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "driver_temp: ";
    rosidl_generator_traits::value_to_yaml(msg.driver_temp, out);
    out << "\n";
  }

  // member: left_pos_encoder
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "left_pos_encoder: ";
    rosidl_generator_traits::value_to_yaml(msg.left_pos_encoder, out);
    out << "\n";
  }

  // member: right_pos_encoder
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "right_pos_encoder: ";
    rosidl_generator_traits::value_to_yaml(msg.right_pos_encoder, out);
    out << "\n";
  }

  // member: alarm_code
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "alarm_code: ";
    rosidl_generator_traits::value_to_yaml(msg.alarm_code, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const MotorInfo & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace sl_vcu_all

namespace rosidl_generator_traits
{

[[deprecated("use sl_vcu_all::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const sl_vcu_all::msg::MotorInfo & msg,
  std::ostream & out, size_t indentation = 0)
{
  sl_vcu_all::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use sl_vcu_all::msg::to_yaml() instead")]]
inline std::string to_yaml(const sl_vcu_all::msg::MotorInfo & msg)
{
  return sl_vcu_all::msg::to_yaml(msg);
}

template<>
inline const char * data_type<sl_vcu_all::msg::MotorInfo>()
{
  return "sl_vcu_all::msg::MotorInfo";
}

template<>
inline const char * name<sl_vcu_all::msg::MotorInfo>()
{
  return "sl_vcu_all/msg/MotorInfo";
}

template<>
struct has_fixed_size<sl_vcu_all::msg::MotorInfo>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<sl_vcu_all::msg::MotorInfo>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<sl_vcu_all::msg::MotorInfo>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_INFO__TRAITS_HPP_
