// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/channel_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/channel_data__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_ChannelData_parts_data
{
public:
  explicit Init_ChannelData_parts_data(::sl_vcu_all::msg::ChannelData & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::ChannelData parts_data(::sl_vcu_all::msg::ChannelData::_parts_data_type arg)
  {
    msg_.parts_data = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::ChannelData msg_;
};

class Init_ChannelData_sync_parts
{
public:
  explicit Init_ChannelData_sync_parts(::sl_vcu_all::msg::ChannelData & msg)
  : msg_(msg)
  {}
  Init_ChannelData_parts_data sync_parts(::sl_vcu_all::msg::ChannelData::_sync_parts_type arg)
  {
    msg_.sync_parts = std::move(arg);
    return Init_ChannelData_parts_data(msg_);
  }

private:
  ::sl_vcu_all::msg::ChannelData msg_;
};

class Init_ChannelData_parts
{
public:
  Init_ChannelData_parts()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_ChannelData_sync_parts parts(::sl_vcu_all::msg::ChannelData::_parts_type arg)
  {
    msg_.parts = std::move(arg);
    return Init_ChannelData_sync_parts(msg_);
  }

private:
  ::sl_vcu_all::msg::ChannelData msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::ChannelData>()
{
  return sl_vcu_all::msg::builder::Init_ChannelData_parts();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__BUILDER_HPP_
