// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/part_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__PART_DATA__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__PART_DATA__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/part_data__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_PartData_on_time_duty
{
public:
  explicit Init_PartData_on_time_duty(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::PartData on_time_duty(::sl_vcu_all::msg::PartData::_on_time_duty_type arg)
  {
    msg_.on_time_duty = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_marquee_direction
{
public:
  explicit Init_PartData_marquee_direction(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_on_time_duty marquee_direction(::sl_vcu_all::msg::PartData::_marquee_direction_type arg)
  {
    msg_.marquee_direction = std::move(arg);
    return Init_PartData_on_time_duty(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_speed
{
public:
  explicit Init_PartData_speed(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_marquee_direction speed(::sl_vcu_all::msg::PartData::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_PartData_marquee_direction(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_frequency
{
public:
  explicit Init_PartData_frequency(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_speed frequency(::sl_vcu_all::msg::PartData::_frequency_type arg)
  {
    msg_.frequency = std::move(arg);
    return Init_PartData_speed(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_blue_brightness
{
public:
  explicit Init_PartData_blue_brightness(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_frequency blue_brightness(::sl_vcu_all::msg::PartData::_blue_brightness_type arg)
  {
    msg_.blue_brightness = std::move(arg);
    return Init_PartData_frequency(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_red_brightness
{
public:
  explicit Init_PartData_red_brightness(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_blue_brightness red_brightness(::sl_vcu_all::msg::PartData::_red_brightness_type arg)
  {
    msg_.red_brightness = std::move(arg);
    return Init_PartData_blue_brightness(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_green_brightness
{
public:
  explicit Init_PartData_green_brightness(::sl_vcu_all::msg::PartData & msg)
  : msg_(msg)
  {}
  Init_PartData_red_brightness green_brightness(::sl_vcu_all::msg::PartData::_green_brightness_type arg)
  {
    msg_.green_brightness = std::move(arg);
    return Init_PartData_red_brightness(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

class Init_PartData_mode
{
public:
  Init_PartData_mode()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_PartData_green_brightness mode(::sl_vcu_all::msg::PartData::_mode_type arg)
  {
    msg_.mode = std::move(arg);
    return Init_PartData_green_brightness(msg_);
  }

private:
  ::sl_vcu_all::msg::PartData msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::PartData>()
{
  return sl_vcu_all::msg::builder::Init_PartData_mode();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__PART_DATA__BUILDER_HPP_
