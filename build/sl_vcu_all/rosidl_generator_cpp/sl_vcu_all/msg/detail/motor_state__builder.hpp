// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from sl_vcu_all:msg/MotorState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/motor_state.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__BUILDER_HPP_
#define SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "sl_vcu_all/msg/detail/motor_state__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace sl_vcu_all
{

namespace msg
{

namespace builder
{

class Init_MotorState_error_info
{
public:
  explicit Init_MotorState_error_info(::sl_vcu_all::msg::MotorState & msg)
  : msg_(msg)
  {}
  ::sl_vcu_all::msg::MotorState error_info(::sl_vcu_all::msg::MotorState::_error_info_type arg)
  {
    msg_.error_info = std::move(arg);
    return std::move(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

class Init_MotorState_error_code
{
public:
  explicit Init_MotorState_error_code(::sl_vcu_all::msg::MotorState & msg)
  : msg_(msg)
  {}
  Init_MotorState_error_info error_code(::sl_vcu_all::msg::MotorState::_error_code_type arg)
  {
    msg_.error_code = std::move(arg);
    return Init_MotorState_error_info(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

class Init_MotorState_state
{
public:
  explicit Init_MotorState_state(::sl_vcu_all::msg::MotorState & msg)
  : msg_(msg)
  {}
  Init_MotorState_error_code state(::sl_vcu_all::msg::MotorState::_state_type arg)
  {
    msg_.state = std::move(arg);
    return Init_MotorState_error_code(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

class Init_MotorState_emergency_stop
{
public:
  explicit Init_MotorState_emergency_stop(::sl_vcu_all::msg::MotorState & msg)
  : msg_(msg)
  {}
  Init_MotorState_state emergency_stop(::sl_vcu_all::msg::MotorState::_emergency_stop_type arg)
  {
    msg_.emergency_stop = std::move(arg);
    return Init_MotorState_state(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

class Init_MotorState_brake_release
{
public:
  explicit Init_MotorState_brake_release(::sl_vcu_all::msg::MotorState & msg)
  : msg_(msg)
  {}
  Init_MotorState_emergency_stop brake_release(::sl_vcu_all::msg::MotorState::_brake_release_type arg)
  {
    msg_.brake_release = std::move(arg);
    return Init_MotorState_emergency_stop(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

class Init_MotorState_header
{
public:
  Init_MotorState_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_MotorState_brake_release header(::sl_vcu_all::msg::MotorState::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_MotorState_brake_release(msg_);
  }

private:
  ::sl_vcu_all::msg::MotorState msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::sl_vcu_all::msg::MotorState>()
{
  return sl_vcu_all::msg::builder::Init_MotorState_header();
}

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__MOTOR_STATE__BUILDER_HPP_
