// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from sl_vcu_all:msg/ChannelData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/channel_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__TRAITS_HPP_
#define SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "sl_vcu_all/msg/detail/channel_data__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'parts_data'
#include "sl_vcu_all/msg/detail/part_data__traits.hpp"

namespace sl_vcu_all
{

namespace msg
{

inline void to_flow_style_yaml(
  const ChannelData & msg,
  std::ostream & out)
{
  out << "{";
  // member: parts
  {
    if (msg.parts.size() == 0) {
      out << "parts: []";
    } else {
      out << "parts: [";
      size_t pending_items = msg.parts.size();
      for (auto item : msg.parts) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: sync_parts
  {
    out << "sync_parts: ";
    rosidl_generator_traits::value_to_yaml(msg.sync_parts, out);
    out << ", ";
  }

  // member: parts_data
  {
    if (msg.parts_data.size() == 0) {
      out << "parts_data: []";
    } else {
      out << "parts_data: [";
      size_t pending_items = msg.parts_data.size();
      for (auto item : msg.parts_data) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const ChannelData & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: parts
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.parts.size() == 0) {
      out << "parts: []\n";
    } else {
      out << "parts:\n";
      for (auto item : msg.parts) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: sync_parts
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sync_parts: ";
    rosidl_generator_traits::value_to_yaml(msg.sync_parts, out);
    out << "\n";
  }

  // member: parts_data
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.parts_data.size() == 0) {
      out << "parts_data: []\n";
    } else {
      out << "parts_data:\n";
      for (auto item : msg.parts_data) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const ChannelData & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace sl_vcu_all

namespace rosidl_generator_traits
{

[[deprecated("use sl_vcu_all::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const sl_vcu_all::msg::ChannelData & msg,
  std::ostream & out, size_t indentation = 0)
{
  sl_vcu_all::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use sl_vcu_all::msg::to_yaml() instead")]]
inline std::string to_yaml(const sl_vcu_all::msg::ChannelData & msg)
{
  return sl_vcu_all::msg::to_yaml(msg);
}

template<>
inline const char * data_type<sl_vcu_all::msg::ChannelData>()
{
  return "sl_vcu_all::msg::ChannelData";
}

template<>
inline const char * name<sl_vcu_all::msg::ChannelData>()
{
  return "sl_vcu_all/msg/ChannelData";
}

template<>
struct has_fixed_size<sl_vcu_all::msg::ChannelData>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<sl_vcu_all::msg::ChannelData>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<sl_vcu_all::msg::ChannelData>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // SL_VCU_ALL__MSG__DETAIL__CHANNEL_DATA__TRAITS_HPP_
