// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/part_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__PART_DATA__TRAITS_HPP_
#define SL_VCU_ALL__MSG__DETAIL__PART_DATA__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "sl_vcu_all/msg/detail/part_data__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace sl_vcu_all
{

namespace msg
{

inline void to_flow_style_yaml(
  const PartData & msg,
  std::ostream & out)
{
  out << "{";
  // member: mode
  {
    out << "mode: ";
    rosidl_generator_traits::value_to_yaml(msg.mode, out);
    out << ", ";
  }

  // member: green_brightness
  {
    out << "green_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.green_brightness, out);
    out << ", ";
  }

  // member: red_brightness
  {
    out << "red_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.red_brightness, out);
    out << ", ";
  }

  // member: blue_brightness
  {
    out << "blue_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.blue_brightness, out);
    out << ", ";
  }

  // member: frequency
  {
    out << "frequency: ";
    rosidl_generator_traits::value_to_yaml(msg.frequency, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: marquee_direction
  {
    out << "marquee_direction: ";
    rosidl_generator_traits::value_to_yaml(msg.marquee_direction, out);
    out << ", ";
  }

  // member: on_time_duty
  {
    out << "on_time_duty: ";
    rosidl_generator_traits::value_to_yaml(msg.on_time_duty, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PartData & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: mode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "mode: ";
    rosidl_generator_traits::value_to_yaml(msg.mode, out);
    out << "\n";
  }

  // member: green_brightness
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "green_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.green_brightness, out);
    out << "\n";
  }

  // member: red_brightness
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "red_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.red_brightness, out);
    out << "\n";
  }

  // member: blue_brightness
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "blue_brightness: ";
    rosidl_generator_traits::value_to_yaml(msg.blue_brightness, out);
    out << "\n";
  }

  // member: frequency
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "frequency: ";
    rosidl_generator_traits::value_to_yaml(msg.frequency, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: marquee_direction
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "marquee_direction: ";
    rosidl_generator_traits::value_to_yaml(msg.marquee_direction, out);
    out << "\n";
  }

  // member: on_time_duty
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "on_time_duty: ";
    rosidl_generator_traits::value_to_yaml(msg.on_time_duty, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PartData & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace sl_vcu_all

namespace rosidl_generator_traits
{

[[deprecated("use sl_vcu_all::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const sl_vcu_all::msg::PartData & msg,
  std::ostream & out, size_t indentation = 0)
{
  sl_vcu_all::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use sl_vcu_all::msg::to_yaml() instead")]]
inline std::string to_yaml(const sl_vcu_all::msg::PartData & msg)
{
  return sl_vcu_all::msg::to_yaml(msg);
}

template<>
inline const char * data_type<sl_vcu_all::msg::PartData>()
{
  return "sl_vcu_all::msg::PartData";
}

template<>
inline const char * name<sl_vcu_all::msg::PartData>()
{
  return "sl_vcu_all/msg/PartData";
}

template<>
struct has_fixed_size<sl_vcu_all::msg::PartData>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<sl_vcu_all::msg::PartData>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<sl_vcu_all::msg::PartData>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // SL_VCU_ALL__MSG__DETAIL__PART_DATA__TRAITS_HPP_
