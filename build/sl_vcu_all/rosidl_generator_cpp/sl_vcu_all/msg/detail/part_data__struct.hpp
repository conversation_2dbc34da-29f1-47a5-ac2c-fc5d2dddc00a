// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from sl_vcu_all:msg/PartData.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/part_data.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_HPP_
#define SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__sl_vcu_all__msg__PartData __attribute__((deprecated))
#else
# define DEPRECATED__sl_vcu_all__msg__PartData __declspec(deprecated)
#endif

namespace sl_vcu_all
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct PartData_
{
  using Type = PartData_<ContainerAllocator>;

  explicit PartData_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mode = "";
      this->green_brightness = 0;
      this->red_brightness = 0;
      this->blue_brightness = 0;
      this->frequency = 0.0f;
      this->speed = 0.0f;
      this->marquee_direction = false;
      this->on_time_duty = 0.0f;
    }
  }

  explicit PartData_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : mode(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->mode = "";
      this->green_brightness = 0;
      this->red_brightness = 0;
      this->blue_brightness = 0;
      this->frequency = 0.0f;
      this->speed = 0.0f;
      this->marquee_direction = false;
      this->on_time_duty = 0.0f;
    }
  }

  // field types and members
  using _mode_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _mode_type mode;
  using _green_brightness_type =
    uint8_t;
  _green_brightness_type green_brightness;
  using _red_brightness_type =
    uint8_t;
  _red_brightness_type red_brightness;
  using _blue_brightness_type =
    uint8_t;
  _blue_brightness_type blue_brightness;
  using _frequency_type =
    float;
  _frequency_type frequency;
  using _speed_type =
    float;
  _speed_type speed;
  using _marquee_direction_type =
    bool;
  _marquee_direction_type marquee_direction;
  using _on_time_duty_type =
    float;
  _on_time_duty_type on_time_duty;

  // setters for named parameter idiom
  Type & set__mode(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->mode = _arg;
    return *this;
  }
  Type & set__green_brightness(
    const uint8_t & _arg)
  {
    this->green_brightness = _arg;
    return *this;
  }
  Type & set__red_brightness(
    const uint8_t & _arg)
  {
    this->red_brightness = _arg;
    return *this;
  }
  Type & set__blue_brightness(
    const uint8_t & _arg)
  {
    this->blue_brightness = _arg;
    return *this;
  }
  Type & set__frequency(
    const float & _arg)
  {
    this->frequency = _arg;
    return *this;
  }
  Type & set__speed(
    const float & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__marquee_direction(
    const bool & _arg)
  {
    this->marquee_direction = _arg;
    return *this;
  }
  Type & set__on_time_duty(
    const float & _arg)
  {
    this->on_time_duty = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    sl_vcu_all::msg::PartData_<ContainerAllocator> *;
  using ConstRawPtr =
    const sl_vcu_all::msg::PartData_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::PartData_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      sl_vcu_all::msg::PartData_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__sl_vcu_all__msg__PartData
    std::shared_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__sl_vcu_all__msg__PartData
    std::shared_ptr<sl_vcu_all::msg::PartData_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PartData_ & other) const
  {
    if (this->mode != other.mode) {
      return false;
    }
    if (this->green_brightness != other.green_brightness) {
      return false;
    }
    if (this->red_brightness != other.red_brightness) {
      return false;
    }
    if (this->blue_brightness != other.blue_brightness) {
      return false;
    }
    if (this->frequency != other.frequency) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->marquee_direction != other.marquee_direction) {
      return false;
    }
    if (this->on_time_duty != other.on_time_duty) {
      return false;
    }
    return true;
  }
  bool operator!=(const PartData_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PartData_

// alias to use template instance with default allocator
using PartData =
  sl_vcu_all::msg::PartData_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace sl_vcu_all

#endif  // SL_VCU_ALL__MSG__DETAIL__PART_DATA__STRUCT_HPP_
