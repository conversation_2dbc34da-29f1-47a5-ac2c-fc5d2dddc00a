// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from sl_vcu_all:msg/BumperState.idl
// generated code does not contain a copyright notice

// IWYU pragma: private, include "sl_vcu_all/msg/bumper_state.hpp"


#ifndef SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TRAITS_HPP_
#define SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "sl_vcu_all/msg/detail/bumper_state__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace sl_vcu_all
{

namespace msg
{

inline void to_flow_style_yaml(
  const BumperState & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: front_bumper_triggered
  {
    out << "front_bumper_triggered: ";
    rosidl_generator_traits::value_to_yaml(msg.front_bumper_triggered, out);
    out << ", ";
  }

  // member: back_bumper_triggered
  {
    out << "back_bumper_triggered: ";
    rosidl_generator_traits::value_to_yaml(msg.back_bumper_triggered, out);
    out << ", ";
  }

  // member: bumper_status
  {
    out << "bumper_status: ";
    rosidl_generator_traits::value_to_yaml(msg.bumper_status, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const BumperState & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: front_bumper_triggered
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "front_bumper_triggered: ";
    rosidl_generator_traits::value_to_yaml(msg.front_bumper_triggered, out);
    out << "\n";
  }

  // member: back_bumper_triggered
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "back_bumper_triggered: ";
    rosidl_generator_traits::value_to_yaml(msg.back_bumper_triggered, out);
    out << "\n";
  }

  // member: bumper_status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "bumper_status: ";
    rosidl_generator_traits::value_to_yaml(msg.bumper_status, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const BumperState & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace sl_vcu_all

namespace rosidl_generator_traits
{

[[deprecated("use sl_vcu_all::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const sl_vcu_all::msg::BumperState & msg,
  std::ostream & out, size_t indentation = 0)
{
  sl_vcu_all::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use sl_vcu_all::msg::to_yaml() instead")]]
inline std::string to_yaml(const sl_vcu_all::msg::BumperState & msg)
{
  return sl_vcu_all::msg::to_yaml(msg);
}

template<>
inline const char * data_type<sl_vcu_all::msg::BumperState>()
{
  return "sl_vcu_all::msg::BumperState";
}

template<>
inline const char * name<sl_vcu_all::msg::BumperState>()
{
  return "sl_vcu_all/msg/BumperState";
}

template<>
struct has_fixed_size<sl_vcu_all::msg::BumperState>
  : std::integral_constant<bool, has_fixed_size<std_msgs::msg::Header>::value> {};

template<>
struct has_bounded_size<sl_vcu_all::msg::BumperState>
  : std::integral_constant<bool, has_bounded_size<std_msgs::msg::Header>::value> {};

template<>
struct is_message<sl_vcu_all::msg::BumperState>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // SL_VCU_ALL__MSG__DETAIL__BUMPER_STATE__TRAITS_HPP_
