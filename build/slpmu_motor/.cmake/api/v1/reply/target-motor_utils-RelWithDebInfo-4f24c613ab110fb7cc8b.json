{"artifacts": [{"path": "motor_utils"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_compile_options"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 50, "parent": 0}, {"command": 1, "file": 0, "line": 54, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 16, "parent": 0}, {"command": 3, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O2 -g -DNDEBUG"}, {"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Wextra"}, {"backtrace": 5, "fragment": "-Wpedantic"}], "includes": [{"backtrace": 3, "path": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/include"}, {"backtrace": 3, "isSystem": true, "path": "/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "slpmu_motor::@6890427a1f51a3e7e1df"}], "id": "motor_utils::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor"}}, "link": {"commandFragments": [{"fragment": "-O2 -g -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "libslpmu_motor.a", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a", "role": "libraries"}], "language": "CXX"}, "name": "motor_utils", "nameOnDisk": "motor_utils", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "bin/motor_utils.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}