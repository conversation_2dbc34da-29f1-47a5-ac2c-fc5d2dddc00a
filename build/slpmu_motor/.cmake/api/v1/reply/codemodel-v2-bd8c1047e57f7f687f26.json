{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-7fb479eeb760a63e6dfd.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "slpmu_motor", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "motor_utils::@6890427a1f51a3e7e1df", "jsonFile": "target-motor_utils-RelWithDebInfo-4f24c613ab110fb7cc8b.json", "name": "motor_utils", "projectIndex": 0}, {"directoryIndex": 0, "id": "slpmu_motor::@6890427a1f51a3e7e1df", "jsonFile": "target-slpmu_motor-RelWithDebInfo-bfbfe3cd995a562979af.json", "name": "slpmu_motor", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor", "source": "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor"}, "version": {"major": 2, "minor": 6}}