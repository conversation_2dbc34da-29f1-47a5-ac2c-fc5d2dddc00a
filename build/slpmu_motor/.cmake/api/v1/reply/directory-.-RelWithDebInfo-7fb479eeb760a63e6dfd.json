{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 18, "parent": 0}, {"command": 0, "file": 0, "line": 25, "parent": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}, {"command": 0, "file": 0, "line": 43, "parent": 0}, {"command": 0, "file": 0, "line": 54, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libslpmu_motor.a"], "targetId": "slpmu_motor::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "share/slpmu_motor/cmake", "exportName": "slpmu_motor_targets", "exportTargets": [{"id": "slpmu_motor::@6890427a1f51a3e7e1df", "index": 1}], "paths": ["CMakeFiles/Export/83be7698cc51d7645f3c318bfb194c3d/slpmu_motor_targets.cmake"], "type": "export"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/slpmu_motor", "paths": ["/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/cmake/slpmu_motor/slpmu_motor-config.cmake"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "bin", "paths": ["motor_utils"], "targetId": "motor_utils::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}], "paths": {"build": ".", "source": "."}}