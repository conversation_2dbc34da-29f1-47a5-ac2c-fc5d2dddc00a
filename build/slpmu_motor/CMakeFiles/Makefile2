# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/slpmu_motor.dir/all
all: CMakeFiles/motor_utils.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/slpmu_motor.dir/clean
clean: CMakeFiles/motor_utils.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/slpmu_motor.dir

# All Build rule for target.
CMakeFiles/slpmu_motor.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=3,4 "Built target slpmu_motor"
.PHONY : CMakeFiles/slpmu_motor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/slpmu_motor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/slpmu_motor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles 0
.PHONY : CMakeFiles/slpmu_motor.dir/rule

# Convenience name for target.
slpmu_motor: CMakeFiles/slpmu_motor.dir/rule
.PHONY : slpmu_motor

# clean rule for target.
CMakeFiles/slpmu_motor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/clean
.PHONY : CMakeFiles/slpmu_motor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/motor_utils.dir

# All Build rule for target.
CMakeFiles/motor_utils.dir/all: CMakeFiles/slpmu_motor.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=1,2 "Built target motor_utils"
.PHONY : CMakeFiles/motor_utils.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/motor_utils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/motor_utils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles 0
.PHONY : CMakeFiles/motor_utils.dir/rule

# Convenience name for target.
motor_utils: CMakeFiles/motor_utils.dir/rule
.PHONY : motor_utils

# clean rule for target.
CMakeFiles/motor_utils.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/clean
.PHONY : CMakeFiles/motor_utils.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

