# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor

# Include any dependencies generated for this target.
include CMakeFiles/slpmu_motor.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/slpmu_motor.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/slpmu_motor.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/slpmu_motor.dir/flags.make

CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o: CMakeFiles/slpmu_motor.dir/flags.make
CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/src/motor_controller_can.cpp
CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o: CMakeFiles/slpmu_motor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o -MF CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o.d -o CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/src/motor_controller_can.cpp

CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/src/motor_controller_can.cpp > CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.i

CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/src/motor_controller_can.cpp -o CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.s

# Object files for target slpmu_motor
slpmu_motor_OBJECTS = \
"CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o"

# External object files for target slpmu_motor
slpmu_motor_EXTERNAL_OBJECTS =

libslpmu_motor.a: CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o
libslpmu_motor.a: CMakeFiles/slpmu_motor.dir/build.make
libslpmu_motor.a: CMakeFiles/slpmu_motor.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libslpmu_motor.a"
	$(CMAKE_COMMAND) -P CMakeFiles/slpmu_motor.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/slpmu_motor.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/slpmu_motor.dir/build: libslpmu_motor.a
.PHONY : CMakeFiles/slpmu_motor.dir/build

CMakeFiles/slpmu_motor.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/slpmu_motor.dir/cmake_clean.cmake
.PHONY : CMakeFiles/slpmu_motor.dir/clean

CMakeFiles/slpmu_motor.dir/depend:
	cd /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/slpmu_motor.dir/depend

