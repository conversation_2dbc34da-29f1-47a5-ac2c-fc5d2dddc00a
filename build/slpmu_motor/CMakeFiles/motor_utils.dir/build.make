# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor

# Include any dependencies generated for this target.
include CMakeFiles/motor_utils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/motor_utils.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/motor_utils.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/motor_utils.dir/flags.make

CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o: CMakeFiles/motor_utils.dir/flags.make
CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o: /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/bin/motor_utils.cpp
CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o: CMakeFiles/motor_utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o -MF CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o.d -o CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o -c /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/bin/motor_utils.cpp

CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/bin/motor_utils.cpp > CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.i

CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor/bin/motor_utils.cpp -o CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.s

# Object files for target motor_utils
motor_utils_OBJECTS = \
"CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o"

# External object files for target motor_utils
motor_utils_EXTERNAL_OBJECTS =

motor_utils: CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o
motor_utils: CMakeFiles/motor_utils.dir/build.make
motor_utils: libslpmu_motor.a
motor_utils: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a
motor_utils: CMakeFiles/motor_utils.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable motor_utils"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/motor_utils.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/motor_utils.dir/build: motor_utils
.PHONY : CMakeFiles/motor_utils.dir/build

CMakeFiles/motor_utils.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/motor_utils.dir/cmake_clean.cmake
.PHONY : CMakeFiles/motor_utils.dir/clean

CMakeFiles/motor_utils.dir/depend:
	cd /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles/motor_utils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/motor_utils.dir/depend

