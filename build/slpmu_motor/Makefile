# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named slpmu_motor

# Build rule for target.
slpmu_motor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 slpmu_motor
.PHONY : slpmu_motor

# fast build rule for target.
slpmu_motor/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/build
.PHONY : slpmu_motor/fast

#=============================================================================
# Target rules for targets named motor_utils

# Build rule for target.
motor_utils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 motor_utils
.PHONY : motor_utils

# fast build rule for target.
motor_utils/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/build
.PHONY : motor_utils/fast

bin/motor_utils.o: bin/motor_utils.cpp.o
.PHONY : bin/motor_utils.o

# target to build an object file
bin/motor_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.o
.PHONY : bin/motor_utils.cpp.o

bin/motor_utils.i: bin/motor_utils.cpp.i
.PHONY : bin/motor_utils.i

# target to preprocess a source file
bin/motor_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.i
.PHONY : bin/motor_utils.cpp.i

bin/motor_utils.s: bin/motor_utils.cpp.s
.PHONY : bin/motor_utils.s

# target to generate assembly for a file
bin/motor_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/motor_utils.dir/build.make CMakeFiles/motor_utils.dir/bin/motor_utils.cpp.s
.PHONY : bin/motor_utils.cpp.s

src/motor_controller_can.o: src/motor_controller_can.cpp.o
.PHONY : src/motor_controller_can.o

# target to build an object file
src/motor_controller_can.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.o
.PHONY : src/motor_controller_can.cpp.o

src/motor_controller_can.i: src/motor_controller_can.cpp.i
.PHONY : src/motor_controller_can.i

# target to preprocess a source file
src/motor_controller_can.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.i
.PHONY : src/motor_controller_can.cpp.i

src/motor_controller_can.s: src/motor_controller_can.cpp.s
.PHONY : src/motor_controller_can.s

# target to generate assembly for a file
src/motor_controller_can.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slpmu_motor.dir/build.make CMakeFiles/slpmu_motor.dir/src/motor_controller_can.cpp.s
.PHONY : src/motor_controller_can.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... motor_utils"
	@echo "... slpmu_motor"
	@echo "... bin/motor_utils.o"
	@echo "... bin/motor_utils.i"
	@echo "... bin/motor_utils.s"
	@echo "... src/motor_controller_can.o"
	@echo "... src/motor_controller_can.i"
	@echo "... src/motor_controller_can.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

